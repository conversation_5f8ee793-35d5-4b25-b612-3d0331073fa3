import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonButton,
  IonIcon,
  IonItem,
  IonLabel,
  IonSelect,
  IonSelectOption,
  IonGrid,
  IonRow,
  IonCol,
  IonImg,
  IonSpinner,
  IonText,
  IonChip,
  IonProgressBar,
  IonCheckbox,
  AlertController,
  ToastController,
  LoadingController,
  ActionSheetController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import {
  camera,
  images,
  location,
  leaf,
  flower,
  analytics,
  checkmarkCircle,
  closeCircle,
  refresh,
  save,
  share
} from 'ionicons/icons';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Geolocation } from '@capacitor/geolocation';
import { Subscription } from 'rxjs';
import {
  IdentificationRequest,
  IdentificationResult,
  LocationInfo,
  DEFAULT_FLORAS
} from '../../../models';
import { CreateObservationRequest, PlantPart } from '../../../models/observation.model';
import { IdentificationService } from '../../../core/services/identification.service';
import { AuthService } from '../../../core/services/auth.service';
import { ObservationService } from '../../../core/services/observation.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-identify',
  templateUrl: './identify.page.html',
  styleUrls: ['./identify.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonCard,
    IonCardContent,
    IonCardHeader,
    IonCardTitle,
    IonButton,
    IonIcon,
    IonItem,
    IonLabel,
    IonSelect,
    IonSelectOption,
    IonImg,
    IonSpinner,
    IonChip,
    IonProgressBar,
    IonCheckbox
  ]
})
export class IdentifyPage implements OnInit, OnDestroy {
  // App configuration
  isDemoMode = environment.demoMode;

  // State management
  currentStep: 'capture' | 'configure' | 'processing' | 'results' = 'capture';

  // Image data
  capturedImage: string | null = null;
  imageFile: File | null = null;

  // Configuration
  selectedPlantPart: PlantPart = 'leaf';
  selectedFloraRegion: string = 'global';
  useLocation: boolean = true;
  currentLocation: LocationInfo | null = null;

  // Results
  identificationResult: IdentificationResult | null = null;

  // UI state
  isProcessing = false;
  processingProgress = 0;
  errorMessage: string | null = null;

  // Available options
  plantParts: { value: PlantPart; label: string; icon: string }[] = [
    { value: 'leaf', label: 'Leaf', icon: 'leaf' },
    { value: 'flower', label: 'Flower', icon: 'flower' },
    { value: 'fruit', label: 'Fruit', icon: 'nutrition' },
    { value: 'bark', label: 'Bark', icon: 'grid' },
    { value: 'habit', label: 'Whole Plant', icon: 'tree' },
    { value: 'other', label: 'Other', icon: 'help' }
  ];

  floraRegions = DEFAULT_FLORAS;

  private subscriptions: Subscription[] = [];

  constructor(
    private identificationService: IdentificationService,
    private authService: AuthService,
    private observationService: ObservationService,
    private router: Router,
    private alertController: AlertController,
    private toastController: ToastController,
    private loadingController: LoadingController,
    private actionSheetController: ActionSheetController
  ) {
    addIcons({
      camera,
      images,
      location,
      leaf,
      flower,
      analytics,
      checkmarkCircle,
      closeCircle,
      refresh,
      save,
      share
    });
  }

  ngOnInit() {
    this.getCurrentLocation();

    // Subscribe to identification service loading state
    const loadingSub = this.identificationService.loadingState$.subscribe(state => {
      this.isProcessing = state.loading;
      this.errorMessage = state.error;
    });

    this.subscriptions.push(loadingSub);
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Camera and image capture methods
  async presentImageSourceActionSheet() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Select Image Source',
      buttons: [
        {
          text: 'Camera',
          icon: 'camera',
          handler: () => {
            this.captureImage(CameraSource.Camera);
          }
        },
        {
          text: 'Photo Library',
          icon: 'images',
          handler: () => {
            this.captureImage(CameraSource.Photos);
          }
        },
        {
          text: 'Cancel',
          icon: 'close',
          role: 'cancel'
        }
      ]
    });
    await actionSheet.present();
  }

  async captureImage(source: CameraSource) {
    try {
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: source
      });

      if (image.dataUrl) {
        this.capturedImage = image.dataUrl;

        // Convert data URL to File object
        const response = await fetch(image.dataUrl);
        const blob = await response.blob();
        this.imageFile = new File([blob], 'plant-image.jpg', { type: 'image/jpeg' });

        this.currentStep = 'configure';
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      this.showErrorToast('Failed to capture image. Please try again.');
    }
  }

  // Location methods
  async getCurrentLocation() {
    if (!this.useLocation) return;

    try {
      const coordinates = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 10000
      });

      this.currentLocation = {
        coordinates: {
          lat: coordinates.coords.latitude,
          lng: coordinates.coords.longitude
        },
        accuracy: coordinates.coords.accuracy || undefined,
        altitude: coordinates.coords.altitude || undefined
      };
    } catch (error) {
      console.error('Error getting location:', error);
      this.useLocation = false;
    }
  }

  // Plant identification process
  async startIdentification() {
    if (!this.imageFile) {
      this.showErrorToast('Please capture an image first.');
      return;
    }

    this.currentStep = 'processing';
    this.processingProgress = 0;

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        if (this.processingProgress < 90) {
          this.processingProgress += 10;
        }
      }, 500);

      const request: IdentificationRequest = {
        imageFile: this.imageFile,
        plantPart: this.selectedPlantPart,
        floraRegion: this.selectedFloraRegion,
        location: this.currentLocation?.coordinates,
        modifiers: {
          includeRelatedSpecies: true,
          maxResults: 5,
          minConfidence: 0.1,
          useLocationContext: this.useLocation && !!this.currentLocation
        }
      };

      this.identificationResult = await this.identificationService.identifyPlant(request);

      clearInterval(progressInterval);
      this.processingProgress = 100;

      setTimeout(() => {
        this.currentStep = 'results';
      }, 500);

    } catch (error: any) {
      this.showErrorToast(error.message || 'Identification failed. Please try again.');
      this.currentStep = 'configure';
    }
  }

  // Navigation and utility methods
  resetIdentification() {
    this.currentStep = 'capture';
    this.capturedImage = null;
    this.imageFile = null;
    this.identificationResult = null;
    this.errorMessage = null;
    this.processingProgress = 0;
  }

  retakePhoto() {
    this.capturedImage = null;
    this.imageFile = null;
    this.currentStep = 'capture';
  }

  goBackToConfiguration() {
    this.currentStep = 'configure';
  }

  async saveAsObservation() {
    if (!this.identificationResult || !this.identificationResult.species.length) {
      this.showErrorToast('No identification results to save.');
      return;
    }

    if (!this.imageFile || !this.currentLocation) {
      this.showErrorToast('Missing required data for saving observation.');
      return;
    }

    const alert = await this.alertController.create({
      header: 'Save Observation',
      message: 'Save this plant identification as an observation?',
      inputs: [
        {
          name: 'notes',
          type: 'textarea',
          placeholder: 'Add notes about this observation (optional)'
        },
        {
          name: 'isPublic',
          type: 'checkbox',
          label: 'Make this observation public',
          value: 'public',
          checked: true
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Save',
          handler: async (data) => {
            await this.performSaveObservation(data.notes, data.includes('public'));
          }
        }
      ]
    });

    await alert.present();
  }

  private async performSaveObservation(notes: string, isPublic: boolean) {
    const loading = await this.loadingController.create({
      message: 'Saving observation...',
      spinner: 'crescent'
    });
    await loading.present();

    try {
      if (!this.identificationResult || !this.imageFile || !this.currentLocation) {
        throw new Error('Missing required data');
      }

      // Create observation from identification result
      const observationRequest: CreateObservationRequest = {
        species: this.identificationResult.species[0], // Use top result
        image: this.imageFile,
        location: this.currentLocation,
        plantPart: this.selectedPlantPart as PlantPart,
        floraRegion: this.selectedFloraRegion,
        isPublic,
        notes: notes || undefined,
        tags: []
      };

      const observationId = await this.observationService.createObservation(observationRequest);

      await loading.dismiss();

      const successToast = await this.toastController.create({
        message: 'Observation saved successfully!',
        duration: 3000,
        position: 'bottom',
        color: 'success',
        cssClass: 'success-toast',
        buttons: [
          {
            text: 'View',
            side: 'end',
            handler: () => {
              this.router.navigate(['/observation', observationId]);
            }
          },
          {
            text: 'Dismiss',
            role: 'cancel',
            side: 'end'
          }
        ]
      });
      await successToast.present();

      // Reset the identification flow
      this.resetIdentification();

    } catch (error: any) {
      await loading.dismiss();
      this.showErrorToast('Failed to save observation: ' + (error.message || 'Unknown error'));
    }
  }

  async shareResults() {
    if (!this.identificationResult) {
      this.showErrorToast('No results to share.');
      return;
    }

    // Implement sharing functionality
    this.showSuccessToast('Feature coming soon: Share results');
  }

  // UI helper methods
  getConfidenceColor(confidence: number): string {
    if (confidence >= 80) return 'success';
    if (confidence >= 60) return 'warning';
    return 'danger';
  }

  getConfidenceText(confidence: number): string {
    if (confidence >= 80) return 'High confidence';
    if (confidence >= 60) return 'Medium confidence';
    return 'Low confidence';
  }

  private async showErrorToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 4000,
      position: 'bottom',
      color: 'danger',
      cssClass: 'error-toast',
      buttons: [{
        text: 'Dismiss',
        role: 'cancel',
        side: 'end'
      }]
    });
    await toast.present();
  }

  private async showSuccessToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      position: 'bottom',
      color: 'success',
      cssClass: 'success-toast',
      buttons: [{
        text: 'Dismiss',
        role: 'cancel',
        side: 'end'
      }]
    });
    await toast.present();
  }
}
