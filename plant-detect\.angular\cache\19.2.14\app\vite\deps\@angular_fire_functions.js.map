{"version": 3, "sources": ["../../../../../../node_modules/@firebase/functions/dist/esm/index.esm2017.js", "../../../../../../node_modules/rxfire/functions/index.esm.js", "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-functions.mjs"], "sourcesContent": ["import { _isFirebaseServerApp, _registerComponent, registerVersion, _getProvider, getApp } from '@firebase/app';\nimport { FirebaseError, isCloudWorkstation, pingServer, updateEmulatorBanner, getModularInstance, getDefaultEmulatorHostnameAndPort } from '@firebase/util';\nimport { Component } from '@firebase/component';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\nfunction mapValues(\n// { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no, f) {\n  const result = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nfunction encode(data) {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nfunction decode(json) {\n  if (json == null) {\n    return json;\n  }\n  if (json['@type']) {\n    switch (json['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE:\n        {\n          // Technically, this could work return a valid number for malformed\n          // data if there was a number followed by garbage. But it's just not\n          // worth all the extra code to detect that case.\n          const value = Number(json['value']);\n          if (isNaN(value)) {\n            throw new Error('Data cannot be decoded from JSON: ' + json);\n          }\n          return value;\n        }\n      default:\n        {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Type constant for Firebase Functions.\n */\nconst FUNCTIONS_TYPE = 'functions';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nclass FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n  /**\n   * A standard error code that will be returned to the client. This also\n   * determines the HTTP status code of the response, as defined in code.proto.\n   */\n  code, message,\n  /**\n   * Additional details to be converted to JSON and included in the error response.\n   */\n  details) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n    this.details = details;\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status) {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n  }\n  return 'unknown';\n}\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nfunction _errorForResponse(status, bodyJSON) {\n  let code = codeForHTTPStatus(status);\n  // Start with reasonable defaults from the status code.\n  let description = code;\n  let details = undefined;\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n  return new FunctionsError(code, description, details);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nclass ContextProvider {\n  constructor(app, authProvider, messagingProvider, appCheckProvider) {\n    this.app = app;\n    this.auth = null;\n    this.messaging = null;\n    this.appCheck = null;\n    this.serverAppAppCheckToken = null;\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.auth = authProvider.getImmediate({\n      optional: true\n    });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n    if (!this.auth) {\n      authProvider.get().then(auth => this.auth = auth, () => {\n        /* get() never rejects */\n      });\n    }\n    if (!this.messaging) {\n      messagingProvider.get().then(messaging => this.messaging = messaging, () => {\n        /* get() never rejects */\n      });\n    }\n    if (!this.appCheck) {\n      appCheckProvider === null || appCheckProvider === void 0 ? void 0 : appCheckProvider.get().then(appCheck => this.appCheck = appCheck, () => {\n        /* get() never rejects */\n      });\n    }\n  }\n  async getAuthToken() {\n    if (!this.auth) {\n      return undefined;\n    }\n    try {\n      const token = await this.auth.getToken();\n      return token === null || token === void 0 ? void 0 : token.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n  async getMessagingToken() {\n    if (!this.messaging || !('Notification' in self) || Notification.permission !== 'granted') {\n      return undefined;\n    }\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n  async getAppCheckToken(limitedUseAppCheckTokens) {\n    if (this.serverAppAppCheckToken) {\n      return this.serverAppAppCheckToken;\n    }\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens ? await this.appCheck.getLimitedUseToken() : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n  async getContext(limitedUseAppCheckTokens) {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return {\n      authToken,\n      messagingToken,\n      appCheckToken\n    };\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_REGION = 'us-central1';\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis) {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nclass FunctionsService {\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(app, authProvider, messagingProvider, appCheckProvider, regionOrCustomDomain = DEFAULT_REGION, fetchImpl = (...args) => fetch(...args)) {\n    this.app = app;\n    this.fetchImpl = fetchImpl;\n    this.emulatorOrigin = null;\n    this.contextProvider = new ContextProvider(app, authProvider, messagingProvider, appCheckProvider);\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain = url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n  _delete() {\n    return this.deleteService();\n  }\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name) {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nfunction connectFunctionsEmulator$1(functionsInstance, host, port) {\n  const useSsl = isCloudWorkstation(host);\n  functionsInstance.emulatorOrigin = `http${useSsl ? 's' : ''}://${host}:${port}`;\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(functionsInstance.emulatorOrigin);\n    updateEmulatorBanner('Functions', true);\n  }\n}\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nfunction httpsCallable$1(functionsInstance, name, options) {\n  const callable = data => {\n    return call(functionsInstance, name, data, options || {});\n  };\n  callable.stream = (data, options) => {\n    return stream(functionsInstance, name, data, options);\n  };\n  return callable;\n}\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nfunction httpsCallableFromURL$1(functionsInstance, url, options) {\n  const callable = data => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n  callable.stream = (data, options) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable;\n}\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(url, body, headers, fetchImpl) {\n  headers['Content-Type'] = 'application/json';\n  let response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(functionsInstance, options) {\n  const headers = {};\n  const context = await functionsInstance.contextProvider.getContext(options.limitedUseAppCheckTokens);\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(functionsInstance, name, data, options) {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(functionsInstance, url, data, options) {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = {\n    data\n  };\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([postJSON(url, body, headers, functionsInstance.fetchImpl), failAfterHandle.promise, functionsInstance.cancelAllRequests]);\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError('cancelled', 'Firebase Functions instance was deleted.');\n  }\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n  return {\n    data: decodedData\n  };\n}\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(functionsInstance, name, data, options) {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(functionsInstance, url, data, options) {\n  var _a;\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = {\n    data\n  };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n  let response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options === null || options === void 0 ? void 0 : options.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver;\n  let resultRejecter;\n  const resultPromise = new Promise((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  (_a = options === null || options === void 0 ? void 0 : options.signal) === null || _a === void 0 ? void 0 : _a.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body.getReader();\n  const rstream = createResponseStream(reader, resultResolver, resultRejecter, options === null || options === void 0 ? void 0 : options.signal);\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const {\n              value,\n              done\n            } = await rreader.read();\n            return {\n              value: value,\n              done\n            };\n          },\n          async return() {\n            await rreader.cancel();\n            return {\n              done: true,\n              value: undefined\n            };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(reader, resultResolver, resultRejecter, signal) {\n  const processLine = (line, controller) => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump() {\n        if (signal === null || signal === void 0 ? void 0 : signal.aborted) {\n          const error = new FunctionsError('cancelled', 'Request was cancelled');\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const {\n            value,\n            done\n          } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal === null || signal === void 0 ? void 0 : signal.aborted) {\n            const error = new FunctionsError('cancelled', 'Request was cancelled');\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, {\n            stream: true\n          });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError = error instanceof FunctionsError ? error : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\nconst name = \"@firebase/functions\";\nconst version = \"0.12.8\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst AUTH_INTERNAL_NAME = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME = 'app-check-internal';\nconst MESSAGING_INTERNAL_NAME = 'messaging-internal';\nfunction registerFunctions(variant) {\n  const factory = (container, {\n    instanceIdentifier: regionOrCustomDomain\n  }) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(app, authProvider, messagingProvider, appCheckProvider, regionOrCustomDomain);\n  };\n  _registerComponent(new Component(FUNCTIONS_TYPE, factory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, 'esm2017');\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nfunction getFunctions(app = getApp(), regionOrCustomDomain = DEFAULT_REGION) {\n  // Dependencies\n  const functionsProvider = _getProvider(getModularInstance(app), FUNCTIONS_TYPE);\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nfunction connectFunctionsEmulator(functionsInstance, host, port) {\n  connectFunctionsEmulator$1(getModularInstance(functionsInstance), host, port);\n}\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nfunction httpsCallable(functionsInstance, name, options) {\n  return httpsCallable$1(getModularInstance(functionsInstance), name, options);\n}\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nfunction httpsCallableFromURL(functionsInstance, url, options) {\n  return httpsCallableFromURL$1(getModularInstance(functionsInstance), url, options);\n}\n\n/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\nregisterFunctions();\nexport { FunctionsError, connectFunctionsEmulator, getFunctions, httpsCallable, httpsCallableFromURL };\n", "import { httpsCallable as httpsCallable$1 } from 'firebase/functions';\nimport { from } from 'rxjs';\nimport { map } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction httpsCallable(functions, name, options) {\n  var callable = httpsCallable$1(functions, name, options);\n  return function (data) {\n    return from(callable(data)).pipe(map(function (r) {\n      return r.data;\n    }));\n  };\n}\nexport { httpsCallable };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, Optional, NgModule, makeEnvironmentProviders, NgZone, Injector } from '@angular/core';\nimport { ɵgetAllInstancesOf as _getAllInstancesOf, ɵgetDefaultInstanceOf as _getDefaultInstanceOf, VERSION, ɵAngularFireSchedulers as _AngularFireSchedulers, ɵzoneWrap as _zoneWrap } from '@angular/fire';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { AppCheckInstances } from '@angular/fire/app-check';\nimport { AuthInstances } from '@angular/fire/auth';\nimport { registerVersion } from 'firebase/app';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport { httpsCallable as httpsCallable$1 } from 'rxfire/functions';\nimport { connectFunctionsEmulator as connectFunctionsEmulator$1, getFunctions as getFunctions$1, httpsCallable as httpsCallable$2, httpsCallableFromURL as httpsCallableFromURL$1 } from 'firebase/functions';\nexport * from 'firebase/functions';\nclass Functions {\n  constructor(functions) {\n    return functions;\n  }\n}\nconst FUNCTIONS_PROVIDER_NAME = 'functions';\nclass FunctionsInstances {\n  constructor() {\n    return _getAllInstancesOf(FUNCTIONS_PROVIDER_NAME);\n  }\n}\nconst functionInstance$ = timer(0, 300).pipe(concatMap(() => from(_getAllInstancesOf(FUNCTIONS_PROVIDER_NAME))), distinct());\nconst PROVIDED_FUNCTIONS_INSTANCES = new InjectionToken('angularfire2.functions-instances');\nfunction defaultFunctionsInstanceFactory(provided, defaultApp) {\n  const defaultAuth = _getDefaultInstanceOf(FUNCTIONS_PROVIDER_NAME, provided, defaultApp);\n  return defaultAuth && new Functions(defaultAuth);\n}\nfunction functionsInstanceFactory(fn) {\n  return (zone, injector) => {\n    const functions = zone.runOutsideAngular(() => fn(injector));\n    return new Functions(functions);\n  };\n}\nconst FUNCTIONS_INSTANCES_PROVIDER = {\n  provide: FunctionsInstances,\n  deps: [[new Optional(), PROVIDED_FUNCTIONS_INSTANCES]]\n};\nconst DEFAULT_FUNCTIONS_INSTANCE_PROVIDER = {\n  provide: Functions,\n  useFactory: defaultFunctionsInstanceFactory,\n  deps: [[new Optional(), PROVIDED_FUNCTIONS_INSTANCES], FirebaseApp]\n};\nclass FunctionsModule {\n  constructor() {\n    registerVersion('angularfire', VERSION.full, 'fn');\n  }\n  static ɵfac = function FunctionsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FunctionsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FunctionsModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [DEFAULT_FUNCTIONS_INSTANCE_PROVIDER, FUNCTIONS_INSTANCES_PROVIDER]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FunctionsModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_FUNCTIONS_INSTANCE_PROVIDER, FUNCTIONS_INSTANCES_PROVIDER]\n    }]\n  }], () => [], null);\n})();\nfunction provideFunctions(fn, ...deps) {\n  registerVersion('angularfire', VERSION.full, 'fn');\n  return makeEnvironmentProviders([DEFAULT_FUNCTIONS_INSTANCE_PROVIDER, FUNCTIONS_INSTANCES_PROVIDER, {\n    provide: PROVIDED_FUNCTIONS_INSTANCES,\n    useFactory: functionsInstanceFactory(fn),\n    multi: true,\n    deps: [NgZone, Injector, _AngularFireSchedulers, FirebaseApps,\n    // Defensively load Auth first, if provided\n    [new Optional(), AuthInstances], [new Optional(), AppCheckInstances], ...deps]\n  }]);\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst httpsCallableData = _zoneWrap(httpsCallable$1, true);\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst connectFunctionsEmulator = _zoneWrap(connectFunctionsEmulator$1, true);\nconst getFunctions = _zoneWrap(getFunctions$1, true);\nconst httpsCallable = _zoneWrap(httpsCallable$2, true);\nconst httpsCallableFromURL = _zoneWrap(httpsCallableFromURL$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Functions, FunctionsInstances, FunctionsModule, connectFunctionsEmulator, functionInstance$, getFunctions, httpsCallable, httpsCallableData, httpsCallableFromURL, provideFunctions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,YAAY;AAClB,IAAM,qBAAqB;AAC3B,SAAS,UAGT,GAAG,GAAG;AACJ,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,GAAG;AACnB,QAAI,EAAE,eAAe,GAAG,GAAG;AACzB,aAAO,GAAG,IAAI,EAAE,EAAE,GAAG,CAAC;AAAA,IACxB;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,OAAO,MAAM;AACpB,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,QAAQ;AAC1B,WAAO,KAAK,QAAQ;AAAA,EACtB;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,IAAI,GAAG;AAG9C,WAAO;AAAA,EACT;AACA,MAAI,SAAS,QAAQ,SAAS,OAAO;AACnC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,mBAAmB;AAC9D,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,MAAM;AACxB,WAAO,KAAK,YAAY;AAAA,EAC1B;AACA,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,KAAK,IAAI,OAAK,OAAO,CAAC,CAAC;AAAA,EAChC;AACA,MAAI,OAAO,SAAS,cAAc,OAAO,SAAS,UAAU;AAC1D,WAAO,UAAU,MAAM,OAAK,OAAO,CAAC,CAAC;AAAA,EACvC;AAEA,QAAM,IAAI,MAAM,qCAAqC,IAAI;AAC3D;AAOA,SAAS,OAAO,MAAM;AACpB,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AACA,MAAI,KAAK,OAAO,GAAG;AACjB,YAAQ,KAAK,OAAO,GAAG;AAAA,MACrB,KAAK;AAAA;AAAA,MAEL,KAAK,oBACH;AAIE,cAAM,QAAQ,OAAO,KAAK,OAAO,CAAC;AAClC,YAAI,MAAM,KAAK,GAAG;AAChB,gBAAM,IAAI,MAAM,uCAAuC,IAAI;AAAA,QAC7D;AACA,eAAO;AAAA,MACT;AAAA,MACF,SACE;AACE,cAAM,IAAI,MAAM,uCAAuC,IAAI;AAAA,MAC7D;AAAA,IACJ;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,KAAK,IAAI,OAAK,OAAO,CAAC,CAAC;AAAA,EAChC;AACA,MAAI,OAAO,SAAS,cAAc,OAAO,SAAS,UAAU;AAC1D,WAAO,UAAU,MAAM,OAAK,OAAO,CAAC,CAAC;AAAA,EACvC;AAEA,SAAO;AACT;AAqBA,IAAM,iBAAiB;AAyBvB,IAAM,eAAe;AAAA,EACnB,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,eAAe;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AACb;AAQA,IAAM,iBAAN,MAAM,wBAAuB,cAAc;AAAA;AAAA;AAAA;AAAA,EAIzC,YAKA,MAAM,SAIN,SAAS;AACP,UAAM,GAAG,cAAc,IAAI,IAAI,IAAI,WAAW,EAAE;AAChD,SAAK,UAAU;AAGf,WAAO,eAAe,MAAM,gBAAe,SAAS;AAAA,EACtD;AACF;AASA,SAAS,kBAAkB,QAAQ;AAEjC,MAAI,UAAU,OAAO,SAAS,KAAK;AACjC,WAAO;AAAA,EACT;AACA,UAAQ,QAAQ;AAAA,IACd,KAAK;AAEH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,EACX;AACA,SAAO;AACT;AAIA,SAAS,kBAAkB,QAAQ,UAAU;AAC3C,MAAI,OAAO,kBAAkB,MAAM;AAEnC,MAAI,cAAc;AAClB,MAAI,UAAU;AAEd,MAAI;AACF,UAAM,YAAY,YAAY,SAAS;AACvC,QAAI,WAAW;AACb,YAAMA,UAAS,UAAU;AACzB,UAAI,OAAOA,YAAW,UAAU;AAC9B,YAAI,CAAC,aAAaA,OAAM,GAAG;AAEzB,iBAAO,IAAI,eAAe,YAAY,UAAU;AAAA,QAClD;AACA,eAAO,aAAaA,OAAM;AAG1B,sBAAcA;AAAA,MAChB;AACA,YAAM,UAAU,UAAU;AAC1B,UAAI,OAAO,YAAY,UAAU;AAC/B,sBAAc;AAAA,MAChB;AACA,gBAAU,UAAU;AACpB,UAAI,YAAY,QAAW;AACzB,kBAAU,OAAO,OAAO;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,MAAI,SAAS,MAAM;AAIjB,WAAO;AAAA,EACT;AACA,SAAO,IAAI,eAAe,MAAM,aAAa,OAAO;AACtD;AAsBA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,KAAK,cAAc,mBAAmB,kBAAkB;AAClE,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,yBAAyB;AAC9B,QAAI,qBAAqB,GAAG,KAAK,IAAI,SAAS,eAAe;AAC3D,WAAK,yBAAyB,IAAI,SAAS;AAAA,IAC7C;AACA,SAAK,OAAO,aAAa,aAAa;AAAA,MACpC,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,YAAY,kBAAkB,aAAa;AAAA,MAC9C,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,CAAC,KAAK,MAAM;AACd,mBAAa,IAAI,EAAE,KAAK,UAAQ,KAAK,OAAO,MAAM,MAAM;AAAA,MAExD,CAAC;AAAA,IACH;AACA,QAAI,CAAC,KAAK,WAAW;AACnB,wBAAkB,IAAI,EAAE,KAAK,eAAa,KAAK,YAAY,WAAW,MAAM;AAAA,MAE5E,CAAC;AAAA,IACH;AACA,QAAI,CAAC,KAAK,UAAU;AAClB,2BAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,IAAI,EAAE,KAAK,cAAY,KAAK,WAAW,UAAU,MAAM;AAAA,MAE5I,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACM,eAAe;AAAA;AACnB,UAAI,CAAC,KAAK,MAAM;AACd,eAAO;AAAA,MACT;AACA,UAAI;AACF,cAAM,QAAQ,MAAM,KAAK,KAAK,SAAS;AACvC,eAAO,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,MAC7D,SAAS,GAAG;AAEV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA,EACM,oBAAoB;AAAA;AACxB,UAAI,CAAC,KAAK,aAAa,EAAE,kBAAkB,SAAS,aAAa,eAAe,WAAW;AACzF,eAAO;AAAA,MACT;AACA,UAAI;AACF,eAAO,MAAM,KAAK,UAAU,SAAS;AAAA,MACvC,SAAS,GAAG;AAIV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA,EACM,iBAAiB,0BAA0B;AAAA;AAC/C,UAAI,KAAK,wBAAwB;AAC/B,eAAO,KAAK;AAAA,MACd;AACA,UAAI,KAAK,UAAU;AACjB,cAAM,SAAS,2BAA2B,MAAM,KAAK,SAAS,mBAAmB,IAAI,MAAM,KAAK,SAAS,SAAS;AAClH,YAAI,OAAO,OAAO;AAIhB,iBAAO;AAAA,QACT;AACA,eAAO,OAAO;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACM,WAAW,0BAA0B;AAAA;AACzC,YAAM,YAAY,MAAM,KAAK,aAAa;AAC1C,YAAM,iBAAiB,MAAM,KAAK,kBAAkB;AACpD,YAAM,gBAAgB,MAAM,KAAK,iBAAiB,wBAAwB;AAC1E,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AACF;AAkBA,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AAOvB,SAAS,UAAU,QAAQ;AAIzB,MAAIC,SAAQ;AACZ,SAAO;AAAA,IACL,SAAS,IAAI,QAAQ,CAAC,GAAG,WAAW;AAClC,MAAAA,SAAQ,WAAW,MAAM;AACvB,eAAO,IAAI,eAAe,qBAAqB,mBAAmB,CAAC;AAAA,MACrE,GAAG,MAAM;AAAA,IACX,CAAC;AAAA,IACD,QAAQ,MAAM;AACZ,UAAIA,QAAO;AACT,qBAAaA,MAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AAKA,IAAM,mBAAN,MAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,YAAY,KAAK,cAAc,mBAAmB,kBAAkB,uBAAuB,gBAAgB,YAAY,IAAI,SAAS,MAAM,GAAG,IAAI,GAAG;AAClJ,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,IAAI,gBAAgB,KAAK,cAAc,mBAAmB,gBAAgB;AAEjG,SAAK,oBAAoB,IAAI,QAAQ,aAAW;AAC9C,WAAK,gBAAgB,MAAM;AACzB,eAAO,QAAQ,QAAQ,QAAQ,CAAC;AAAA,MAClC;AAAA,IACF,CAAC;AAED,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,oBAAoB;AACxC,WAAK,eAAe,IAAI,UAAU,IAAI,aAAa,MAAM,KAAK,IAAI;AAClE,WAAK,SAAS;AAAA,IAChB,SAAS,GAAG;AACV,WAAK,eAAe;AACpB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,UAAU;AACR,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAKC,OAAM;AACT,UAAM,YAAY,KAAK,IAAI,QAAQ;AACnC,QAAI,KAAK,mBAAmB,MAAM;AAChC,YAAM,SAAS,KAAK;AACpB,aAAO,GAAG,MAAM,IAAI,SAAS,IAAI,KAAK,MAAM,IAAIA,KAAI;AAAA,IACtD;AACA,QAAI,KAAK,iBAAiB,MAAM;AAC9B,aAAO,GAAG,KAAK,YAAY,IAAIA,KAAI;AAAA,IACrC;AACA,WAAO,WAAW,KAAK,MAAM,IAAI,SAAS,uBAAuBA,KAAI;AAAA,EACvE;AACF;AAUA,SAAS,2BAA2B,mBAAmB,MAAM,MAAM;AACjE,QAAM,SAAS,mBAAmB,IAAI;AACtC,oBAAkB,iBAAiB,OAAO,SAAS,MAAM,EAAE,MAAM,IAAI,IAAI,IAAI;AAE7E,MAAI,QAAQ;AACV,SAAK,WAAW,kBAAkB,cAAc;AAChD,yBAAqB,aAAa,IAAI;AAAA,EACxC;AACF;AAMA,SAAS,gBAAgB,mBAAmBA,OAAM,SAAS;AACzD,QAAM,WAAW,UAAQ;AACvB,WAAO,KAAK,mBAAmBA,OAAM,MAAM,WAAW,CAAC,CAAC;AAAA,EAC1D;AACA,WAAS,SAAS,CAAC,MAAMC,aAAY;AACnC,WAAO,OAAO,mBAAmBD,OAAM,MAAMC,QAAO;AAAA,EACtD;AACA,SAAO;AACT;AAMA,SAAS,uBAAuB,mBAAmB,KAAK,SAAS;AAC/D,QAAM,WAAW,UAAQ;AACvB,WAAO,UAAU,mBAAmB,KAAK,MAAM,WAAW,CAAC,CAAC;AAAA,EAC9D;AACA,WAAS,SAAS,CAAC,MAAMA,aAAY;AACnC,WAAO,YAAY,mBAAmB,KAAK,MAAMA,YAAW,CAAC,CAAC;AAAA,EAChE;AACA,SAAO;AACT;AAQA,SAAe,SAAS,KAAK,MAAM,SAAS,WAAW;AAAA;AACrD,YAAQ,cAAc,IAAI;AAC1B,QAAI;AACJ,QAAI;AACF,iBAAW,MAAM,UAAU,KAAK;AAAA,QAC9B,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU,IAAI;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH,SAAS,GAAG;AAKV,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,OAAO;AACX,QAAI;AACF,aAAO,MAAM,SAAS,KAAK;AAAA,IAC7B,SAAS,GAAG;AAAA,IAEZ;AACA,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA;AAOA,SAAe,gBAAgB,mBAAmB,SAAS;AAAA;AACzD,UAAM,UAAU,CAAC;AACjB,UAAM,UAAU,MAAM,kBAAkB,gBAAgB,WAAW,QAAQ,wBAAwB;AACnG,QAAI,QAAQ,WAAW;AACrB,cAAQ,eAAe,IAAI,YAAY,QAAQ;AAAA,IACjD;AACA,QAAI,QAAQ,gBAAgB;AAC1B,cAAQ,4BAA4B,IAAI,QAAQ;AAAA,IAClD;AACA,QAAI,QAAQ,kBAAkB,MAAM;AAClC,cAAQ,qBAAqB,IAAI,QAAQ;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA;AAMA,SAAS,KAAK,mBAAmBD,OAAM,MAAM,SAAS;AACpD,QAAM,MAAM,kBAAkB,KAAKA,KAAI;AACvC,SAAO,UAAU,mBAAmB,KAAK,MAAM,OAAO;AACxD;AAMA,SAAe,UAAU,mBAAmB,KAAK,MAAM,SAAS;AAAA;AAE9D,WAAO,OAAO,IAAI;AAClB,UAAM,OAAO;AAAA,MACX;AAAA,IACF;AAEA,UAAM,UAAU,MAAM,gBAAgB,mBAAmB,OAAO;AAEhE,UAAM,UAAU,QAAQ,WAAW;AACnC,UAAM,kBAAkB,UAAU,OAAO;AACzC,UAAM,WAAW,MAAM,QAAQ,KAAK,CAAC,SAAS,KAAK,MAAM,SAAS,kBAAkB,SAAS,GAAG,gBAAgB,SAAS,kBAAkB,iBAAiB,CAAC;AAE7J,oBAAgB,OAAO;AAEvB,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,eAAe,aAAa,0CAA0C;AAAA,IAClF;AAEA,UAAM,QAAQ,kBAAkB,SAAS,QAAQ,SAAS,IAAI;AAC9D,QAAI,OAAO;AACT,YAAM;AAAA,IACR;AACA,QAAI,CAAC,SAAS,MAAM;AAClB,YAAM,IAAI,eAAe,YAAY,oCAAoC;AAAA,IAC3E;AACA,QAAI,eAAe,SAAS,KAAK;AAGjC,QAAI,OAAO,iBAAiB,aAAa;AACvC,qBAAe,SAAS,KAAK;AAAA,IAC/B;AACA,QAAI,OAAO,iBAAiB,aAAa;AAEvC,YAAM,IAAI,eAAe,YAAY,iCAAiC;AAAA,IACxE;AAEA,UAAM,cAAc,OAAO,YAAY;AACvC,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAOA,SAAS,OAAO,mBAAmBA,OAAM,MAAM,SAAS;AACtD,QAAM,MAAM,kBAAkB,KAAKA,KAAI;AACvC,SAAO,YAAY,mBAAmB,KAAK,MAAM,WAAW,CAAC,CAAC;AAChE;AAOA,SAAe,YAAY,mBAAmB,KAAK,MAAM,SAAS;AAAA;AAChE,QAAI;AAEJ,WAAO,OAAO,IAAI;AAClB,UAAM,OAAO;AAAA,MACX;AAAA,IACF;AAGA,UAAM,UAAU,MAAM,gBAAgB,mBAAmB,OAAO;AAChE,YAAQ,cAAc,IAAI;AAC1B,YAAQ,QAAQ,IAAI;AACpB,QAAI;AACJ,QAAI;AACF,iBAAW,MAAM,kBAAkB,UAAU,KAAK;AAAA,QAChD,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU,IAAI;AAAA,QACzB;AAAA,QACA,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,MACpE,CAAC;AAAA,IACH,SAAS,GAAG;AACV,UAAI,aAAa,SAAS,EAAE,SAAS,cAAc;AACjD,cAAME,SAAQ,IAAI,eAAe,aAAa,wBAAwB;AACtE,eAAO;AAAA,UACL,MAAM,QAAQ,OAAOA,MAAK;AAAA,UAC1B,QAAQ;AAAA,YACN,CAAC,OAAO,aAAa,IAAI;AACvB,qBAAO;AAAA,gBACL,OAAO;AACL,yBAAO,QAAQ,OAAOA,MAAK;AAAA,gBAC7B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAKA,YAAM,QAAQ,kBAAkB,GAAG,IAAI;AACvC,aAAO;AAAA,QACL,MAAM,QAAQ,OAAO,KAAK;AAAA;AAAA,QAE1B,QAAQ;AAAA,UACN,CAAC,OAAO,aAAa,IAAI;AACvB,mBAAO;AAAA,cACL,OAAO;AACL,uBAAO,QAAQ,OAAO,KAAK;AAAA,cAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACJ,UAAM,gBAAgB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrD,uBAAiB;AACjB,uBAAiB;AAAA,IACnB,CAAC;AACD,KAAC,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,SAAS,MAAM;AAC9I,YAAM,QAAQ,IAAI,eAAe,aAAa,wBAAwB;AACtE,qBAAe,KAAK;AAAA,IACtB,CAAC;AACD,UAAM,SAAS,SAAS,KAAK,UAAU;AACvC,UAAM,UAAU,qBAAqB,QAAQ,gBAAgB,gBAAgB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,MAAM;AAC7I,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,CAAC,OAAO,aAAa,IAAI;AACvB,gBAAM,UAAU,QAAQ,UAAU;AAClC,iBAAO;AAAA,YACC,OAAO;AAAA;AACX,sBAAM;AAAA,kBACJ;AAAA,kBACA;AAAA,gBACF,IAAI,MAAM,QAAQ,KAAK;AACvB,uBAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA;AAAA,YACM,SAAS;AAAA;AACb,sBAAM,QAAQ,OAAO;AACrB,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,OAAO;AAAA,gBACT;AAAA,cACF;AAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAgBA,SAAS,qBAAqB,QAAQ,gBAAgB,gBAAgB,QAAQ;AAC5E,QAAM,cAAc,CAAC,MAAM,eAAe;AACxC,UAAM,QAAQ,KAAK,MAAM,cAAc;AAEvC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI;AACF,YAAM,WAAW,KAAK,MAAM,IAAI;AAChC,UAAI,YAAY,UAAU;AACxB,uBAAe,OAAO,SAAS,MAAM,CAAC;AACtC;AAAA,MACF;AACA,UAAI,aAAa,UAAU;AACzB,mBAAW,QAAQ,OAAO,SAAS,OAAO,CAAC;AAC3C;AAAA,MACF;AACA,UAAI,WAAW,UAAU;AACvB,cAAM,QAAQ,kBAAkB,GAAG,QAAQ;AAC3C,mBAAW,MAAM,KAAK;AACtB,uBAAe,KAAK;AACpB;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,UAAI,iBAAiB,gBAAgB;AACnC,mBAAW,MAAM,KAAK;AACtB,uBAAe,KAAK;AACpB;AAAA,MACF;AAAA,IAEF;AAAA,EACF;AACA,QAAM,UAAU,IAAI,YAAY;AAChC,SAAO,IAAI,eAAe;AAAA,IACxB,MAAM,YAAY;AAChB,UAAI,cAAc;AAClB,aAAO,KAAK;AACZ,eAAe,OAAO;AAAA;AACpB,cAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAClE,kBAAM,QAAQ,IAAI,eAAe,aAAa,uBAAuB;AACrE,uBAAW,MAAM,KAAK;AACtB,2BAAe,KAAK;AACpB,mBAAO,QAAQ,QAAQ;AAAA,UACzB;AACA,cAAI;AACF,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,MAAM,OAAO,KAAK;AACtB,gBAAI,MAAM;AACR,kBAAI,YAAY,KAAK,GAAG;AACtB,4BAAY,YAAY,KAAK,GAAG,UAAU;AAAA,cAC5C;AACA,yBAAW,MAAM;AACjB;AAAA,YACF;AACA,gBAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAClE,oBAAM,QAAQ,IAAI,eAAe,aAAa,uBAAuB;AACrE,yBAAW,MAAM,KAAK;AACtB,6BAAe,KAAK;AACpB,oBAAM,OAAO,OAAO;AACpB;AAAA,YACF;AACA,2BAAe,QAAQ,OAAO,OAAO;AAAA,cACnC,QAAQ;AAAA,YACV,CAAC;AACD,kBAAM,QAAQ,YAAY,MAAM,IAAI;AACpC,0BAAc,MAAM,IAAI,KAAK;AAC7B,uBAAW,QAAQ,OAAO;AACxB,kBAAI,KAAK,KAAK,GAAG;AACf,4BAAY,KAAK,KAAK,GAAG,UAAU;AAAA,cACrC;AAAA,YACF;AACA,mBAAO,KAAK;AAAA,UACd,SAAS,OAAO;AACd,kBAAM,iBAAiB,iBAAiB,iBAAiB,QAAQ,kBAAkB,GAAG,IAAI;AAC1F,uBAAW,MAAM,cAAc;AAC/B,2BAAe,cAAc;AAAA,UAC/B;AAAA,QACF;AAAA;AAAA,IACF;AAAA,IACA,SAAS;AACP,aAAO,OAAO,OAAO;AAAA,IACvB;AAAA,EACF,CAAC;AACH;AACA,IAAM,OAAO;AACb,IAAM,UAAU;AAkBhB,IAAM,qBAAqB;AAC3B,IAAM,0BAA0B;AAChC,IAAM,0BAA0B;AAChC,SAAS,kBAAkB,SAAS;AAClC,QAAM,UAAU,CAAC,WAAW;AAAA,IAC1B,oBAAoB;AAAA,EACtB,MAAM;AAEJ,UAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAa;AACtD,UAAM,eAAe,UAAU,YAAY,kBAAkB;AAC7D,UAAM,oBAAoB,UAAU,YAAY,uBAAuB;AACvE,UAAM,mBAAmB,UAAU,YAAY,uBAAuB;AAEtE,WAAO,IAAI,iBAAiB,KAAK,cAAc,mBAAmB,kBAAkB,oBAAoB;AAAA,EAC1G;AACA,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAgB;AAAA,IAAS;AAAA;AAAA,EAAmC,EAAE,qBAAqB,IAAI,CAAC;AACzH,kBAAgB,MAAM,SAAS,OAAO;AAEtC,kBAAgB,MAAM,SAAS,SAAS;AAC1C;AA0BA,SAAS,aAAa,MAAM,OAAO,GAAG,uBAAuB,gBAAgB;AAE3E,QAAM,oBAAoB,aAAa,mBAAmB,GAAG,GAAG,cAAc;AAC9E,QAAM,oBAAoB,kBAAkB,aAAa;AAAA,IACvD,YAAY;AAAA,EACd,CAAC;AACD,QAAM,WAAW,kCAAkC,WAAW;AAC9D,MAAI,UAAU;AACZ,6BAAyB,mBAAmB,GAAG,QAAQ;AAAA,EACzD;AACA,SAAO;AACT;AAUA,SAAS,yBAAyB,mBAAmB,MAAM,MAAM;AAC/D,6BAA2B,mBAAmB,iBAAiB,GAAG,MAAM,IAAI;AAC9E;AAMA,SAAS,cAAc,mBAAmBF,OAAM,SAAS;AACvD,SAAO,gBAAgB,mBAAmB,iBAAiB,GAAGA,OAAM,OAAO;AAC7E;AAMA,SAAS,qBAAqB,mBAAmB,KAAK,SAAS;AAC7D,SAAO,uBAAuB,mBAAmB,iBAAiB,GAAG,KAAK,OAAO;AACnF;AAOA,kBAAkB;;;ACn7BlB,SAASG,eAAc,WAAWC,OAAM,SAAS;AAC/C,MAAI,WAAW,cAAgB,WAAWA,OAAM,OAAO;AACvD,SAAO,SAAU,MAAM;AACrB,WAAO,KAAK,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,SAAU,GAAG;AAChD,aAAO,EAAE;AAAA,IACX,CAAC,CAAC;AAAA,EACJ;AACF;;;ACfA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,WAAW;AACrB,WAAO;AAAA,EACT;AACF;AACA,IAAM,0BAA0B;AAChC,IAAM,qBAAN,MAAyB;AAAA,EACvB,cAAc;AACZ,WAAO,mBAAmB,uBAAuB;AAAA,EACnD;AACF;AACA,IAAM,oBAAoB,MAAM,GAAG,GAAG,EAAE,KAAK,UAAU,MAAM,KAAK,mBAAmB,uBAAuB,CAAC,CAAC,GAAG,SAAS,CAAC;AAC3H,IAAM,+BAA+B,IAAI,eAAe,kCAAkC;AAC1F,SAAS,gCAAgC,UAAU,YAAY;AAC7D,QAAM,cAAc,sBAAsB,yBAAyB,UAAU,UAAU;AACvF,SAAO,eAAe,IAAI,UAAU,WAAW;AACjD;AACA,SAAS,yBAAyB,IAAI;AACpC,SAAO,CAAC,MAAM,aAAa;AACzB,UAAM,YAAY,KAAK,kBAAkB,MAAM,GAAG,QAAQ,CAAC;AAC3D,WAAO,IAAI,UAAU,SAAS;AAAA,EAChC;AACF;AACA,IAAM,+BAA+B;AAAA,EACnC,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,4BAA4B,CAAC;AACvD;AACA,IAAM,sCAAsC;AAAA,EAC1C,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,4BAA4B,GAAG,WAAW;AACpE;AACA,IAAM,mBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,oBAAgB,eAAe,QAAQ,MAAM,IAAI;AAAA,EACnD;AAUF;AATE,cAJI,kBAIG,QAAO,SAAS,wBAAwB,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,kBAAiB;AACpD;AACA,cAPI,kBAOG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AACR,CAAC;AACD,cAVI,kBAUG,QAAyB,iBAAiB;AAAA,EAC/C,WAAW,CAAC,qCAAqC,4BAA4B;AAC/E,CAAC;AAZH,IAAM,kBAAN;AAAA,CAcC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,qCAAqC,4BAA4B;AAAA,IAC/E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,iBAAiB,OAAO,MAAM;AACrC,kBAAgB,eAAe,QAAQ,MAAM,IAAI;AACjD,SAAO,yBAAyB,CAAC,qCAAqC,8BAA8B;AAAA,IAClG,SAAS;AAAA,IACT,YAAY,yBAAyB,EAAE;AAAA,IACvC,OAAO;AAAA,IACP,MAAM;AAAA,MAAC;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAwB;AAAA;AAAA,MAEjD,CAAC,IAAI,SAAS,GAAG,aAAa;AAAA,MAAG,CAAC,IAAI,SAAS,GAAG,iBAAiB;AAAA,MAAG,GAAG;AAAA,IAAI;AAAA,EAC/E,CAAC,CAAC;AACJ;AAGA,IAAM,oBAAoB,UAAUC,gBAAiB,IAAI;AAGzD,IAAMC,4BAA2B,UAAU,0BAA4B,IAAI;AAC3E,IAAMC,gBAAe,UAAU,cAAgB,IAAI;AACnD,IAAMF,iBAAgB,UAAU,eAAiB,IAAI;AACrD,IAAMG,wBAAuB,UAAU,sBAAwB,IAAI;", "names": ["status", "timer", "name", "options", "error", "httpsCallable", "name", "httpsCallable", "connectFunctionsEmulator", "getFunctions", "httpsCallableFromURL"]}