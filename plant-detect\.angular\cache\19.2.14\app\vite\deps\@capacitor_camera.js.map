{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/camera/dist/esm/definitions.js", "../../../../../../node_modules/@capacitor/camera/dist/esm/web.js", "../../../../../../node_modules/@capacitor/camera/dist/esm/index.js"], "sourcesContent": ["export var CameraSource;\n(function (CameraSource) {\n  /**\n   * Prompts the user to select either the photo album or take a photo.\n   */\n  CameraSource[\"Prompt\"] = \"PROMPT\";\n  /**\n   * Take a new photo using the camera.\n   */\n  CameraSource[\"Camera\"] = \"CAMERA\";\n  /**\n   * Pick an existing photo from the gallery or photo album.\n   */\n  CameraSource[\"Photos\"] = \"PHOTOS\";\n})(CameraSource || (CameraSource = {}));\nexport var CameraDirection;\n(function (CameraDirection) {\n  CameraDirection[\"Rear\"] = \"REAR\";\n  CameraDirection[\"Front\"] = \"FRONT\";\n})(CameraDirection || (CameraDirection = {}));\nexport var CameraResultType;\n(function (CameraResultType) {\n  CameraResultType[\"Uri\"] = \"uri\";\n  CameraResultType[\"Base64\"] = \"base64\";\n  CameraResultType[\"DataUrl\"] = \"dataUrl\";\n})(CameraResultType || (CameraResultType = {}));\n", "import { WebPlugin, CapacitorException } from '@capacitor/core';\nimport { CameraSource, CameraDirection } from './definitions';\nexport class CameraWeb extends WebPlugin {\n  async getPhoto(options) {\n    // eslint-disable-next-line no-async-promise-executor\n    return new Promise(async (resolve, reject) => {\n      if (options.webUseInput || options.source === CameraSource.Photos) {\n        this.fileInputExperience(options, resolve, reject);\n      } else if (options.source === CameraSource.Prompt) {\n        let actionSheet = document.querySelector('pwa-action-sheet');\n        if (!actionSheet) {\n          actionSheet = document.createElement('pwa-action-sheet');\n          document.body.appendChild(actionSheet);\n        }\n        actionSheet.header = options.promptLabelHeader || 'Photo';\n        actionSheet.cancelable = false;\n        actionSheet.options = [{\n          title: options.promptLabelPhoto || 'From Photos'\n        }, {\n          title: options.promptLabelPicture || 'Take Picture'\n        }];\n        actionSheet.addEventListener('onSelection', async e => {\n          const selection = e.detail;\n          if (selection === 0) {\n            this.fileInputExperience(options, resolve, reject);\n          } else {\n            this.cameraExperience(options, resolve, reject);\n          }\n        });\n      } else {\n        this.cameraExperience(options, resolve, reject);\n      }\n    });\n  }\n  async pickImages(_options) {\n    // eslint-disable-next-line no-async-promise-executor\n    return new Promise(async (resolve, reject) => {\n      this.multipleFileInputExperience(resolve, reject);\n    });\n  }\n  async cameraExperience(options, resolve, reject) {\n    if (customElements.get('pwa-camera-modal')) {\n      const cameraModal = document.createElement('pwa-camera-modal');\n      cameraModal.facingMode = options.direction === CameraDirection.Front ? 'user' : 'environment';\n      document.body.appendChild(cameraModal);\n      try {\n        await cameraModal.componentOnReady();\n        cameraModal.addEventListener('onPhoto', async e => {\n          const photo = e.detail;\n          if (photo === null) {\n            reject(new CapacitorException('User cancelled photos app'));\n          } else if (photo instanceof Error) {\n            reject(photo);\n          } else {\n            resolve(await this._getCameraPhoto(photo, options));\n          }\n          cameraModal.dismiss();\n          document.body.removeChild(cameraModal);\n        });\n        cameraModal.present();\n      } catch (e) {\n        this.fileInputExperience(options, resolve, reject);\n      }\n    } else {\n      console.error(`Unable to load PWA Element 'pwa-camera-modal'. See the docs: https://capacitorjs.com/docs/web/pwa-elements.`);\n      this.fileInputExperience(options, resolve, reject);\n    }\n  }\n  fileInputExperience(options, resolve, reject) {\n    let input = document.querySelector('#_capacitor-camera-input');\n    const cleanup = () => {\n      var _a;\n      (_a = input.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(input);\n    };\n    if (!input) {\n      input = document.createElement('input');\n      input.id = '_capacitor-camera-input';\n      input.type = 'file';\n      input.hidden = true;\n      document.body.appendChild(input);\n      input.addEventListener('change', _e => {\n        const file = input.files[0];\n        let format = 'jpeg';\n        if (file.type === 'image/png') {\n          format = 'png';\n        } else if (file.type === 'image/gif') {\n          format = 'gif';\n        }\n        if (options.resultType === 'dataUrl' || options.resultType === 'base64') {\n          const reader = new FileReader();\n          reader.addEventListener('load', () => {\n            if (options.resultType === 'dataUrl') {\n              resolve({\n                dataUrl: reader.result,\n                format\n              });\n            } else if (options.resultType === 'base64') {\n              const b64 = reader.result.split(',')[1];\n              resolve({\n                base64String: b64,\n                format\n              });\n            }\n            cleanup();\n          });\n          reader.readAsDataURL(file);\n        } else {\n          resolve({\n            webPath: URL.createObjectURL(file),\n            format: format\n          });\n          cleanup();\n        }\n      });\n      input.addEventListener('cancel', _e => {\n        reject(new CapacitorException('User cancelled photos app'));\n        cleanup();\n      });\n    }\n    input.accept = 'image/*';\n    input.capture = true;\n    if (options.source === CameraSource.Photos || options.source === CameraSource.Prompt) {\n      input.removeAttribute('capture');\n    } else if (options.direction === CameraDirection.Front) {\n      input.capture = 'user';\n    } else if (options.direction === CameraDirection.Rear) {\n      input.capture = 'environment';\n    }\n    input.click();\n  }\n  multipleFileInputExperience(resolve, reject) {\n    let input = document.querySelector('#_capacitor-camera-input-multiple');\n    const cleanup = () => {\n      var _a;\n      (_a = input.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(input);\n    };\n    if (!input) {\n      input = document.createElement('input');\n      input.id = '_capacitor-camera-input-multiple';\n      input.type = 'file';\n      input.hidden = true;\n      input.multiple = true;\n      document.body.appendChild(input);\n      input.addEventListener('change', _e => {\n        const photos = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < input.files.length; i++) {\n          const file = input.files[i];\n          let format = 'jpeg';\n          if (file.type === 'image/png') {\n            format = 'png';\n          } else if (file.type === 'image/gif') {\n            format = 'gif';\n          }\n          photos.push({\n            webPath: URL.createObjectURL(file),\n            format: format\n          });\n        }\n        resolve({\n          photos\n        });\n        cleanup();\n      });\n      input.addEventListener('cancel', _e => {\n        reject(new CapacitorException('User cancelled photos app'));\n        cleanup();\n      });\n    }\n    input.accept = 'image/*';\n    input.click();\n  }\n  _getCameraPhoto(photo, options) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      const format = photo.type.split('/')[1];\n      if (options.resultType === 'uri') {\n        resolve({\n          webPath: URL.createObjectURL(photo),\n          format: format,\n          saved: false\n        });\n      } else {\n        reader.readAsDataURL(photo);\n        reader.onloadend = () => {\n          const r = reader.result;\n          if (options.resultType === 'dataUrl') {\n            resolve({\n              dataUrl: r,\n              format: format,\n              saved: false\n            });\n          } else {\n            resolve({\n              base64String: r.split(',')[1],\n              format: format,\n              saved: false\n            });\n          }\n        };\n        reader.onerror = e => {\n          reject(e);\n        };\n      }\n    });\n  }\n  async checkPermissions() {\n    if (typeof navigator === 'undefined' || !navigator.permissions) {\n      throw this.unavailable('Permissions API not available in this browser');\n    }\n    try {\n      // https://developer.mozilla.org/en-US/docs/Web/API/Permissions/query\n      // the specific permissions that are supported varies among browsers that implement the\n      // permissions API, so we need a try/catch in case 'camera' is invalid\n      const permission = await window.navigator.permissions.query({\n        name: 'camera'\n      });\n      return {\n        camera: permission.state,\n        photos: 'granted'\n      };\n    } catch (_a) {\n      throw this.unavailable('Camera permissions are not available in this browser');\n    }\n  }\n  async requestPermissions() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async pickLimitedLibraryPhotos() {\n    throw this.unavailable('Not implemented on web.');\n  }\n  async getLimitedLibraryPhotos() {\n    throw this.unavailable('Not implemented on web.');\n  }\n}\nconst Camera = new CameraWeb();\nexport { Camera };\n", "import { registerPlugin } from '@capacitor/core';\nimport { CameraWeb } from './web';\nconst Camera = registerPlugin('Camera', {\n  web: () => new CameraWeb()\n});\nexport * from './definitions';\nexport { Camera };\n"], "mappings": ";;;;;;;;;;AAAO,IAAI;AAAA,CACV,SAAUA,eAAc;AAIvB,EAAAA,cAAa,QAAQ,IAAI;AAIzB,EAAAA,cAAa,QAAQ,IAAI;AAIzB,EAAAA,cAAa,QAAQ,IAAI;AAC3B,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAC/B,IAAI;AAAA,CACV,SAAUC,kBAAiB;AAC1B,EAAAA,iBAAgB,MAAM,IAAI;AAC1B,EAAAA,iBAAgB,OAAO,IAAI;AAC7B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AACrC,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAC3B,EAAAA,kBAAiB,KAAK,IAAI;AAC1B,EAAAA,kBAAiB,QAAQ,IAAI;AAC7B,EAAAA,kBAAiB,SAAS,IAAI;AAChC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;;;ACvBvC,IAAM,YAAN,cAAwB,UAAU;AAAA,EACjC,SAAS,SAAS;AAAA;AAEtB,aAAO,IAAI,QAAQ,CAAO,SAAS,WAAW;AAC5C,YAAI,QAAQ,eAAe,QAAQ,WAAW,aAAa,QAAQ;AACjE,eAAK,oBAAoB,SAAS,SAAS,MAAM;AAAA,QACnD,WAAW,QAAQ,WAAW,aAAa,QAAQ;AACjD,cAAI,cAAc,SAAS,cAAc,kBAAkB;AAC3D,cAAI,CAAC,aAAa;AAChB,0BAAc,SAAS,cAAc,kBAAkB;AACvD,qBAAS,KAAK,YAAY,WAAW;AAAA,UACvC;AACA,sBAAY,SAAS,QAAQ,qBAAqB;AAClD,sBAAY,aAAa;AACzB,sBAAY,UAAU,CAAC;AAAA,YACrB,OAAO,QAAQ,oBAAoB;AAAA,UACrC,GAAG;AAAA,YACD,OAAO,QAAQ,sBAAsB;AAAA,UACvC,CAAC;AACD,sBAAY,iBAAiB,eAAe,CAAM,MAAK;AACrD,kBAAM,YAAY,EAAE;AACpB,gBAAI,cAAc,GAAG;AACnB,mBAAK,oBAAoB,SAAS,SAAS,MAAM;AAAA,YACnD,OAAO;AACL,mBAAK,iBAAiB,SAAS,SAAS,MAAM;AAAA,YAChD;AAAA,UACF,EAAC;AAAA,QACH,OAAO;AACL,eAAK,iBAAiB,SAAS,SAAS,MAAM;AAAA,QAChD;AAAA,MACF,EAAC;AAAA,IACH;AAAA;AAAA,EACM,WAAW,UAAU;AAAA;AAEzB,aAAO,IAAI,QAAQ,CAAO,SAAS,WAAW;AAC5C,aAAK,4BAA4B,SAAS,MAAM;AAAA,MAClD,EAAC;AAAA,IACH;AAAA;AAAA,EACM,iBAAiB,SAAS,SAAS,QAAQ;AAAA;AAC/C,UAAI,eAAe,IAAI,kBAAkB,GAAG;AAC1C,cAAM,cAAc,SAAS,cAAc,kBAAkB;AAC7D,oBAAY,aAAa,QAAQ,cAAc,gBAAgB,QAAQ,SAAS;AAChF,iBAAS,KAAK,YAAY,WAAW;AACrC,YAAI;AACF,gBAAM,YAAY,iBAAiB;AACnC,sBAAY,iBAAiB,WAAW,CAAM,MAAK;AACjD,kBAAM,QAAQ,EAAE;AAChB,gBAAI,UAAU,MAAM;AAClB,qBAAO,IAAI,mBAAmB,2BAA2B,CAAC;AAAA,YAC5D,WAAW,iBAAiB,OAAO;AACjC,qBAAO,KAAK;AAAA,YACd,OAAO;AACL,sBAAQ,MAAM,KAAK,gBAAgB,OAAO,OAAO,CAAC;AAAA,YACpD;AACA,wBAAY,QAAQ;AACpB,qBAAS,KAAK,YAAY,WAAW;AAAA,UACvC,EAAC;AACD,sBAAY,QAAQ;AAAA,QACtB,SAAS,GAAG;AACV,eAAK,oBAAoB,SAAS,SAAS,MAAM;AAAA,QACnD;AAAA,MACF,OAAO;AACL,gBAAQ,MAAM,6GAA6G;AAC3H,aAAK,oBAAoB,SAAS,SAAS,MAAM;AAAA,MACnD;AAAA,IACF;AAAA;AAAA,EACA,oBAAoB,SAAS,SAAS,QAAQ;AAC5C,QAAI,QAAQ,SAAS,cAAc,0BAA0B;AAC7D,UAAM,UAAU,MAAM;AACpB,UAAI;AACJ,OAAC,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,KAAK;AAAA,IACnF;AACA,QAAI,CAAC,OAAO;AACV,cAAQ,SAAS,cAAc,OAAO;AACtC,YAAM,KAAK;AACX,YAAM,OAAO;AACb,YAAM,SAAS;AACf,eAAS,KAAK,YAAY,KAAK;AAC/B,YAAM,iBAAiB,UAAU,QAAM;AACrC,cAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,YAAI,SAAS;AACb,YAAI,KAAK,SAAS,aAAa;AAC7B,mBAAS;AAAA,QACX,WAAW,KAAK,SAAS,aAAa;AACpC,mBAAS;AAAA,QACX;AACA,YAAI,QAAQ,eAAe,aAAa,QAAQ,eAAe,UAAU;AACvE,gBAAM,SAAS,IAAI,WAAW;AAC9B,iBAAO,iBAAiB,QAAQ,MAAM;AACpC,gBAAI,QAAQ,eAAe,WAAW;AACpC,sBAAQ;AAAA,gBACN,SAAS,OAAO;AAAA,gBAChB;AAAA,cACF,CAAC;AAAA,YACH,WAAW,QAAQ,eAAe,UAAU;AAC1C,oBAAM,MAAM,OAAO,OAAO,MAAM,GAAG,EAAE,CAAC;AACtC,sBAAQ;AAAA,gBACN,cAAc;AAAA,gBACd;AAAA,cACF,CAAC;AAAA,YACH;AACA,oBAAQ;AAAA,UACV,CAAC;AACD,iBAAO,cAAc,IAAI;AAAA,QAC3B,OAAO;AACL,kBAAQ;AAAA,YACN,SAAS,IAAI,gBAAgB,IAAI;AAAA,YACjC;AAAA,UACF,CAAC;AACD,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AACD,YAAM,iBAAiB,UAAU,QAAM;AACrC,eAAO,IAAI,mBAAmB,2BAA2B,CAAC;AAC1D,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,QAAI,QAAQ,WAAW,aAAa,UAAU,QAAQ,WAAW,aAAa,QAAQ;AACpF,YAAM,gBAAgB,SAAS;AAAA,IACjC,WAAW,QAAQ,cAAc,gBAAgB,OAAO;AACtD,YAAM,UAAU;AAAA,IAClB,WAAW,QAAQ,cAAc,gBAAgB,MAAM;AACrD,YAAM,UAAU;AAAA,IAClB;AACA,UAAM,MAAM;AAAA,EACd;AAAA,EACA,4BAA4B,SAAS,QAAQ;AAC3C,QAAI,QAAQ,SAAS,cAAc,mCAAmC;AACtE,UAAM,UAAU,MAAM;AACpB,UAAI;AACJ,OAAC,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,KAAK;AAAA,IACnF;AACA,QAAI,CAAC,OAAO;AACV,cAAQ,SAAS,cAAc,OAAO;AACtC,YAAM,KAAK;AACX,YAAM,OAAO;AACb,YAAM,SAAS;AACf,YAAM,WAAW;AACjB,eAAS,KAAK,YAAY,KAAK;AAC/B,YAAM,iBAAiB,UAAU,QAAM;AACrC,cAAM,SAAS,CAAC;AAEhB,iBAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,gBAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,cAAI,SAAS;AACb,cAAI,KAAK,SAAS,aAAa;AAC7B,qBAAS;AAAA,UACX,WAAW,KAAK,SAAS,aAAa;AACpC,qBAAS;AAAA,UACX;AACA,iBAAO,KAAK;AAAA,YACV,SAAS,IAAI,gBAAgB,IAAI;AAAA,YACjC;AAAA,UACF,CAAC;AAAA,QACH;AACA,gBAAQ;AAAA,UACN;AAAA,QACF,CAAC;AACD,gBAAQ;AAAA,MACV,CAAC;AACD,YAAM,iBAAiB,UAAU,QAAM;AACrC,eAAO,IAAI,mBAAmB,2BAA2B,CAAC;AAC1D,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,UAAM,SAAS;AACf,UAAM,MAAM;AAAA,EACd;AAAA,EACA,gBAAgB,OAAO,SAAS;AAC9B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,SAAS,IAAI,WAAW;AAC9B,YAAM,SAAS,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC;AACtC,UAAI,QAAQ,eAAe,OAAO;AAChC,gBAAQ;AAAA,UACN,SAAS,IAAI,gBAAgB,KAAK;AAAA,UAClC;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,eAAO,cAAc,KAAK;AAC1B,eAAO,YAAY,MAAM;AACvB,gBAAM,IAAI,OAAO;AACjB,cAAI,QAAQ,eAAe,WAAW;AACpC,oBAAQ;AAAA,cACN,SAAS;AAAA,cACT;AAAA,cACA,OAAO;AAAA,YACT,CAAC;AAAA,UACH,OAAO;AACL,oBAAQ;AAAA,cACN,cAAc,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,cAC5B;AAAA,cACA,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO,UAAU,OAAK;AACpB,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACM,mBAAmB;AAAA;AACvB,UAAI,OAAO,cAAc,eAAe,CAAC,UAAU,aAAa;AAC9D,cAAM,KAAK,YAAY,+CAA+C;AAAA,MACxE;AACA,UAAI;AAIF,cAAM,aAAa,MAAM,OAAO,UAAU,YAAY,MAAM;AAAA,UAC1D,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,UACL,QAAQ,WAAW;AAAA,UACnB,QAAQ;AAAA,QACV;AAAA,MACF,SAAS,IAAI;AACX,cAAM,KAAK,YAAY,sDAAsD;AAAA,MAC/E;AAAA,IACF;AAAA;AAAA,EACM,qBAAqB;AAAA;AACzB,YAAM,KAAK,cAAc,yBAAyB;AAAA,IACpD;AAAA;AAAA,EACM,2BAA2B;AAAA;AAC/B,YAAM,KAAK,YAAY,yBAAyB;AAAA,IAClD;AAAA;AAAA,EACM,0BAA0B;AAAA;AAC9B,YAAM,KAAK,YAAY,yBAAyB;AAAA,IAClD;AAAA;AACF;AACA,IAAM,SAAS,IAAI,UAAU;;;ACzO7B,IAAMC,UAAS,eAAe,UAAU;AAAA,EACtC,KAAK,MAAM,IAAI,UAAU;AAC3B,CAAC;", "names": ["CameraSource", "CameraDirection", "CameraResultType", "Camera"]}