import{K as gt,N as mt,U as ut,a as rt,f as at,g as st,i as ct,j as lt,w as dt}from"./chunk-AWIL3PGF.js";import{a as pt}from"./chunk-JBBAVVC3.js";import{G as V,I as X,L as Q,N as W,O as Z,U as tt,X as et,_ as nt,aa as it,ba as ot,x as H}from"./chunk-N2M46O22.js";import{C as a,D as x,E as I,F as p,G as d,I as t,Ia as E,J as n,K as u,Ka as T,La as F,M as S,Ma as N,N as f,Na as D,O as i,Oa as R,P as m,Qa as j,Ua as U,Wa as L,X as b,Xa as B,Y as O,Ya as Y,ba as y,ca as w,db as z,ea as A,ga as k,hb as G,ib as J,m as M,nb as $,o as _,ob as K,qb as q}from"./chunk-AZEIYKMX.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{g}from"./chunk-OLRFWS6T.js";var ft=(()=>{let s=class s{constructor(e,r,c){this.auth=e,this.firestore=r,this.storage=c}runAllTests(){return g(this,null,function*(){let e=[];return e.push(yield this.testAuthentication()),e.push(yield this.testFirestore()),e.push(yield this.testStorage()),e})}testAuthentication(){return g(this,null,function*(){try{return this.auth?{service:"Authentication",status:"success",message:"Firebase Auth is configured and working",details:{currentUser:this.auth.currentUser?"User signed in":"No user signed in",authDomain:this.auth.config.authDomain}}:{service:"Authentication",status:"not_configured",message:"Firebase Auth not configured"}}catch(e){return{service:"Authentication",status:"error",message:"Firebase Auth configuration error",details:e.message}}})}testFirestore(){return g(this,null,function*(){try{if(!this.firestore)return{service:"Firestore",status:"not_configured",message:"Firebase Firestore not configured"};let e=Q(this.firestore,"test"),r=W(e,"connection-test"),c={message:"Firebase connection test",timestamp:V.now(),testId:Date.now()};yield tt(r,c);let h=yield Z(r);return h.exists()?{service:"Firestore",status:"success",message:"Firebase Firestore is configured and working",details:{projectId:this.firestore.app.options.projectId,testData:h.data()}}:{service:"Firestore",status:"error",message:"Could not read test document from Firestore"}}catch(e){return{service:"Firestore",status:"error",message:"Firebase Firestore configuration error",details:e.message}}})}testStorage(){return g(this,null,function*(){try{if(!this.storage)return{service:"Storage",status:"not_configured",message:"Firebase Storage not configured"};let e=new Blob(["Firebase Storage test"],{type:"text/plain"}),r=it(this.storage,`test/connection-test-${Date.now()}.txt`);yield ot(r,e);let c=yield nt(r);return{service:"Storage",status:"success",message:"Firebase Storage is configured and working",details:{bucket:this.storage.app.options.storageBucket,testFileUrl:c}}}catch(e){return{service:"Storage",status:"error",message:"Firebase Storage configuration error",details:e.message}}})}testPlantNetAPI(){return g(this,null,function*(){try{let c=yield(yield fetch("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==")).blob(),h=new File([c],"test.png",{type:"image/png"}),P=new FormData;P.append("images",h),P.append("organs","leaf");let C=yield fetch("https://my-api.plantnet.org/v1/identify/k-world-flora?api-key=2b10vj1Of6umIEdb0f8Y6XSo8O",{method:"POST",body:P});if(C.ok){let v=yield C.json();return{service:"PlantNet API",status:"success",message:"PlantNet API is working with your key",details:{remainingRequests:v.remainingIdentificationRequests,version:v.version}}}else{let v=yield C.text();return{service:"PlantNet API",status:"error",message:`PlantNet API error: ${C.status}`,details:v}}}catch(e){return{service:"PlantNet API",status:"error",message:"PlantNet API test failed",details:e.message}}})}};s.\u0275fac=function(r){return new(r||s)(_(H),_(X),_(et))},s.\u0275prov=M({token:s,factory:s.\u0275fac,providedIn:"root"});let o=s;return o})();function vt(o,s){o&1&&(t(0,"div",19),u(1,"ion-spinner",20),t(2,"p"),i(3,"Testing Firebase configuration..."),n()())}function _t(o,s){if(o&1&&(t(0,"div",28)(1,"pre"),i(2),b(3,"json"),n()()),o&2){let l=f().$implicit;a(2),m(O(3,1,l.details))}}function xt(o,s){if(o&1&&(t(0,"div",22)(1,"div",23)(2,"ion-chip",24),u(3,"ion-icon",25),t(4,"ion-label"),i(5),n()()(),t(6,"p",26),i(7),n(),p(8,_t,4,3,"div",27),n()),o&2){let l=s.$implicit,e=f(2);a(2),d("color",e.getStatusColor(l.status)),a(),d("name",e.getStatusIcon(l.status)),a(2),m(l.service),a(2),m(l.message),a(),d("ngIf",l.details)}}function Pt(o,s){if(o&1&&(t(0,"div"),p(1,xt,9,5,"div",21),n()),o&2){let l=f();a(),d("ngForOf",l.testResults)}}function bt(o,s){if(o&1&&(t(0,"div",28)(1,"pre"),i(2),b(3,"json"),n()()),o&2){let l=f(2);a(2),m(O(3,1,l.plantNetResult.details))}}function Ot(o,s){if(o&1&&(t(0,"div",22)(1,"div",23)(2,"ion-chip",24),u(3,"ion-icon",25),t(4,"ion-label"),i(5),n()()(),t(6,"p",26),i(7),n(),p(8,bt,4,3,"div",27),n()),o&2){let l=f();a(2),d("color",l.getStatusColor(l.plantNetResult.status)),a(),d("name",l.getStatusIcon(l.plantNetResult.status)),a(2),m(l.plantNetResult.service),a(2),m(l.plantNetResult.message),a(),d("ngIf",l.plantNetResult.details)}}var Ut=(()=>{let s=class s{constructor(e,r,c){this.firebaseTestService=e,this.loadingController=r,this.toastController=c,this.testResults=[],this.plantNetResult=null,this.isLoading=!1,this.environment=pt,rt({checkmarkCircle:st,closeCircle:ct,warningOutline:ut,refresh:gt,settings:mt,cloud:lt,key:dt,camera:at})}ngOnInit(){this.runTests()}runTests(){return g(this,null,function*(){this.isLoading=!0;let e=yield this.loadingController.create({message:"Testing Firebase configuration...",spinner:"crescent"});yield e.present();try{this.testResults=yield this.firebaseTestService.runAllTests(),this.plantNetResult=yield this.firebaseTestService.testPlantNetAPI()}catch(r){console.error("Test error:",r),this.showToast("Test failed: "+r.message,"danger")}finally{this.isLoading=!1,yield e.dismiss()}})}getStatusColor(e){switch(e){case"success":return"success";case"error":return"danger";case"not_configured":return"warning";default:return"medium"}}getStatusIcon(e){switch(e){case"success":return"checkmark-circle";case"error":return"close-circle";case"not_configured":return"warning-outline";default:return"warning-outline"}}showToast(e,r="primary"){return g(this,null,function*(){yield(yield this.toastController.create({message:e,duration:3e3,color:r,position:"bottom"})).present()})}retryTests(){return g(this,null,function*(){yield this.runTests(),this.showToast("Tests completed","primary")})}};s.\u0275fac=function(r){return new(r||s)(x(ft),x($),x(K))},s.\u0275cmp=I({type:s,selectors:[["app-test-config"]],decls:88,vars:9,consts:[[3,"translucent"],[3,"fullscreen"],[1,"test-container"],[1,"env-card"],["name","settings"],[1,"test-card"],["name","cloud"],["class","loading-container",4,"ngIf"],[4,"ngIf"],["name","camera"],["class","test-result",4,"ngIf"],[1,"instructions-card"],["name","key"],[1,"instruction-section"],["href","https://console.firebase.google.com/","target","_blank"],["href","https://my.plantnet.org/","target","_blank"],[1,"action-buttons"],["expand","block",3,"click","disabled"],["name","refresh","slot","start"],[1,"loading-container"],["name","crescent"],["class","test-result",4,"ngFor","ngForOf"],[1,"test-result"],[1,"result-header"],[3,"color"],[3,"name"],[1,"result-message"],["class","result-details",4,"ngIf"],[1,"result-details"]],template:function(r,c){r&1&&(t(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),i(3,"Configuration Test"),n()()(),t(4,"ion-content",1)(5,"div",2)(6,"ion-card",3)(7,"ion-card-header")(8,"ion-card-title"),u(9,"ion-icon",4),i(10," Environment Configuration "),n()(),t(11,"ion-card-content")(12,"ion-list")(13,"ion-item")(14,"ion-label")(15,"h3"),i(16,"Production Mode"),n(),t(17,"p"),i(18),n()()(),t(19,"ion-item")(20,"ion-label")(21,"h3"),i(22,"Demo Mode"),n(),t(23,"p"),i(24),n()()(),t(25,"ion-item")(26,"ion-label")(27,"h3"),i(28,"PlantNet API Key"),n(),t(29,"p"),i(30),n()()()()()(),t(31,"ion-card",5)(32,"ion-card-header")(33,"ion-card-title"),u(34,"ion-icon",6),i(35," Firebase Services "),n()(),t(36,"ion-card-content"),p(37,vt,4,0,"div",7)(38,Pt,2,1,"div",8),n()(),t(39,"ion-card",5)(40,"ion-card-header")(41,"ion-card-title"),u(42,"ion-icon",9),i(43," PlantNet API "),n()(),t(44,"ion-card-content"),p(45,Ot,9,5,"div",10),n()(),t(46,"ion-card",11)(47,"ion-card-header")(48,"ion-card-title"),u(49,"ion-icon",12),i(50," Configuration Instructions "),n()(),t(51,"ion-card-content")(52,"div",13)(53,"h4"),i(54,"\u{1F525} Firebase Setup"),n(),t(55,"ol")(56,"li"),i(57,"Go to "),t(58,"a",14),i(59,"Firebase Console"),n()(),t(60,"li"),i(61,"Create a new project or select existing"),n(),t(62,"li"),i(63,"Enable Authentication, Firestore, and Storage"),n(),t(64,"li"),i(65,"Add a web app and copy the config"),n(),t(66,"li"),i(67,"Update "),t(68,"code"),i(69,"src/environments/environment.ts"),n()()()(),t(70,"div",13)(71,"h4"),i(72,"\u{1F33F} PlantNet API Setup"),n(),t(73,"ol")(74,"li"),i(75,"Go to "),t(76,"a",15),i(77,"PlantNet API"),n()(),t(78,"li"),i(79,"Create an account and get your API key"),n(),t(80,"li"),i(81,"Your key is already configured: "),t(82,"code"),i(83,"2b10vj1Of6umIEdb0f8Y6XSo8O"),n()()()()()(),t(84,"div",16)(85,"ion-button",17),S("click",function(){return c.retryTests()}),u(86,"ion-icon",18),i(87," Retry Tests "),n()()()()),r&2&&(d("translucent",!0),a(4),d("fullscreen",!0),a(14),m(c.environment.production?"Yes":"No"),a(6),m(c.environment.demoMode?"Yes":"No (Using real APIs)"),a(6),m(c.environment.plantNetApiKey?"Configured":"Not configured"),a(7),d("ngIf",c.isLoading),a(),d("ngIf",!c.isLoading&&c.testResults.length>0),a(7),d("ngIf",c.plantNetResult),a(40),d("disabled",c.isLoading))},dependencies:[k,y,w,A,j,U,G,J,T,N,D,F,E,q,z,R,Y,L,B],styles:[".test-container[_ngcontent-%COMP%]{padding:20px 20px 100px}.env-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .instructions-card[_ngcontent-%COMP%]{border-radius:16px;margin-bottom:20px;box-shadow:0 4px 12px #0000001a}.env-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%], .instructions-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:1.2rem;font-weight:600}.env-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .instructions-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.4rem;color:var(--ion-color-primary)}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium)}.test-result[_ngcontent-%COMP%]{margin-bottom:20px;padding:16px;border-radius:12px;background:var(--ion-color-light)}.test-result[_ngcontent-%COMP%]:last-child{margin-bottom:0}.result-header[_ngcontent-%COMP%]{margin-bottom:12px}.result-header[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{font-weight:500}.result-header[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}.result-message[_ngcontent-%COMP%]{margin:0 0 12px;font-weight:500;color:var(--ion-color-dark)}.result-details[_ngcontent-%COMP%]{background:var(--ion-color-dark);border-radius:8px;padding:12px;margin-top:12px}.result-details[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-light);font-size:.85rem;white-space:pre-wrap;word-break:break-word}.instruction-section[_ngcontent-%COMP%]{margin-bottom:24px}.instruction-section[_ngcontent-%COMP%]:last-child{margin-bottom:0}.instruction-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;font-size:1.1rem;font-weight:600;color:var(--ion-color-primary)}.instruction-section[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin:0;padding-left:20px}.instruction-section[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px;line-height:1.5}.instruction-section[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--ion-color-primary);text-decoration:none;font-weight:500}.instruction-section[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.instruction-section[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:var(--ion-color-light);padding:2px 6px;border-radius:4px;font-family:Courier New,monospace;font-size:.9rem;color:var(--ion-color-dark)}.action-buttons[_ngcontent-%COMP%]{margin-top:20px}.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 12px;height:56px;font-weight:600}.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:not([disabled]){--background: var(--ion-color-primary);--box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), .3)}.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:not([disabled]):hover{--background: var(--ion-color-primary-shade);transform:translateY(-2px);--box-shadow: 0 6px 16px rgba(var(--ion-color-primary-rgb), .4)}@media (prefers-color-scheme: dark){.env-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .instructions-card[_ngcontent-%COMP%]{background:var(--ion-color-dark)}.test-result[_ngcontent-%COMP%]{background:var(--ion-color-dark-tint)}.result-details[_ngcontent-%COMP%]{background:var(--ion-color-step-900)}}@media (max-width: 768px){.test-container[_ngcontent-%COMP%]{padding:16px 16px 80px}.result-details[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{font-size:.8rem}}"]});let o=s;return o})();export{Ut as TestConfigPage};
