import{a as he}from"./chunk-SPQUQXZ2.js";import{A as de,C as ge,I as me,J as pe,N as fe,O as _e,Q as ue,R as ve,T as Ce,a as oe,e as ie,f as re,h as ae,m as ce,r as se,y as le}from"./chunk-AWIL3PGF.js";import{b as ne}from"./chunk-UUEAGYFQ.js";import"./chunk-N2M46O22.js";import{$a as H,B as x,C as r,D as _,E as S,F as h,G as l,Ha as D,I as e,Ia as T,J as t,Ja as A,K as c,Ka as F,L as M,La as U,M as u,N as f,O as o,Oa as j,P as m,Pa as R,Q as y,Qa as V,Ta as N,Ua as B,V as v,W as w,Wa as J,Xa as Y,Ya as $,_a as G,ab as Q,ba as I,ca as k,cb as q,ga as E,hb as K,ib as W,mb as X,nb as Z,oa as L,ob as ee,qb as te,sa as z,v as O,w as b}from"./chunk-AZEIYKMX.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{g as p}from"./chunk-OLRFWS6T.js";var Oe=()=>["/tabs/identify"],Pe=()=>["/tabs/observations"],be=()=>["/settings"],xe=s=>["/observation",s];function Me(s,g){s&1&&(e(0,"div",9)(1,"ion-card",10)(2,"ion-card-content")(3,"div",11),c(4,"ion-skeleton-text",12),e(5,"div",13),c(6,"ion-skeleton-text",14)(7,"ion-skeleton-text",15),t()()()()())}function ye(s,g){if(s&1&&(e(0,"div",52),c(1,"img",53),e(2,"div",54)(3,"h4",55),o(4),t(),e(5,"p",56),o(6),t(),e(7,"ion-chip",57),o(8),t()(),c(9,"ion-icon",58),t()),s&2){let i=g.$implicit,n=f(3);l("routerLink",w(7,xe,i.id)),r(),l("src",i.image.thumbnailUrl||i.image.url,x)("alt",i.species.scientificName),r(3),m(i.species.scientificName),r(2),m(n.formatDate(i.createdAt)),r(),l("color",n.getConfidenceColor(i.species.confidence)),r(),y(" ",i.species.confidence,"% confidence ")}}function Se(s,g){if(s&1&&(e(0,"ion-card",45)(1,"ion-card-content")(2,"div",46)(3,"h3",27),c(4,"ion-icon",47),o(5," Recent Observations "),t(),e(6,"ion-button",48),o(7," View All "),c(8,"ion-icon",49),t()(),e(9,"div",50),h(10,ye,10,9,"div",51),t()()()),s&2){let i=f(2);r(6),l("routerLink",v(2,Pe)),r(4),l("ngForOf",i.recentObservations)}}function we(s,g){if(s&1){let i=M();e(0,"div",16)(1,"ion-card",10)(2,"ion-card-content")(3,"div",11)(4,"ion-avatar",17),c(5,"img",18),t(),e(6,"div",13)(7,"h2",19),o(8),t(),e(9,"p",20),o(10),t(),e(11,"ion-chip",21),c(12,"ion-icon",22),e(13,"ion-label"),o(14),t()()()(),e(15,"div",23)(16,"div",24),c(17,"ion-icon",25),e(18,"span"),o(19),t()()()()(),e(20,"ion-card",26)(21,"ion-card-content")(22,"h3",27),c(23,"ion-icon",28),o(24," Statistics "),t(),e(25,"ion-grid",29)(26,"ion-row")(27,"ion-col",30)(28,"div",31)(29,"div",32),o(30),t(),e(31,"div",33),o(32,"Observations"),t()()(),e(33,"ion-col",30)(34,"div",31)(35,"div",32),o(36),t(),e(37,"div",33),o(38,"Species Found"),t()()()(),e(39,"ion-row")(40,"ion-col",30)(41,"div",31)(42,"div",32),o(43),t(),e(44,"div",33),o(45,"Accurate IDs"),t()()(),e(46,"ion-col",30)(47,"div",31)(48,"div",32),o(49),t(),e(50,"div",33),o(51,"Score"),t()()()()()()(),h(52,Se,11,3,"ion-card",34),e(53,"ion-card",35)(54,"ion-card-content")(55,"h3",27),c(56,"ion-icon",36),o(57," Quick Actions "),t(),e(58,"ion-list",37)(59,"ion-item",38),c(60,"ion-icon",39),e(61,"ion-label")(62,"h3"),o(63,"Identify Plant"),t(),e(64,"p"),o(65,"Take a photo to identify a new plant"),t()(),c(66,"ion-icon",40),t(),e(67,"ion-item",38),c(68,"ion-icon",41),e(69,"ion-label")(70,"h3"),o(71,"My Observations"),t(),e(72,"p"),o(73,"View all your plant observations"),t()(),c(74,"ion-icon",40),t(),e(75,"ion-item",38),c(76,"ion-icon",42),e(77,"ion-label")(78,"h3"),o(79,"Settings"),t(),e(80,"p"),o(81,"Manage your preferences"),t()(),c(82,"ion-icon",40),t(),e(83,"ion-item",43),u("click",function(){O(i);let a=f();return b(a.confirmSignOut())}),c(84,"ion-icon",44),e(85,"ion-label")(86,"h3"),o(87,"Sign Out"),t(),e(88,"p"),o(89,"Sign out of your account"),t()(),c(90,"ion-icon",40),t()()()()()}if(s&2){let i=f();r(5),l("src",i.user.avatar||"assets/images/default-avatar.svg",x)("alt",i.user.name),r(3),m(i.user.name),r(2),m(i.user.email),r(),l("color",i.getAchievementColor()),r(3),m(i.getAchievementLevel()),r(5),y("Joined ",i.getJoinedDate(),""),r(11),m(i.user.stats.totalObservations),r(6),m(i.user.stats.speciesIdentified),r(7),m(i.user.stats.accurateIdentifications),r(6),m(i.user.stats.contributionScore),r(3),l("ngIf",i.recentObservations.length>0),r(7),l("routerLink",v(15,Oe)),r(8),l("routerLink",v(16,Pe)),r(8),l("routerLink",v(17,be))}}function Ie(s,g){if(s&1){let i=M();e(0,"div",59)(1,"ion-card",60)(2,"ion-card-content")(3,"div",61),c(4,"ion-icon",62),e(5,"h3"),o(6,"Error Loading Profile"),t(),e(7,"p"),o(8),t(),e(9,"ion-button",63),u("click",function(){O(i);let a=f();return b(a.loadProfileData())}),o(10," Try Again "),t()()()()()}if(s&2){let i=f();r(8),m(i.error)}}var Ve=(()=>{let g=class g{constructor(n,a,d,C,P){this.authService=n,this.observationService=a,this.alertController=d,this.toastController=C,this.loadingController=P,this.user=null,this.recentObservations=[],this.isLoading=!0,this.error=null,this.subscriptions=[],oe({person:pe,settings:fe,camera:re,leaf:le,trophy:Ce,statsChart:ve,calendar:ie,location:de,pencil:me,logOut:ge,chevronForward:ae,star:ue,eye:ce,heart:se,share:_e})}ngOnInit(){this.loadProfileData(),this.subscribeToUserChanges()}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe())}subscribeToUserChanges(){let n=this.authService.user$.subscribe(a=>{this.user=a,a&&this.loadUserObservations()});this.subscriptions.push(n)}loadProfileData(){return p(this,null,function*(){this.isLoading=!0,this.error=null;try{this.user&&(yield this.loadUserObservations())}catch(n){this.error=n.message||"Failed to load profile data"}finally{this.isLoading=!1}})}loadUserObservations(){return p(this,null,function*(){try{this.observationService.userObservations$.subscribe(n=>{this.recentObservations=n.slice(0,3)})}catch(n){console.error("Error loading user observations:",n)}})}doRefresh(n){return p(this,null,function*(){yield this.loadProfileData(),n.target.complete()})}editProfile(){return p(this,null,function*(){if(!this.user)return;yield(yield this.alertController.create({header:"Edit Profile",inputs:[{name:"name",type:"text",placeholder:"Display Name",value:this.user.name}],buttons:[{text:"Cancel",role:"cancel"},{text:"Save",handler:a=>p(this,null,function*(){a.name&&a.name.trim()&&(yield this.updateProfile({name:a.name.trim()}))})}]})).present()})}updateProfile(n){return p(this,null,function*(){let a=yield this.loadingController.create({message:"Updating profile...",spinner:"crescent"});yield a.present();try{yield this.authService.updateUserProfile(n),yield(yield this.toastController.create({message:"Profile updated successfully!",duration:2e3,position:"bottom",color:"success"})).present()}catch(d){yield(yield this.toastController.create({message:"Failed to update profile: "+d.message,duration:3e3,position:"bottom",color:"danger"})).present()}finally{yield a.dismiss()}})}confirmSignOut(){return p(this,null,function*(){yield(yield this.alertController.create({header:"Sign Out",message:"Are you sure you want to sign out?",buttons:[{text:"Cancel",role:"cancel"},{text:"Sign Out",role:"destructive",handler:()=>{this.authService.signOut()}}]})).present()})}getJoinedDate(){var a,d;return(d=(a=this.user)==null?void 0:a.stats)!=null&&d.joinDate?this.user.stats.joinDate.toDate().toLocaleDateString("en-US",{year:"numeric",month:"long"}):""}getAchievementLevel(){var a;if(!((a=this.user)!=null&&a.stats))return"Beginner";let n=this.user.stats.totalObservations;return n>=100?"Expert":n>=50?"Advanced":n>=20?"Intermediate":n>=5?"Novice":"Beginner"}getAchievementColor(){switch(this.getAchievementLevel()){case"Expert":return"warning";case"Advanced":return"secondary";case"Intermediate":return"primary";case"Novice":return"tertiary";default:return"medium"}}formatDate(n){return n?(n instanceof Date?n:n.toDate()).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):""}getConfidenceColor(n){return n>=80?"success":n>=60?"warning":"danger"}};g.\u0275fac=function(a){return new(a||g)(_(ne),_(he),_(X),_(ee),_(Z))},g.\u0275cmp=S({type:g,selectors:[["app-profile"]],decls:13,vars:6,consts:[[3,"translucent"],["slot","end"],["fill","clear",3,"click","disabled"],["name","pencil","slot","icon-only"],[3,"fullscreen"],["slot","fixed",3,"ionRefresh"],["class","loading-container",4,"ngIf"],["class","profile-content",4,"ngIf"],["class","error-container",4,"ngIf"],[1,"loading-container"],[1,"profile-header-card"],[1,"profile-header"],["animated","",2,"width","80px","height","80px","border-radius","50%"],[1,"profile-info"],["animated","",2,"width","60%","height","24px"],["animated","",2,"width","40%","height","16px"],[1,"profile-content"],[1,"profile-avatar"],[3,"src","alt"],[1,"profile-name"],[1,"profile-email"],[1,"achievement-chip",3,"color"],["name","trophy"],[1,"profile-meta"],[1,"meta-item"],["name","calendar","color","medium"],[1,"stats-card"],[1,"card-title"],["name","stats-chart","color","primary"],[1,"stats-grid"],["size","6"],[1,"stat-item"],[1,"stat-number"],[1,"stat-label"],["class","observations-card",4,"ngIf"],[1,"actions-card"],["name","settings","color","tertiary"],[1,"actions-list"],["button","",1,"action-item",3,"routerLink"],["name","camera","slot","start","color","primary"],["name","chevron-forward","slot","end","color","medium"],["name","eye","slot","start","color","secondary"],["name","settings","slot","start","color","tertiary"],["button","",1,"action-item","sign-out",3,"click"],["name","log-out","slot","start","color","danger"],[1,"observations-card"],[1,"card-header"],["name","leaf","color","secondary"],["fill","clear","size","small",3,"routerLink"],["name","chevron-forward","slot","end"],[1,"observations-list"],["class","observation-item",3,"routerLink",4,"ngFor","ngForOf"],[1,"observation-item",3,"routerLink"],[1,"observation-image",3,"src","alt"],[1,"observation-info"],[1,"species-name"],[1,"observation-date"],["size","small",3,"color"],["name","chevron-forward","color","medium"],[1,"error-container"],[1,"error-card"],[1,"error-content"],["name","alert-circle","color","danger","size","large"],["fill","outline",3,"click"]],template:function(a,d){a&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),o(3,"Profile"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),u("click",function(){return d.editProfile()}),c(6,"ion-icon",3),t()()()(),e(7,"ion-content",4)(8,"ion-refresher",5),u("ionRefresh",function(P){return d.doRefresh(P)}),c(9,"ion-refresher-content"),t(),h(10,Me,8,0,"div",6)(11,we,91,18,"div",7)(12,Ie,11,1,"div",8),t()),a&2&&(l("translucent",!0),r(5),l("disabled",!d.user),r(2),l("fullscreen",!0),r(3),l("ngIf",d.isLoading),r(),l("ngIf",!d.isLoading&&d.user),r(),l("ngIf",d.error&&!d.isLoading))},dependencies:[E,I,k,z,L,V,B,K,W,F,U,te,T,D,Y,J,$,N,Q,R,j,q,G,H,A],styles:[".profile-content[_ngcontent-%COMP%]{padding:16px 16px 100px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.loading-container[_ngcontent-%COMP%]{padding:16px}.error-container[_ngcontent-%COMP%]{padding:16px;display:flex;justify-content:center;align-items:center;min-height:50vh}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInLeft{0%{opacity:0;transform:translate(-30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_scaleIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}.profile-header-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--ion-color-primary) 0%,var(--ion-color-secondary) 100%);color:#fff;border-radius:20px;margin-bottom:20px;box-shadow:0 8px 32px rgba(var(--ion-color-primary-rgb),.3);animation:_ngcontent-%COMP%_scaleIn .5s ease-out}.profile-header-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:24px}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:20px;margin-bottom:16px}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border:4px solid rgba(255,255,255,.3);box-shadow:0 4px 16px #0003;transition:transform .3s ease}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]{flex:1}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{margin:0 0 4px;font-size:1.5rem;font-weight:700;color:#fff;text-shadow:0 2px 4px rgba(0,0,0,.2)}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-email[_ngcontent-%COMP%]{margin:0 0 12px;color:#fffc;font-size:.9rem}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .achievement-chip[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .2);--color: white;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.3);font-weight:600}.profile-header-card[_ngcontent-%COMP%]   .profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .achievement-chip[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:4px}.profile-header-card[_ngcontent-%COMP%]   .profile-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;color:#ffffffe6;font-size:.9rem}.profile-header-card[_ngcontent-%COMP%]   .profile-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.1rem}.stats-card[_ngcontent-%COMP%]{border-radius:16px;margin-bottom:20px;box-shadow:0 4px 20px #00000014;animation:_ngcontent-%COMP%_slideInLeft .6s ease-out .1s both}.stats-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 20px;font-size:1.2rem;font-weight:600;color:var(--ion-color-dark)}.stats-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.3rem}.stats-card[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]{padding:0}.stats-card[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{text-align:center;padding:16px 8px;border-radius:12px;background:var(--ion-color-light);transition:all .3s ease}.stats-card[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(var(--ion-color-primary-rgb),.15)}.stats-card[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:var(--ion-color-primary);margin-bottom:4px;line-height:1}.stats-card[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.85rem;color:var(--ion-color-medium);font-weight:500;text-transform:uppercase;letter-spacing:.5px}.observations-card[_ngcontent-%COMP%]{border-radius:16px;margin-bottom:20px;box-shadow:0 4px 20px #00000014;animation:_ngcontent-%COMP%_slideInLeft .6s ease-out .2s both}.observations-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.observations-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0;font-size:1.2rem;font-weight:600;color:var(--ion-color-dark)}.observations-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.3rem}.observations-card[_ngcontent-%COMP%]   .observations-list[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px;border-radius:12px;margin-bottom:8px;background:var(--ion-color-light);transition:all .3s ease;cursor:pointer}.observations-card[_ngcontent-%COMP%]   .observations-list[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]:hover{transform:translate(4px);box-shadow:0 4px 12px rgba(var(--ion-color-secondary-rgb),.15);background:var(--ion-color-light-tint)}.observations-card[_ngcontent-%COMP%]   .observations-list[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.observations-card[_ngcontent-%COMP%]   .observations-list[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   .observation-image[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:8px;object-fit:cover;box-shadow:0 2px 8px #0000001a}.observations-card[_ngcontent-%COMP%]   .observations-list[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   .observation-info[_ngcontent-%COMP%]{flex:1}.observations-card[_ngcontent-%COMP%]   .observations-list[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   .observation-info[_ngcontent-%COMP%]   .species-name[_ngcontent-%COMP%]{margin:0 0 4px;font-size:1rem;font-weight:600;color:var(--ion-color-dark);font-style:italic}.observations-card[_ngcontent-%COMP%]   .observations-list[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   .observation-info[_ngcontent-%COMP%]   .observation-date[_ngcontent-%COMP%]{margin:0 0 6px;font-size:.85rem;color:var(--ion-color-medium)}.observations-card[_ngcontent-%COMP%]   .observations-list[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   .observation-info[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{font-size:.75rem;height:24px}.actions-card[_ngcontent-%COMP%]{border-radius:16px;margin-bottom:20px;box-shadow:0 4px 20px #00000014;animation:_ngcontent-%COMP%_slideInLeft .6s ease-out .3s both}.actions-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 16px;font-size:1.2rem;font-weight:600;color:var(--ion-color-dark)}.actions-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.3rem}.actions-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]{background:transparent}.actions-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item[_ngcontent-%COMP%]{--background: var(--ion-color-light);--border-radius: 12px;margin-bottom:8px;transition:all .3s ease}.actions-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item[_ngcontent-%COMP%]:hover{--background: var(--ion-color-light-tint);transform:translate(4px)}.actions-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item.sign-out[_ngcontent-%COMP%]{--background: rgba(var(--ion-color-danger-rgb), .05)}.actions-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item.sign-out[_ngcontent-%COMP%]:hover{--background: rgba(var(--ion-color-danger-rgb), .1)}.actions-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item.sign-out[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-danger)}.actions-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-weight:600;font-size:1rem}.actions-card[_ngcontent-%COMP%]   .actions-list[_ngcontent-%COMP%]   .action-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.85rem;color:var(--ion-color-medium)}.error-card[_ngcontent-%COMP%]{border-radius:16px;border:1px solid var(--ion-color-danger-tint);background:rgba(var(--ion-color-danger-rgb),.05)}.error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]{text-align:center;padding:20px}.error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-bottom:16px}.error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-danger);font-weight:600}.error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 20px;color:var(--ion-color-medium)}@media (max-width: 768px){.profile-content[_ngcontent-%COMP%]{padding:12px}.profile-header[_ngcontent-%COMP%]{flex-direction:column;text-align:center;gap:16px!important}.profile-header[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]{text-align:center}.stats-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{margin-bottom:12px}}@media (prefers-color-scheme: dark){.profile-header-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--ion-color-primary-shade) 0%,var(--ion-color-secondary-shade) 100%)}.stats-card[_ngcontent-%COMP%], .observations-card[_ngcontent-%COMP%], .actions-card[_ngcontent-%COMP%]{background:var(--ion-color-dark);box-shadow:0 4px 20px #0000004d}.stat-item[_ngcontent-%COMP%], .observation-item[_ngcontent-%COMP%], .action-item[_ngcontent-%COMP%]{--background: var(--ion-color-dark-tint) !important}}"]});let s=g;return s})();export{Ve as ProfilePage};
