rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Observations rules
    match /observations/{observationId} {
      // Anyone can read public observations
      allow read: if resource.data.isPublic == true;

      // Users can read their own observations (public or private)
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;

      // Users can create observations
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.userId
        && validateObservation(request.resource.data);

      // Users can update their own observations
      allow update: if request.auth != null
        && request.auth.uid == resource.data.userId
        && validateObservation(request.resource.data);

      // Users can delete their own observations
      allow delete: if request.auth != null && request.auth.uid == resource.data.userId;
    }

    // Comments on observations
    match /observations/{observationId}/comments/{commentId} {
      // Anyone can read comments on public observations
      allow read: if get(/databases/$(database)/documents/observations/$(observationId)).data.isPublic == true;

      // Users can read comments on their own observations
      allow read: if request.auth != null
        && request.auth.uid == get(/databases/$(database)/documents/observations/$(observationId)).data.userId;

      // Authenticated users can create comments
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.userId
        && validateComment(request.resource.data);

      // Users can update their own comments
      allow update: if request.auth != null && request.auth.uid == resource.data.userId;

      // Users can delete their own comments
      allow delete: if request.auth != null && request.auth.uid == resource.data.userId;
    }

    // Likes on observations
    match /observations/{observationId}/likes/{likeId} {
      // Anyone can read likes on public observations
      allow read: if get(/databases/$(database)/documents/observations/$(observationId)).data.isPublic == true;

      // Users can create/delete their own likes
      allow create, delete: if request.auth != null && request.auth.uid == resource.data.userId;
    }

    // Identification history (user-specific)
    match /users/{userId}/identifications/{identificationId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Global identification logs (for analytics)
    match /identifications/{identificationId} {
      // Only allow reads for authenticated users (for their own data)
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;

      // Allow Firebase Functions to write identification logs
      allow create: if request.auth != null;
    }

    // User preferences and settings
    match /users/{userId}/preferences/{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Public collections (read-only)
    match /species/{speciesId} {
      allow read: if true;
    }

    match /flora-regions/{regionId} {
      allow read: if true;
    }

    // Validation functions
    function validateObservation(data) {
      return data.keys().hasAll(['userId', 'species', 'location', 'createdAt', 'isPublic'])
        && data.userId is string
        && data.species is map
        && data.location is map
        && data.createdAt is timestamp
        && data.isPublic is bool;
    }

    function validateComment(data) {
      return data.keys().hasAll(['userId', 'content', 'createdAt'])
        && data.userId is string
        && data.content is string
        && data.content.size() > 0
        && data.content.size() <= 1000
        && data.createdAt is timestamp;
    }
  }
}
