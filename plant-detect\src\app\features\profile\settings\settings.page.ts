import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonCard,
  IonCardContent,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonToggle,
  IonSelect,
  IonSelectOption,
  IonButtons,
  IonBackButton,
  ToastController,
  AlertController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import {
  construct,
  settings,
  chevronForward,
  notifications,
  globe,
  moon,
  language,
  shield,
  help,
  information,
  logOut,
  person,
  leaf
} from 'ionicons/icons';
import { AuthService } from '../../../core/services/auth.service';
import { User, UserPreferences } from '../../../models/user.model';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.page.html',
  styleUrls: ['./settings.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonCard,
    IonCardContent,
    IonIcon,
    IonList,
    IonItem,
    IonLabel,
    IonToggle,
    IonSelect,
    IonSelectOption,
    IonButtons,
    IonBackButton
  ]
})
export class SettingsPage implements OnInit {
  user: User | null = null;
  preferences: UserPreferences | null = null;

  constructor(
    private authService: AuthService,
    private toastController: ToastController,
    private alertController: AlertController
  ) {
    addIcons({
      construct,
      settings,
      chevronForward,
      notifications,
      globe,
      moon,
      language,
      shield,
      help,
      information,
      logOut,
      person,
      leaf
    });
  }

  ngOnInit() {
    this.loadUserData();
  }

  private loadUserData() {
    this.authService.user$.subscribe(user => {
      this.user = user;
      this.preferences = user?.preferences || null;
    });
  }

  async updatePreference(key: keyof UserPreferences, value: any) {
    if (!this.user || !this.preferences) return;

    try {
      const updatedPreferences = {
        ...this.preferences,
        [key]: value
      };

      await this.authService.updateUserProfile({
        preferences: updatedPreferences
      });

      const toast = await this.toastController.create({
        message: 'Settings updated successfully',
        duration: 2000,
        position: 'bottom',
        color: 'success'
      });
      await toast.present();

    } catch (error: any) {
      const toast = await this.toastController.create({
        message: 'Failed to update settings: ' + error.message,
        duration: 3000,
        position: 'bottom',
        color: 'danger'
      });
      await toast.present();
    }
  }

  async updateNotificationSetting(key: keyof UserPreferences['notifications'], value: boolean) {
    if (!this.user || !this.preferences) return;

    const updatedNotifications = {
      ...this.preferences.notifications,
      [key]: value
    };

    await this.updatePreference('notifications', updatedNotifications);
  }

  async confirmSignOut() {
    const alert = await this.alertController.create({
      header: 'Sign Out',
      message: 'Are you sure you want to sign out?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Sign Out',
          role: 'destructive',
          handler: () => {
            this.authService.signOut();
          }
        }
      ]
    });

    await alert.present();
  }

  async showAbout() {
    const alert = await this.alertController.create({
      header: 'About Plant Detect',
      message: `
        <p><strong>Version:</strong> 1.0.0</p>
        <p><strong>Powered by:</strong> PlantNet API</p>
        <p><strong>Built with:</strong> Ionic & Angular</p>
        <br>
        <p>Plant Detect helps you identify plants using AI-powered image recognition. Take a photo of any plant and get instant identification results with detailed information about the species.</p>
      `,
      buttons: ['OK']
    });

    await alert.present();
  }
}
