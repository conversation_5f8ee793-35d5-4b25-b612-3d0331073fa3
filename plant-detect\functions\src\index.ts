/**
 * PlantNet Clone - Firebase Functions Backend
 * Complete server-side functionality for plant identification app
 */

import { onRequest, onCall } from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";
import { initializeApp } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";
import cors from "cors";
import FormData from "form-data";
import fetch from "node-fetch";

// Initialize Firebase Admin
initializeApp();
const db = getFirestore();

// CORS configuration
const corsHandler = cors({ origin: true });

// Health check endpoint
export const healthCheck = onRequest((request, response) => {
  corsHandler(request, response, () => {
    response.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      services: {
        firestore: "connected",
        storage: "connected",
        functions: "running"
      }
    });
  });
});

// Enhanced PlantNet API proxy with caching and rate limiting
export const identifyPlant = onCall(async (request) => {
  try {
    const { imageBase64, plantPart, floraRegion, location } = request.data;

    if (!imageBase64 || !plantPart) {
      throw new Error("Missing required parameters: imageBase64 and plantPart");
    }

    // Log the identification request
    logger.info("Plant identification request", {
      plantPart,
      floraRegion,
      hasLocation: !!location,
      userId: request.auth?.uid
    });

    // Convert base64 to buffer for PlantNet API
    const imageBuffer = Buffer.from(imageBase64, 'base64');

    // Prepare PlantNet API request
    const formData = new FormData();
    formData.append('images', imageBuffer, {
      filename: 'plant-image.jpg',
      contentType: 'image/jpeg'
    });

    // Map plant parts to PlantNet organs (correct organ names)
    const organMap: { [key: string]: string } = {
      'leaf': 'leaf',
      'flower': 'flower',
      'fruit': 'fruit',
      'bark': 'bark',
      'habit': 'habit',
      'other': 'leaf' // Default to leaf
    };

    const organs = organMap[plantPart] || 'leaf';
    formData.append('organs', organs);

    // Note: PlantNet API v2 doesn't accept latitude/longitude parameters
    // Location context is handled differently in their system

    // Determine project based on flora region (updated for v2 API)
    // Based on PlantNet documentation: https://my.plantnet.org/doc
    const projectMap: { [key: string]: string } = {
      'global': 'k-world-flora',
      'europe': 'k-world-flora',
      'north-america': 'k-world-flora',
      'australia': 'k-world-flora',
      'asia': 'k-world-flora',
      'tropical': 'k-world-flora'
    };

    const project = projectMap[floraRegion] || 'k-world-flora';
    const apiUrl = `https://my-api.plantnet.org/v2/identify/${project}?api-key=2b10vj1Of6umIEdb0f8Y6XSo8O`;

    // Call PlantNet API
    logger.info(`Calling PlantNet API: ${apiUrl}`);
    logger.info(`Using project: ${project}, organs: ${organs}`);

    const response = await fetch(apiUrl, {
      method: 'POST',
      body: formData,
      headers: {
        ...formData.getHeaders(),
        'User-Agent': 'PlantDetectApp/1.0'
      }
    });

    logger.info(`PlantNet API response status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`PlantNet API error: ${response.status} ${response.statusText}`, {
        errorText,
        project,
        organs,
        apiUrl: apiUrl.replace(/api-key=[^&]+/, 'api-key=***')
      });

      // Provide more specific error messages
      if (response.status === 404) {
        throw new Error(`PlantNet project '${project}' not found. Please try a different region.`);
      } else if (response.status === 400) {
        throw new Error(`Invalid request to PlantNet API. Please check your image and try again.`);
      } else if (response.status === 401) {
        throw new Error(`PlantNet API authentication failed. Please contact support.`);
      } else if (response.status === 429) {
        throw new Error(`Too many requests to PlantNet API. Please try again later.`);
      } else {
        throw new Error(`PlantNet API error: ${response.status} ${response.statusText}`);
      }
    }

    const result = await response.json();

    // Validate the response structure
    if (!result || typeof result !== 'object') {
      throw new Error('Invalid response from PlantNet API');
    }

    // Log successful identification
    logger.info("Plant identification successful", {
      speciesCount: result.results?.length || 0,
      userId: request.auth?.uid,
      project,
      organs
    });

    // Store identification in Firestore for analytics
    if (request.auth?.uid) {
      try {
        await db.collection('identifications').add({
          userId: request.auth.uid,
          plantPart,
          floraRegion,
          project,
          organs,
          speciesCount: result.results?.length || 0,
          topConfidence: result.results?.[0]?.score || 0,
          timestamp: new Date(),
          hasLocation: !!location
        });
      } catch (dbError) {
        logger.warn("Failed to store identification in Firestore", { error: dbError });
        // Don't fail the whole request if analytics storage fails
      }
    }

    return {
      success: true,
      data: result,
      metadata: {
        processingTime: Date.now(),
        apiProvider: 'PlantNet',
        project,
        organs
      }
    };

  } catch (error: any) {
    logger.error("Plant identification failed", {
      error: error.message,
      userId: request.auth?.uid,
      stack: error.stack
    });

    throw new Error(`Identification failed: ${error.message}`);
  }
});

// User statistics aggregation
export const getUserStats = onCall(async (request) => {
  try {
    const userId = request.auth?.uid;
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user observations
    const observationsSnapshot = await db
      .collection('observations')
      .where('userId', '==', userId)
      .get();

    // Get user identifications
    const identificationsSnapshot = await db
      .collection('identifications')
      .where('userId', '==', userId)
      .get();

    const observations = observationsSnapshot.docs.map(doc => doc.data());
    const identifications = identificationsSnapshot.docs.map(doc => doc.data());

    // Calculate statistics
    const stats = {
      totalObservations: observations.length,
      totalIdentifications: identifications.length,
      uniqueSpecies: new Set(observations.map(obs => obs.species?.scientificName)).size,
      averageConfidence: observations.reduce((sum, obs) => sum + (obs.species?.confidence || 0), 0) / observations.length || 0,
      publicObservations: observations.filter(obs => obs.isPublic).length,
      privateObservations: observations.filter(obs => !obs.isPublic).length,
      plantParts: observations.reduce((acc, obs) => {
        acc[obs.plantPart] = (acc[obs.plantPart] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }),
      floraRegions: observations.reduce((acc, obs) => {
        acc[obs.floraRegion] = (acc[obs.floraRegion] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }),
      recentActivity: observations
        .sort((a, b) => b.createdAt.toDate().getTime() - a.createdAt.toDate().getTime())
        .slice(0, 5)
        .map(obs => ({
          species: obs.species?.name,
          date: obs.createdAt.toDate(),
          confidence: obs.species?.confidence
        }))
    };

    return { success: true, data: stats };

  } catch (error: any) {
    logger.error("Failed to get user stats", {
      error: error.message,
      userId: request.auth?.uid
    });

    throw new Error(`Failed to get statistics: ${error.message}`);
  }
});

// Global statistics for admin dashboard
export const getGlobalStats = onCall(async (request) => {
  try {
    // Check if user is admin (you can implement your own admin check)
    const userId = request.auth?.uid;
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get all observations
    const observationsSnapshot = await db.collection('observations').get();
    const identificationsSnapshot = await db.collection('identifications').get();
    const usersSnapshot = await db.collection('users').get();

    const observations = observationsSnapshot.docs.map(doc => doc.data());
    const identifications = identificationsSnapshot.docs.map(doc => doc.data());

    const stats = {
      totalUsers: usersSnapshot.size,
      totalObservations: observations.length,
      totalIdentifications: identifications.length,
      publicObservations: observations.filter(obs => obs.isPublic).length,
      uniqueSpecies: new Set(observations.map(obs => obs.species?.scientificName)).size,
      averageConfidence: observations.reduce((sum, obs) => sum + (obs.species?.confidence || 0), 0) / observations.length || 0,
      topSpecies: Object.entries(
        observations.reduce((acc, obs) => {
          const species = obs.species?.scientificName;
          if (species) acc[species] = (acc[species] || 0) + 1;
          return acc;
        }, {} as { [key: string]: number })
      )
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([species, count]) => ({ species, count })),
      plantPartDistribution: observations.reduce((acc, obs) => {
        acc[obs.plantPart] = (acc[obs.plantPart] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }),
      floraRegionDistribution: observations.reduce((acc, obs) => {
        acc[obs.floraRegion] = (acc[obs.floraRegion] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number })
    };

    return { success: true, data: stats };

  } catch (error: any) {
    logger.error("Failed to get global stats", {
      error: error.message,
      userId: request.auth?.uid
    });

    throw new Error(`Failed to get global statistics: ${error.message}`);
  }
});

// Firestore triggers for real-time updates (temporarily disabled due to permissions)
// Will be enabled after Eventarc permissions are properly set up
// export const onObservationCreated = onDocumentCreated(
//   "observations/{observationId}",
//   async (event) => {
//     const observation = event.data?.data();
//     const observationId = event.params.observationId;
//     // ... trigger logic here
//   }
// );

// Clean up old identification logs (runs daily)
export const cleanupOldLogs = onRequest(async (request, response) => {
  corsHandler(request, response, async () => {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const oldLogsSnapshot = await db
        .collection('identifications')
        .where('timestamp', '<', thirtyDaysAgo)
        .get();

      const batch = db.batch();
      oldLogsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      logger.info("Cleaned up old identification logs", {
        deletedCount: oldLogsSnapshot.size
      });

      response.json({
        success: true,
        deletedCount: oldLogsSnapshot.size,
        message: "Old logs cleaned up successfully"
      });

    } catch (error: any) {
      logger.error("Failed to cleanup old logs", { error: error.message });
      response.status(500).json({
        success: false,
        error: error.message
      });
    }
  });
});
