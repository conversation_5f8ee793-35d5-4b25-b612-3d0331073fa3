import{A as D,C as q,D as F,E as x,F as M,G as u,H as p,I as k,K as C,L as g,M as N,N as d,O as b,P as y,R as A,S as v,T as U,U as T,V as j,W as I,X as z,Z as B,_ as G,aa as L,ba as H,w as O,x as W}from"./chunk-YUFJ6257.js";import{b as m,h as E,i as l,k as w,l as P,m as f,na as $,o as h}from"./chunk-DVLCYDDQ.js";import{a as n,b as S,g as a}from"./chunk-OLRFWS6T.js";var J=(()=>{let s=class s{constructor(t,e,r){this.auth=t,this.firestore=e,this.storage=r,this.currentUserSubject=new m(null),this.currentUser$=this.currentUserSubject.asObservable(),q(this.auth,i=>{this.currentUserSubject.next(i)})}signInWithEmail(t,e){return a(this,null,function*(){return(yield F(this.auth,t,e)).user})}signUpWithEmail(t,e){return a(this,null,function*(){return(yield D(this.auth,t,e)).user})}signInWithGoogle(){return a(this,null,function*(){let t=new O;return(yield x(this.auth,t)).user})}signOut(){return a(this,null,function*(){yield M(this.auth)})}getCurrentUser(){return this.auth.currentUser}createUserDocument(t,e){return a(this,null,function*(){let r=d(this.firestore,"users",t),i={uid:t,name:e.name,email:e.email,avatar:"",preferences:n({defaultFlora:"global",isPublic:!1,units:"metric",language:"en",theme:"auto",notifications:{newSpecies:!0,communityUpdates:!0,observationComments:!0,systemUpdates:!0}},e.preferences),stats:{totalObservations:0,speciesIdentified:0,accurateIdentifications:0,contributionScore:0,joinDate:u.now(),lastActiveDate:u.now()},createdAt:u.now(),updatedAt:u.now()};yield T(r,i)})}getUserDocument(t){return a(this,null,function*(){let e=d(this.firestore,"users",t),r=yield b(e);return r.exists()?r.data():null})}updateUserDocument(t,e){return a(this,null,function*(){let r=d(this.firestore,"users",t);yield j(r,S(n({},e),{updatedAt:p()}))})}createObservation(t){return a(this,null,function*(){let e=g(this.firestore,"observations"),r=yield this.uploadImage(t.image,"observations"),i={userId:this.auth.currentUser.uid,species:t.species,image:{url:r,thumbnailUrl:r,metadata:{width:0,height:0,size:t.image.size,format:t.image.type}},location:t.location,plantPart:t.plantPart,floraRegion:t.floraRegion,isPublic:t.isPublic||!1,tags:t.tags||[],notes:t.notes||"",createdAt:u.now(),updatedAt:u.now()};return(yield C(e,i)).id})}getObservation(t){return a(this,null,function*(){let e=d(this.firestore,"observations",t),r=yield b(e);return r.exists()?n({id:r.id},r.data()):null})}getUserObservations(t,e=20){return a(this,null,function*(){let r=g(this.firestore,"observations"),i=U(r,I("userId","==",t),v("createdAt","desc"),A(e));return(yield y(i)).docs.map(R=>n({id:R.id},R.data()))})}getPublicObservations(t=20){return a(this,null,function*(){let e=g(this.firestore,"observations"),r=U(e,I("isPublic","==",!0),v("createdAt","desc"),A(t));return(yield y(r)).docs.map(o=>n({id:o.id},o.data()))})}updateObservation(t,e){return a(this,null,function*(){let r=d(this.firestore,"observations",t);yield j(r,S(n({},e),{updatedAt:p()}))})}deleteObservation(t){return a(this,null,function*(){let e=d(this.firestore,"observations",t);yield N(e)})}uploadImage(t,e){return a(this,null,function*(){let r=`${Date.now()}_${t.name}`,i=`${e}/${r}`,o=L(this.storage,i);return yield H(o,t),yield G(o)})}deleteImage(t){return a(this,null,function*(){let e=L(this.storage,t);yield B(e)})}getTimestamp(){return u.now()}getServerTimestamp(){return p()}};s.\u0275fac=function(e){return new(e||s)(h(W),h(k),h(z))},s.\u0275prov=f({token:s,factory:s.\u0275fac,providedIn:"root"});let c=s;return c})();var nt=(()=>{let s=class s{constructor(t,e){this.firebaseService=t,this.router=e,this.authStateSubject=new m({user:null,firebaseUser:null,isAuthenticated:!1,isLoading:!0,error:null}),this.authState$=this.authStateSubject.asObservable(),this.user$=this.authState$.pipe(l(r=>r.user)),this.isAuthenticated$=this.authState$.pipe(l(r=>r.isAuthenticated)),this.isLoading$=this.authState$.pipe(l(r=>r.isLoading)),this.initializeAuthState()}initializeAuthState(){this.firebaseService.currentUser$.subscribe(t=>a(this,null,function*(){if(this.updateAuthState({isLoading:!0,error:null}),t)try{let e=yield this.firebaseService.getUserDocument(t.uid);e?this.updateAuthState({user:e,firebaseUser:t,isAuthenticated:!0,isLoading:!1,error:null}):yield this.createUserDocument(t)}catch(e){console.error("Error loading user data:",e),this.updateAuthState({user:null,firebaseUser:null,isAuthenticated:!1,isLoading:!1,error:"Failed to load user data"})}else this.updateAuthState({user:null,firebaseUser:null,isAuthenticated:!1,isLoading:!1,error:null})}))}updateAuthState(t){let e=this.authStateSubject.value;this.authStateSubject.next(n(n({},e),t))}createUserDocument(t){return a(this,null,function*(){let e={name:t.displayName||"Anonymous User",email:t.email||""};yield this.firebaseService.createUserDocument(t.uid,e);let r=yield this.firebaseService.getUserDocument(t.uid);this.updateAuthState({user:r,firebaseUser:t,isAuthenticated:!0,isLoading:!1,error:null})})}signInWithEmail(t,e){return a(this,null,function*(){try{this.updateAuthState({isLoading:!0,error:null}),yield this.firebaseService.signInWithEmail(t,e)}catch(r){throw this.updateAuthState({isLoading:!1,error:this.getErrorMessage(r)}),r}})}signUpWithEmail(t,e,r){return a(this,null,function*(){try{this.updateAuthState({isLoading:!0,error:null});let i=yield this.firebaseService.signUpWithEmail(t,e),o={name:r,email:t};yield this.firebaseService.createUserDocument(i.uid,o)}catch(i){throw this.updateAuthState({isLoading:!1,error:this.getErrorMessage(i)}),i}})}signInWithGoogle(){return a(this,null,function*(){try{this.updateAuthState({isLoading:!0,error:null}),yield this.firebaseService.signInWithGoogle()}catch(t){throw this.updateAuthState({isLoading:!1,error:this.getErrorMessage(t)}),t}})}signOut(){return a(this,null,function*(){try{this.updateAuthState({isLoading:!0,error:null}),yield this.firebaseService.signOut(),yield this.router.navigate(["/login"])}catch(t){throw this.updateAuthState({isLoading:!1,error:this.getErrorMessage(t)}),t}})}updateUserProfile(t){return a(this,null,function*(){let e=this.authStateSubject.value;if(!e.user)throw new Error("No user logged in");try{yield this.firebaseService.updateUserDocument(e.user.uid,t);let r=n(n({},e.user),t);this.updateAuthState({user:r})}catch(r){throw this.updateAuthState({error:this.getErrorMessage(r)}),r}})}getCurrentUser(){return this.authStateSubject.value.user}getCurrentFirebaseUser(){return this.authStateSubject.value.firebaseUser}isAuthenticated(){return this.authStateSubject.value.isAuthenticated}getErrorMessage(t){if(t.code)switch(t.code){case"auth/user-not-found":return"No user found with this email address.";case"auth/wrong-password":return"Incorrect password.";case"auth/email-already-in-use":return"An account with this email already exists.";case"auth/weak-password":return"Password should be at least 6 characters.";case"auth/invalid-email":return"Invalid email address.";case"auth/network-request-failed":return"Network error. Please check your connection.";case"auth/too-many-requests":return"Too many failed attempts. Please try again later.";default:return t.message||"An authentication error occurred."}return t.message||"An unexpected error occurred."}waitForAuthState(){return this.authState$.pipe(l(t=>!t.isLoading),w(t=>t?E(!0):this.waitForAuthState()))}requireAuth(){return this.waitForAuthState().pipe(w(()=>this.isAuthenticated$),P(t=>{t||this.router.navigate(["/login"])}))}};s.\u0275fac=function(e){return new(e||s)(h(J),h($))},s.\u0275prov=f({token:s,factory:s.\u0275fac,providedIn:"root"});let c=s;return c})();export{J as a,nt as b};
