<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/profile"></ion-back-button>
    </ion-buttons>
    <ion-title>Settings</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="settings-content">

    <!-- User Preferences -->
    <ion-card class="preferences-card" *ngIf="preferences">
      <ion-card-content>
        <h3 class="card-title">
          <ion-icon name="person" color="primary"></ion-icon>
          Preferences
        </h3>

        <ion-list class="preferences-list">
          <!-- Default Flora -->
          <ion-item>
            <ion-icon name="leaf" slot="start" color="secondary"></ion-icon>
            <ion-label>
              <h3>Default Flora Region</h3>
              <p>Choose your preferred flora region for identification</p>
            </ion-label>
            <ion-select
              [value]="preferences.defaultFlora"
              (ionChange)="updatePreference('defaultFlora', $event.detail.value)"
              interface="popover"
              placeholder="Select Flora">
              <ion-select-option value="global">Global Flora</ion-select-option>
              <ion-select-option value="asia">Asian Flora</ion-select-option>
              <ion-select-option value="europe">European Flora</ion-select-option>
              <ion-select-option value="north-america">North American Flora</ion-select-option>
              <ion-select-option value="south-america">South American Flora</ion-select-option>
              <ion-select-option value="africa">African Flora</ion-select-option>
              <ion-select-option value="oceania">Oceanian Flora</ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Theme -->
          <ion-item>
            <ion-icon name="moon" slot="start" color="tertiary"></ion-icon>
            <ion-label>
              <h3>Theme</h3>
              <p>Choose your preferred app theme</p>
            </ion-label>
            <ion-select
              [value]="preferences.theme"
              (ionChange)="updatePreference('theme', $event.detail.value)"
              interface="popover"
              placeholder="Select Theme">
              <ion-select-option value="light">Light</ion-select-option>
              <ion-select-option value="dark">Dark</ion-select-option>
              <ion-select-option value="auto">Auto (System)</ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Language -->
          <ion-item>
            <ion-icon name="language" slot="start" color="warning"></ion-icon>
            <ion-label>
              <h3>Language</h3>
              <p>Choose your preferred language</p>
            </ion-label>
            <ion-select
              [value]="preferences.language"
              (ionChange)="updatePreference('language', $event.detail.value)"
              interface="popover"
              placeholder="Select Language">
              <ion-select-option value="en">English</ion-select-option>
              <ion-select-option value="es">Español</ion-select-option>
              <ion-select-option value="fr">Français</ion-select-option>
              <ion-select-option value="de">Deutsch</ion-select-option>
              <ion-select-option value="it">Italiano</ion-select-option>
              <ion-select-option value="pt">Português</ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Units -->
          <ion-item>
            <ion-icon name="globe" slot="start" color="success"></ion-icon>
            <ion-label>
              <h3>Units</h3>
              <p>Choose your preferred measurement units</p>
            </ion-label>
            <ion-select
              [value]="preferences.units"
              (ionChange)="updatePreference('units', $event.detail.value)"
              interface="popover"
              placeholder="Select Units">
              <ion-select-option value="metric">Metric</ion-select-option>
              <ion-select-option value="imperial">Imperial</ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Public Profile -->
          <ion-item>
            <ion-icon name="shield" slot="start" color="medium"></ion-icon>
            <ion-label>
              <h3>Public Profile</h3>
              <p>Make your observations visible to other users</p>
            </ion-label>
            <ion-toggle
              [checked]="preferences.isPublic"
              (ionChange)="updatePreference('isPublic', $event.detail.checked)">
            </ion-toggle>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Notifications -->
    <ion-card class="notifications-card" *ngIf="preferences?.notifications">
      <ion-card-content>
        <h3 class="card-title">
          <ion-icon name="notifications" color="primary"></ion-icon>
          Notifications
        </h3>

        <ion-list class="notifications-list">
          <ion-item>
            <ion-label>
              <h3>New Species Alerts</h3>
              <p>Get notified when new species are added to the database</p>
            </ion-label>
            <ion-toggle
              [checked]="preferences?.notifications?.newSpecies || false"
              (ionChange)="updateNotificationSetting('newSpecies', $event.detail.checked)">
            </ion-toggle>
          </ion-item>

          <ion-item>
            <ion-label>
              <h3>Community Updates</h3>
              <p>Receive updates about community activities and features</p>
            </ion-label>
            <ion-toggle
              [checked]="preferences?.notifications?.communityUpdates || false"
              (ionChange)="updateNotificationSetting('communityUpdates', $event.detail.checked)">
            </ion-toggle>
          </ion-item>

          <ion-item>
            <ion-label>
              <h3>Observation Comments</h3>
              <p>Get notified when someone comments on your observations</p>
            </ion-label>
            <ion-toggle
              [checked]="preferences?.notifications?.observationComments || false"
              (ionChange)="updateNotificationSetting('observationComments', $event.detail.checked)">
            </ion-toggle>
          </ion-item>

          <ion-item>
            <ion-label>
              <h3>System Updates</h3>
              <p>Receive important app updates and maintenance notifications</p>
            </ion-label>
            <ion-toggle
              [checked]="preferences?.notifications?.systemUpdates || false"
              (ionChange)="updateNotificationSetting('systemUpdates', $event.detail.checked)">
            </ion-toggle>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- App Settings -->
    <ion-card class="app-settings-card">
      <ion-card-content>
        <h3 class="card-title">
          <ion-icon name="settings" color="primary"></ion-icon>
          App Settings
        </h3>

        <ion-list class="app-settings-list">
          <ion-item button [routerLink]="['/test-config']">
            <ion-icon name="construct" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Test Configuration</h3>
              <p>Test Firebase and PlantNet API connections</p>
            </ion-label>
            <ion-icon name="chevron-forward" slot="end" color="medium"></ion-icon>
          </ion-item>

          <ion-item button (click)="showAbout()">
            <ion-icon name="information" slot="start" color="tertiary"></ion-icon>
            <ion-label>
              <h3>About</h3>
              <p>App version and information</p>
            </ion-label>
            <ion-icon name="chevron-forward" slot="end" color="medium"></ion-icon>
          </ion-item>

          <ion-item button (click)="confirmSignOut()" class="sign-out-item">
            <ion-icon name="log-out" slot="start" color="danger"></ion-icon>
            <ion-label>
              <h3>Sign Out</h3>
              <p>Sign out of your account</p>
            </ion-label>
            <ion-icon name="chevron-forward" slot="end" color="medium"></ion-icon>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

  </div>
</ion-content>
