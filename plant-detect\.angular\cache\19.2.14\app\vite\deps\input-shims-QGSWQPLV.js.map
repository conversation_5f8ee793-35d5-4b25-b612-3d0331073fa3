{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/input-shims.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win, d as doc } from './index6.js';\nimport { g as getScrollElement, c as scrollByPoint, a as findClosestIonContent } from './index8.js';\nimport { a as addEventListener, b as removeEventListener, r as raf, c as componentOnReady } from './helpers.js';\nimport { a as KeyboardResize, K as Keyboard } from './keyboard.js';\nconst cloneMap = new WeakMap();\nconst relocateInput = (componentEl, inputEl, shouldRelocate, inputRelativeY = 0, disabledClonedInput = false) => {\n  if (cloneMap.has(componentEl) === shouldRelocate) {\n    return;\n  }\n  if (shouldRelocate) {\n    addClone(componentEl, inputEl, inputRelativeY, disabledClonedInput);\n  } else {\n    removeClone(componentEl, inputEl);\n  }\n};\nconst isFocused = input => {\n  /**\n   * https://developer.mozilla.org/en-US/docs/Web/API/Node/getRootNode\n   * Calling getRootNode on an element in standard web page will return HTMLDocument.\n   * Calling getRootNode on an element inside of the Shadow DOM will return the associated ShadowRoot.\n   * Calling getRootNode on an element that is not attached to a document/shadow tree will return\n   * the root of the DOM tree it belongs to.\n   * isFocused is used for the hide-caret utility which only considers input/textarea elements\n   * that are present in the DOM, so we don't set types for that final case since it does not apply.\n   */\n  return input === input.getRootNode().activeElement;\n};\nconst addClone = (componentEl, inputEl, inputRelativeY, disabledClonedInput = false) => {\n  // this allows for the actual input to receive the focus from\n  // the user's touch event, but before it receives focus, it\n  // moves the actual input to a location that will not screw\n  // up the app's layout, and does not allow the native browser\n  // to attempt to scroll the input into place (messing up headers/footers)\n  // the cloned input fills the area of where native input should be\n  // while the native input fakes out the browser by relocating itself\n  // before it receives the actual focus event\n  // We hide the focused input (with the visible caret) invisible by making it scale(0),\n  const parentEl = inputEl.parentNode;\n  // DOM WRITES\n  const clonedEl = inputEl.cloneNode(false);\n  clonedEl.classList.add('cloned-input');\n  clonedEl.tabIndex = -1;\n  /**\n   * Making the cloned input disabled prevents\n   * Chrome for Android from still scrolling\n   * the entire page since this cloned input\n   * will briefly be hidden by the keyboard\n   * even though it is not focused.\n   *\n   * This is not needed on iOS. While this\n   * does not cause functional issues on iOS,\n   * the input still appears slightly dimmed even\n   * if we set opacity: 1.\n   */\n  if (disabledClonedInput) {\n    clonedEl.disabled = true;\n  }\n  parentEl.appendChild(clonedEl);\n  cloneMap.set(componentEl, clonedEl);\n  const doc = componentEl.ownerDocument;\n  const tx = doc.dir === 'rtl' ? 9999 : -9999;\n  componentEl.style.pointerEvents = 'none';\n  inputEl.style.transform = `translate3d(${tx}px,${inputRelativeY}px,0) scale(0)`;\n};\nconst removeClone = (componentEl, inputEl) => {\n  const clone = cloneMap.get(componentEl);\n  if (clone) {\n    cloneMap.delete(componentEl);\n    clone.remove();\n  }\n  componentEl.style.pointerEvents = '';\n  inputEl.style.transform = '';\n};\n/**\n * Factoring in 50px gives us some room\n * in case the keyboard shows password/autofill bars\n * asynchronously.\n */\nconst SCROLL_AMOUNT_PADDING = 50;\nconst enableHideCaretOnScroll = (componentEl, inputEl, scrollEl) => {\n  if (!scrollEl || !inputEl) {\n    return () => {\n      return;\n    };\n  }\n  const scrollHideCaret = shouldHideCaret => {\n    if (isFocused(inputEl)) {\n      relocateInput(componentEl, inputEl, shouldHideCaret);\n    }\n  };\n  const onBlur = () => relocateInput(componentEl, inputEl, false);\n  const hideCaret = () => scrollHideCaret(true);\n  const showCaret = () => scrollHideCaret(false);\n  addEventListener(scrollEl, 'ionScrollStart', hideCaret);\n  addEventListener(scrollEl, 'ionScrollEnd', showCaret);\n  inputEl.addEventListener('blur', onBlur);\n  return () => {\n    removeEventListener(scrollEl, 'ionScrollStart', hideCaret);\n    removeEventListener(scrollEl, 'ionScrollEnd', showCaret);\n    inputEl.removeEventListener('blur', onBlur);\n  };\n};\nconst SKIP_SELECTOR = 'input, textarea, [no-blur], [contenteditable]';\nconst enableInputBlurring = () => {\n  let focused = true;\n  let didScroll = false;\n  const doc = document;\n  const onScroll = () => {\n    didScroll = true;\n  };\n  const onFocusin = () => {\n    focused = true;\n  };\n  const onTouchend = ev => {\n    // if app did scroll return early\n    if (didScroll) {\n      didScroll = false;\n      return;\n    }\n    const active = doc.activeElement;\n    if (!active) {\n      return;\n    }\n    // only blur if the active element is a text-input or a textarea\n    if (active.matches(SKIP_SELECTOR)) {\n      return;\n    }\n    // if the selected target is the active element, do not blur\n    const tapped = ev.target;\n    if (tapped === active) {\n      return;\n    }\n    if (tapped.matches(SKIP_SELECTOR) || tapped.closest(SKIP_SELECTOR)) {\n      return;\n    }\n    focused = false;\n    // TODO FW-2796: find a better way, why 50ms?\n    setTimeout(() => {\n      if (!focused) {\n        active.blur();\n      }\n    }, 50);\n  };\n  addEventListener(doc, 'ionScrollStart', onScroll);\n  doc.addEventListener('focusin', onFocusin, true);\n  doc.addEventListener('touchend', onTouchend, false);\n  return () => {\n    removeEventListener(doc, 'ionScrollStart', onScroll, true);\n    doc.removeEventListener('focusin', onFocusin, true);\n    doc.removeEventListener('touchend', onTouchend, false);\n  };\n};\nconst SCROLL_ASSIST_SPEED = 0.3;\nconst getScrollData = (componentEl, contentEl, keyboardHeight, platformHeight) => {\n  var _a;\n  const itemEl = (_a = componentEl.closest('ion-item,[ion-item]')) !== null && _a !== void 0 ? _a : componentEl;\n  return calcScrollData(itemEl.getBoundingClientRect(), contentEl.getBoundingClientRect(), keyboardHeight, platformHeight);\n};\nconst calcScrollData = (inputRect, contentRect, keyboardHeight, platformHeight) => {\n  // compute input's Y values relative to the body\n  const inputTop = inputRect.top;\n  const inputBottom = inputRect.bottom;\n  // compute visible area\n  const visibleAreaTop = contentRect.top;\n  const visibleAreaBottom = Math.min(contentRect.bottom, platformHeight - keyboardHeight);\n  // compute safe area\n  const safeAreaTop = visibleAreaTop + 15;\n  const safeAreaBottom = visibleAreaBottom - SCROLL_AMOUNT_PADDING;\n  // figure out if each edge of the input is within the safe area\n  const distanceToBottom = safeAreaBottom - inputBottom;\n  const distanceToTop = safeAreaTop - inputTop;\n  // desiredScrollAmount is the negated distance to the safe area according to our calculations.\n  const desiredScrollAmount = Math.round(distanceToBottom < 0 ? -distanceToBottom : distanceToTop > 0 ? -distanceToTop : 0);\n  // our calculations make some assumptions that aren't always true, like the keyboard being closed when an input\n  // gets focus, so make sure we don't scroll the input above the visible area\n  const scrollAmount = Math.min(desiredScrollAmount, inputTop - visibleAreaTop);\n  const distance = Math.abs(scrollAmount);\n  const duration = distance / SCROLL_ASSIST_SPEED;\n  const scrollDuration = Math.min(400, Math.max(150, duration));\n  return {\n    scrollAmount,\n    scrollDuration,\n    scrollPadding: keyboardHeight,\n    inputSafeY: -(inputTop - safeAreaTop) + 4\n  };\n};\nconst PADDING_TIMER_KEY = '$ionPaddingTimer';\n/**\n * Scroll padding adds additional padding to the bottom\n * of ion-content so that there is enough scroll space\n * for an input to be scrolled above the keyboard. This\n * is needed in environments where the webview does not\n * resize when the keyboard opens.\n *\n * Example: If an input at the bottom of ion-content is\n * focused, there is no additional scrolling space below\n * it, so the input cannot be scrolled above the keyboard.\n * Scroll padding fixes this by adding padding equal to the\n * height of the keyboard to the bottom of the content.\n *\n * Common environments where this is needed:\n * - Mobile Safari: The keyboard overlays the content\n * - Capacitor/Cordova on iOS: The keyboard overlays the content\n * when the KeyboardResize mode is set to 'none'.\n */\nconst setScrollPadding = (contentEl, paddingAmount, clearCallback) => {\n  const timer = contentEl[PADDING_TIMER_KEY];\n  if (timer) {\n    clearTimeout(timer);\n  }\n  if (paddingAmount > 0) {\n    contentEl.style.setProperty('--keyboard-offset', `${paddingAmount}px`);\n  } else {\n    contentEl[PADDING_TIMER_KEY] = setTimeout(() => {\n      contentEl.style.setProperty('--keyboard-offset', '0px');\n      if (clearCallback) {\n        clearCallback();\n      }\n    }, 120);\n  }\n};\n/**\n * When an input is about to be focused,\n * set a timeout to clear any scroll padding\n * on the content. Note: The clearing\n * is done on a timeout so that if users\n * are moving focus from one input to the next\n * then re-adding scroll padding to the new\n * input with cancel the timeout to clear the\n * scroll padding.\n */\nconst setClearScrollPaddingListener = (inputEl, contentEl, doneCallback) => {\n  const clearScrollPadding = () => {\n    if (contentEl) {\n      setScrollPadding(contentEl, 0, doneCallback);\n    }\n  };\n  inputEl.addEventListener('focusout', clearScrollPadding, {\n    once: true\n  });\n};\nlet currentPadding = 0;\nconst SKIP_SCROLL_ASSIST = 'data-ionic-skip-scroll-assist';\nconst enableScrollAssist = (componentEl, inputEl, contentEl, footerEl, keyboardHeight, enableScrollPadding, keyboardResize, disableClonedInput = false) => {\n  /**\n   * Scroll padding should only be added if:\n   * 1. The global scrollPadding config option\n   * is set to true.\n   * 2. The native keyboard resize mode is either \"none\"\n   * (keyboard overlays webview) or undefined (resize\n   * information unavailable)\n   * Resize info is available on Capacitor 4+\n   */\n  const addScrollPadding = enableScrollPadding && (keyboardResize === undefined || keyboardResize.mode === KeyboardResize.None);\n  /**\n   * This tracks whether or not the keyboard has been\n   * presented for a single focused text field. Note\n   * that it does not track if the keyboard is open\n   * in general such as if the keyboard is open for\n   * a different focused text field.\n   */\n  let hasKeyboardBeenPresentedForTextField = false;\n  /**\n   * When adding scroll padding we need to know\n   * how much of the viewport the keyboard obscures.\n   * We do this by subtracting the keyboard height\n   * from the platform height.\n   *\n   * If we compute this value when switching between\n   * inputs then the webview may already be resized.\n   * At this point, `win.innerHeight` has already accounted\n   * for the keyboard meaning we would then subtract\n   * the keyboard height again. This will result in the input\n   * being scrolled more than it needs to.\n   */\n  const platformHeight = win !== undefined ? win.innerHeight : 0;\n  /**\n   * Scroll assist is run when a text field\n   * is focused. However, it may need to\n   * re-run when the keyboard size changes\n   * such that the text field is now hidden\n   * underneath the keyboard.\n   * This function re-runs scroll assist\n   * when that happens.\n   *\n   * One limitation of this is on a web browser\n   * where native keyboard APIs do not have cross-browser\n   * support. `ionKeyboardDidShow` relies on the Visual Viewport API.\n   * This means that if the keyboard changes but does not change\n   * geometry, then scroll assist will not re-run even if\n   * the user has scrolled the text field under the keyboard.\n   * This is not a problem when running in Cordova/Capacitor\n   * because `ionKeyboardDidShow` uses the native events\n   * which fire every time the keyboard changes.\n   */\n  const keyboardShow = ev => {\n    /**\n     * If the keyboard has not yet been presented\n     * for this text field then the text field has just\n     * received focus. In that case, the focusin listener\n     * will run scroll assist.\n     */\n    if (hasKeyboardBeenPresentedForTextField === false) {\n      hasKeyboardBeenPresentedForTextField = true;\n      return;\n    }\n    /**\n     * Otherwise, the keyboard has already been presented\n     * for the focused text field.\n     * This means that the keyboard likely changed\n     * geometry, and we need to re-run scroll assist.\n     * This can happen when the user rotates their device\n     * or when they switch keyboards.\n     *\n     * Make sure we pass in the computed keyboard height\n     * rather than the estimated keyboard height.\n     *\n     * Since the keyboard is already open then we do not\n     * need to wait for the webview to resize, so we pass\n     * \"waitForResize: false\".\n     */\n    jsSetFocus(componentEl, inputEl, contentEl, footerEl, ev.detail.keyboardHeight, addScrollPadding, disableClonedInput, platformHeight, false);\n  };\n  /**\n   * Reset the internal state when the text field loses focus.\n   */\n  const focusOut = () => {\n    hasKeyboardBeenPresentedForTextField = false;\n    win === null || win === void 0 ? void 0 : win.removeEventListener('ionKeyboardDidShow', keyboardShow);\n    componentEl.removeEventListener('focusout', focusOut);\n  };\n  /**\n   * When the input is about to receive\n   * focus, we need to move it to prevent\n   * mobile Safari from adjusting the viewport.\n   */\n  const focusIn = async () => {\n    /**\n     * Scroll assist should not run again\n     * on inputs that have been manually\n     * focused inside of the scroll assist\n     * implementation.\n     */\n    if (inputEl.hasAttribute(SKIP_SCROLL_ASSIST)) {\n      inputEl.removeAttribute(SKIP_SCROLL_ASSIST);\n      return;\n    }\n    jsSetFocus(componentEl, inputEl, contentEl, footerEl, keyboardHeight, addScrollPadding, disableClonedInput, platformHeight);\n    win === null || win === void 0 ? void 0 : win.addEventListener('ionKeyboardDidShow', keyboardShow);\n    componentEl.addEventListener('focusout', focusOut);\n  };\n  componentEl.addEventListener('focusin', focusIn);\n  return () => {\n    componentEl.removeEventListener('focusin', focusIn);\n    win === null || win === void 0 ? void 0 : win.removeEventListener('ionKeyboardDidShow', keyboardShow);\n    componentEl.removeEventListener('focusout', focusOut);\n  };\n};\n/**\n * Use this function when you want to manually\n * focus an input but not have scroll assist run again.\n */\nconst setManualFocus = el => {\n  /**\n   * If element is already focused then\n   * a new focusin event will not be dispatched\n   * to remove the SKIL_SCROLL_ASSIST attribute.\n   */\n  if (document.activeElement === el) {\n    return;\n  }\n  el.setAttribute(SKIP_SCROLL_ASSIST, 'true');\n  el.focus();\n};\nconst jsSetFocus = async (componentEl, inputEl, contentEl, footerEl, keyboardHeight, enableScrollPadding, disableClonedInput = false, platformHeight = 0, waitForResize = true) => {\n  if (!contentEl && !footerEl) {\n    return;\n  }\n  const scrollData = getScrollData(componentEl, contentEl || footerEl, keyboardHeight, platformHeight);\n  if (contentEl && Math.abs(scrollData.scrollAmount) < 4) {\n    // the text input is in a safe position that doesn't\n    // require it to be scrolled into view, just set focus now\n    setManualFocus(inputEl);\n    /**\n     * Even though the input does not need\n     * scroll assist, we should preserve the\n     * the scroll padding as users could be moving\n     * focus from an input that needs scroll padding\n     * to an input that does not need scroll padding.\n     * If we remove the scroll padding now, users will\n     * see the page jump.\n     */\n    if (enableScrollPadding && contentEl !== null) {\n      setScrollPadding(contentEl, currentPadding);\n      setClearScrollPaddingListener(inputEl, contentEl, () => currentPadding = 0);\n    }\n    return;\n  }\n  // temporarily move the focus to the focus holder so the browser\n  // doesn't freak out while it's trying to get the input in place\n  // at this point the native text input still does not have focus\n  relocateInput(componentEl, inputEl, true, scrollData.inputSafeY, disableClonedInput);\n  setManualFocus(inputEl);\n  /**\n   * Relocating/Focusing input causes the\n   * click event to be cancelled, so\n   * manually fire one here.\n   */\n  raf(() => componentEl.click());\n  /**\n   * If enabled, we can add scroll padding to\n   * the bottom of the content so that scroll assist\n   * has enough room to scroll the input above\n   * the keyboard.\n   */\n  if (enableScrollPadding && contentEl) {\n    currentPadding = scrollData.scrollPadding;\n    setScrollPadding(contentEl, currentPadding);\n  }\n  if (typeof window !== 'undefined') {\n    let scrollContentTimeout;\n    const scrollContent = async () => {\n      // clean up listeners and timeouts\n      if (scrollContentTimeout !== undefined) {\n        clearTimeout(scrollContentTimeout);\n      }\n      window.removeEventListener('ionKeyboardDidShow', doubleKeyboardEventListener);\n      window.removeEventListener('ionKeyboardDidShow', scrollContent);\n      // scroll the input into place\n      if (contentEl) {\n        await scrollByPoint(contentEl, 0, scrollData.scrollAmount, scrollData.scrollDuration);\n      }\n      // the scroll view is in the correct position now\n      // give the native text input focus\n      relocateInput(componentEl, inputEl, false, scrollData.inputSafeY);\n      // ensure this is the focused input\n      setManualFocus(inputEl);\n      /**\n       * When the input is about to be blurred\n       * we should set a timeout to remove\n       * any scroll padding.\n       */\n      if (enableScrollPadding) {\n        setClearScrollPaddingListener(inputEl, contentEl, () => currentPadding = 0);\n      }\n    };\n    const doubleKeyboardEventListener = () => {\n      window.removeEventListener('ionKeyboardDidShow', doubleKeyboardEventListener);\n      window.addEventListener('ionKeyboardDidShow', scrollContent);\n    };\n    if (contentEl) {\n      const scrollEl = await getScrollElement(contentEl);\n      /**\n       * scrollData will only consider the amount we need\n       * to scroll in order to properly bring the input\n       * into view. It will not consider the amount\n       * we can scroll in the content element.\n       * As a result, scrollData may request a greater\n       * scroll position than is currently available\n       * in the DOM. If this is the case, we need to\n       * wait for the webview to resize/the keyboard\n       * to show in order for additional scroll\n       * bandwidth to become available.\n       */\n      const totalScrollAmount = scrollEl.scrollHeight - scrollEl.clientHeight;\n      if (waitForResize && scrollData.scrollAmount > totalScrollAmount - scrollEl.scrollTop) {\n        /**\n         * On iOS devices, the system will show a \"Passwords\" bar above the keyboard\n         * after the initial keyboard is shown. This prevents the webview from resizing\n         * until the \"Passwords\" bar is shown, so we need to wait for that to happen first.\n         */\n        if (inputEl.type === 'password') {\n          // Add 50px to account for the \"Passwords\" bar\n          scrollData.scrollAmount += SCROLL_AMOUNT_PADDING;\n          window.addEventListener('ionKeyboardDidShow', doubleKeyboardEventListener);\n        } else {\n          window.addEventListener('ionKeyboardDidShow', scrollContent);\n        }\n        /**\n         * This should only fire in 2 instances:\n         * 1. The app is very slow.\n         * 2. The app is running in a browser on an old OS\n         * that does not support Ionic Keyboard Events\n         */\n        scrollContentTimeout = setTimeout(scrollContent, 1000);\n        return;\n      }\n    }\n    scrollContent();\n  }\n};\nconst INPUT_BLURRING = true;\nconst startInputShims = async (config, platform) => {\n  /**\n   * If doc is undefined then we are in an SSR environment\n   * where input shims do not apply.\n   */\n  if (doc === undefined) {\n    return;\n  }\n  const isIOS = platform === 'ios';\n  const isAndroid = platform === 'android';\n  /**\n   * Hide Caret and Input Blurring are needed on iOS.\n   * Scroll Assist and Scroll Padding are needed on iOS and Android\n   * with Chrome web browser (not Chrome webview).\n   */\n  const keyboardHeight = config.getNumber('keyboardHeight', 290);\n  const scrollAssist = config.getBoolean('scrollAssist', true);\n  const hideCaret = config.getBoolean('hideCaretOnScroll', isIOS);\n  /**\n   * The team is evaluating if inputBlurring is still needed. As a result\n   * this feature is disabled by default as of Ionic 8.0. Developers are\n   * able to re-enable it temporarily. The team may remove this utility\n   * if it is determined that doing so would not bring any adverse side effects.\n   * TODO FW-6014 remove input blurring utility (including implementation)\n   */\n  const inputBlurring = config.getBoolean('inputBlurring', false);\n  const scrollPadding = config.getBoolean('scrollPadding', true);\n  const inputs = Array.from(doc.querySelectorAll('ion-input, ion-textarea'));\n  const hideCaretMap = new WeakMap();\n  const scrollAssistMap = new WeakMap();\n  /**\n   * Grab the native keyboard resize configuration\n   * and pass it to scroll assist. Scroll assist requires\n   * that we adjust the input right before the input\n   * is about to be focused. If we called `Keyboard.getResizeMode`\n   * on focusin in scroll assist, we could potentially adjust the\n   * input too late since this call is async.\n   */\n  const keyboardResizeMode = await Keyboard.getResizeMode();\n  const registerInput = async componentEl => {\n    await new Promise(resolve => componentOnReady(componentEl, resolve));\n    const inputRoot = componentEl.shadowRoot || componentEl;\n    const inputEl = inputRoot.querySelector('input') || inputRoot.querySelector('textarea');\n    const scrollEl = findClosestIonContent(componentEl);\n    const footerEl = !scrollEl ? componentEl.closest('ion-footer') : null;\n    if (!inputEl) {\n      return;\n    }\n    if (!!scrollEl && hideCaret && !hideCaretMap.has(componentEl)) {\n      const rmFn = enableHideCaretOnScroll(componentEl, inputEl, scrollEl);\n      hideCaretMap.set(componentEl, rmFn);\n    }\n    /**\n     * date/datetime-locale inputs on mobile devices show date picker\n     * overlays instead of keyboards. As a result, scroll assist is\n     * not needed. This also works around a bug in iOS <16 where\n     * scroll assist causes the browser to lock up. See FW-1997.\n     */\n    const isDateInput = inputEl.type === 'date' || inputEl.type === 'datetime-local';\n    if (!isDateInput && (!!scrollEl || !!footerEl) && scrollAssist && !scrollAssistMap.has(componentEl)) {\n      const rmFn = enableScrollAssist(componentEl, inputEl, scrollEl, footerEl, keyboardHeight, scrollPadding, keyboardResizeMode, isAndroid);\n      scrollAssistMap.set(componentEl, rmFn);\n    }\n  };\n  const unregisterInput = componentEl => {\n    if (hideCaret) {\n      const fn = hideCaretMap.get(componentEl);\n      if (fn) {\n        fn();\n      }\n      hideCaretMap.delete(componentEl);\n    }\n    if (scrollAssist) {\n      const fn = scrollAssistMap.get(componentEl);\n      if (fn) {\n        fn();\n      }\n      scrollAssistMap.delete(componentEl);\n    }\n  };\n  if (inputBlurring && INPUT_BLURRING) {\n    enableInputBlurring();\n  }\n  // Input might be already loaded in the DOM before ion-device-hacks did.\n  // At this point we need to look for all of the inputs not registered yet\n  // and register them.\n  for (const input of inputs) {\n    registerInput(input);\n  }\n  doc.addEventListener('ionInputDidLoad', ev => {\n    registerInput(ev.detail);\n  });\n  doc.addEventListener('ionInputDidUnload', ev => {\n    unregisterInput(ev.detail);\n  });\n};\nexport { startInputShims };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,WAAW,oBAAI,QAAQ;AAC7B,IAAM,gBAAgB,CAAC,aAAa,SAAS,gBAAgB,iBAAiB,GAAG,sBAAsB,UAAU;AAC/G,MAAI,SAAS,IAAI,WAAW,MAAM,gBAAgB;AAChD;AAAA,EACF;AACA,MAAI,gBAAgB;AAClB,aAAS,aAAa,SAAS,gBAAgB,mBAAmB;AAAA,EACpE,OAAO;AACL,gBAAY,aAAa,OAAO;AAAA,EAClC;AACF;AACA,IAAM,YAAY,WAAS;AAUzB,SAAO,UAAU,MAAM,YAAY,EAAE;AACvC;AACA,IAAM,WAAW,CAAC,aAAa,SAAS,gBAAgB,sBAAsB,UAAU;AAUtF,QAAM,WAAW,QAAQ;AAEzB,QAAM,WAAW,QAAQ,UAAU,KAAK;AACxC,WAAS,UAAU,IAAI,cAAc;AACrC,WAAS,WAAW;AAapB,MAAI,qBAAqB;AACvB,aAAS,WAAW;AAAA,EACtB;AACA,WAAS,YAAY,QAAQ;AAC7B,WAAS,IAAI,aAAa,QAAQ;AAClC,QAAMA,OAAM,YAAY;AACxB,QAAM,KAAKA,KAAI,QAAQ,QAAQ,OAAO;AACtC,cAAY,MAAM,gBAAgB;AAClC,UAAQ,MAAM,YAAY,eAAe,EAAE,MAAM,cAAc;AACjE;AACA,IAAM,cAAc,CAAC,aAAa,YAAY;AAC5C,QAAM,QAAQ,SAAS,IAAI,WAAW;AACtC,MAAI,OAAO;AACT,aAAS,OAAO,WAAW;AAC3B,UAAM,OAAO;AAAA,EACf;AACA,cAAY,MAAM,gBAAgB;AAClC,UAAQ,MAAM,YAAY;AAC5B;AAMA,IAAM,wBAAwB;AAC9B,IAAM,0BAA0B,CAAC,aAAa,SAAS,aAAa;AAClE,MAAI,CAAC,YAAY,CAAC,SAAS;AACzB,WAAO,MAAM;AACX;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB,qBAAmB;AACzC,QAAI,UAAU,OAAO,GAAG;AACtB,oBAAc,aAAa,SAAS,eAAe;AAAA,IACrD;AAAA,EACF;AACA,QAAM,SAAS,MAAM,cAAc,aAAa,SAAS,KAAK;AAC9D,QAAM,YAAY,MAAM,gBAAgB,IAAI;AAC5C,QAAM,YAAY,MAAM,gBAAgB,KAAK;AAC7C,mBAAiB,UAAU,kBAAkB,SAAS;AACtD,mBAAiB,UAAU,gBAAgB,SAAS;AACpD,UAAQ,iBAAiB,QAAQ,MAAM;AACvC,SAAO,MAAM;AACX,wBAAoB,UAAU,kBAAkB,SAAS;AACzD,wBAAoB,UAAU,gBAAgB,SAAS;AACvD,YAAQ,oBAAoB,QAAQ,MAAM;AAAA,EAC5C;AACF;AACA,IAAM,gBAAgB;AACtB,IAAM,sBAAsB,MAAM;AAChC,MAAI,UAAU;AACd,MAAI,YAAY;AAChB,QAAMA,OAAM;AACZ,QAAM,WAAW,MAAM;AACrB,gBAAY;AAAA,EACd;AACA,QAAM,YAAY,MAAM;AACtB,cAAU;AAAA,EACZ;AACA,QAAM,aAAa,QAAM;AAEvB,QAAI,WAAW;AACb,kBAAY;AACZ;AAAA,IACF;AACA,UAAM,SAASA,KAAI;AACnB,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AAEA,QAAI,OAAO,QAAQ,aAAa,GAAG;AACjC;AAAA,IACF;AAEA,UAAM,SAAS,GAAG;AAClB,QAAI,WAAW,QAAQ;AACrB;AAAA,IACF;AACA,QAAI,OAAO,QAAQ,aAAa,KAAK,OAAO,QAAQ,aAAa,GAAG;AAClE;AAAA,IACF;AACA,cAAU;AAEV,eAAW,MAAM;AACf,UAAI,CAAC,SAAS;AACZ,eAAO,KAAK;AAAA,MACd;AAAA,IACF,GAAG,EAAE;AAAA,EACP;AACA,mBAAiBA,MAAK,kBAAkB,QAAQ;AAChD,EAAAA,KAAI,iBAAiB,WAAW,WAAW,IAAI;AAC/C,EAAAA,KAAI,iBAAiB,YAAY,YAAY,KAAK;AAClD,SAAO,MAAM;AACX,wBAAoBA,MAAK,kBAAkB,UAAU,IAAI;AACzD,IAAAA,KAAI,oBAAoB,WAAW,WAAW,IAAI;AAClD,IAAAA,KAAI,oBAAoB,YAAY,YAAY,KAAK;AAAA,EACvD;AACF;AACA,IAAM,sBAAsB;AAC5B,IAAM,gBAAgB,CAAC,aAAa,WAAW,gBAAgB,mBAAmB;AAChF,MAAI;AACJ,QAAM,UAAU,KAAK,YAAY,QAAQ,qBAAqB,OAAO,QAAQ,OAAO,SAAS,KAAK;AAClG,SAAO,eAAe,OAAO,sBAAsB,GAAG,UAAU,sBAAsB,GAAG,gBAAgB,cAAc;AACzH;AACA,IAAM,iBAAiB,CAAC,WAAW,aAAa,gBAAgB,mBAAmB;AAEjF,QAAM,WAAW,UAAU;AAC3B,QAAM,cAAc,UAAU;AAE9B,QAAM,iBAAiB,YAAY;AACnC,QAAM,oBAAoB,KAAK,IAAI,YAAY,QAAQ,iBAAiB,cAAc;AAEtF,QAAM,cAAc,iBAAiB;AACrC,QAAM,iBAAiB,oBAAoB;AAE3C,QAAM,mBAAmB,iBAAiB;AAC1C,QAAM,gBAAgB,cAAc;AAEpC,QAAM,sBAAsB,KAAK,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,gBAAgB,IAAI,CAAC,gBAAgB,CAAC;AAGxH,QAAM,eAAe,KAAK,IAAI,qBAAqB,WAAW,cAAc;AAC5E,QAAM,WAAW,KAAK,IAAI,YAAY;AACtC,QAAM,WAAW,WAAW;AAC5B,QAAM,iBAAiB,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAC5D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf,YAAY,EAAE,WAAW,eAAe;AAAA,EAC1C;AACF;AACA,IAAM,oBAAoB;AAmB1B,IAAM,mBAAmB,CAAC,WAAW,eAAe,kBAAkB;AACpE,QAAM,QAAQ,UAAU,iBAAiB;AACzC,MAAI,OAAO;AACT,iBAAa,KAAK;AAAA,EACpB;AACA,MAAI,gBAAgB,GAAG;AACrB,cAAU,MAAM,YAAY,qBAAqB,GAAG,aAAa,IAAI;AAAA,EACvE,OAAO;AACL,cAAU,iBAAiB,IAAI,WAAW,MAAM;AAC9C,gBAAU,MAAM,YAAY,qBAAqB,KAAK;AACtD,UAAI,eAAe;AACjB,sBAAc;AAAA,MAChB;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF;AAWA,IAAM,gCAAgC,CAAC,SAAS,WAAW,iBAAiB;AAC1E,QAAM,qBAAqB,MAAM;AAC/B,QAAI,WAAW;AACb,uBAAiB,WAAW,GAAG,YAAY;AAAA,IAC7C;AAAA,EACF;AACA,UAAQ,iBAAiB,YAAY,oBAAoB;AAAA,IACvD,MAAM;AAAA,EACR,CAAC;AACH;AACA,IAAI,iBAAiB;AACrB,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB,CAAC,aAAa,SAAS,WAAW,UAAU,gBAAgB,qBAAqB,gBAAgB,qBAAqB,UAAU;AAUzJ,QAAM,mBAAmB,wBAAwB,mBAAmB,UAAa,eAAe,SAAS,eAAe;AAQxH,MAAI,uCAAuC;AAc3C,QAAM,iBAAiB,QAAQ,SAAY,IAAI,cAAc;AAoB7D,QAAM,eAAe,QAAM;AAOzB,QAAI,yCAAyC,OAAO;AAClD,6CAAuC;AACvC;AAAA,IACF;AAgBA,eAAW,aAAa,SAAS,WAAW,UAAU,GAAG,OAAO,gBAAgB,kBAAkB,oBAAoB,gBAAgB,KAAK;AAAA,EAC7I;AAIA,QAAM,WAAW,MAAM;AACrB,2CAAuC;AACvC,YAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,oBAAoB,sBAAsB,YAAY;AACpG,gBAAY,oBAAoB,YAAY,QAAQ;AAAA,EACtD;AAMA,QAAM,UAAU,MAAY;AAO1B,QAAI,QAAQ,aAAa,kBAAkB,GAAG;AAC5C,cAAQ,gBAAgB,kBAAkB;AAC1C;AAAA,IACF;AACA,eAAW,aAAa,SAAS,WAAW,UAAU,gBAAgB,kBAAkB,oBAAoB,cAAc;AAC1H,YAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,iBAAiB,sBAAsB,YAAY;AACjG,gBAAY,iBAAiB,YAAY,QAAQ;AAAA,EACnD;AACA,cAAY,iBAAiB,WAAW,OAAO;AAC/C,SAAO,MAAM;AACX,gBAAY,oBAAoB,WAAW,OAAO;AAClD,YAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,oBAAoB,sBAAsB,YAAY;AACpG,gBAAY,oBAAoB,YAAY,QAAQ;AAAA,EACtD;AACF;AAKA,IAAM,iBAAiB,QAAM;AAM3B,MAAI,SAAS,kBAAkB,IAAI;AACjC;AAAA,EACF;AACA,KAAG,aAAa,oBAAoB,MAAM;AAC1C,KAAG,MAAM;AACX;AACA,IAAM,aAAa,CAAO,aAAa,SAAS,WAAW,UAAU,gBAAgB,qBAAqB,qBAAqB,OAAO,iBAAiB,GAAG,gBAAgB,SAAS;AACjL,MAAI,CAAC,aAAa,CAAC,UAAU;AAC3B;AAAA,EACF;AACA,QAAM,aAAa,cAAc,aAAa,aAAa,UAAU,gBAAgB,cAAc;AACnG,MAAI,aAAa,KAAK,IAAI,WAAW,YAAY,IAAI,GAAG;AAGtD,mBAAe,OAAO;AAUtB,QAAI,uBAAuB,cAAc,MAAM;AAC7C,uBAAiB,WAAW,cAAc;AAC1C,oCAA8B,SAAS,WAAW,MAAM,iBAAiB,CAAC;AAAA,IAC5E;AACA;AAAA,EACF;AAIA,gBAAc,aAAa,SAAS,MAAM,WAAW,YAAY,kBAAkB;AACnF,iBAAe,OAAO;AAMtB,MAAI,MAAM,YAAY,MAAM,CAAC;AAO7B,MAAI,uBAAuB,WAAW;AACpC,qBAAiB,WAAW;AAC5B,qBAAiB,WAAW,cAAc;AAAA,EAC5C;AACA,MAAI,OAAO,WAAW,aAAa;AACjC,QAAI;AACJ,UAAM,gBAAgB,MAAY;AAEhC,UAAI,yBAAyB,QAAW;AACtC,qBAAa,oBAAoB;AAAA,MACnC;AACA,aAAO,oBAAoB,sBAAsB,2BAA2B;AAC5E,aAAO,oBAAoB,sBAAsB,aAAa;AAE9D,UAAI,WAAW;AACb,cAAM,cAAc,WAAW,GAAG,WAAW,cAAc,WAAW,cAAc;AAAA,MACtF;AAGA,oBAAc,aAAa,SAAS,OAAO,WAAW,UAAU;AAEhE,qBAAe,OAAO;AAMtB,UAAI,qBAAqB;AACvB,sCAA8B,SAAS,WAAW,MAAM,iBAAiB,CAAC;AAAA,MAC5E;AAAA,IACF;AACA,UAAM,8BAA8B,MAAM;AACxC,aAAO,oBAAoB,sBAAsB,2BAA2B;AAC5E,aAAO,iBAAiB,sBAAsB,aAAa;AAAA,IAC7D;AACA,QAAI,WAAW;AACb,YAAM,WAAW,MAAM,iBAAiB,SAAS;AAajD,YAAM,oBAAoB,SAAS,eAAe,SAAS;AAC3D,UAAI,iBAAiB,WAAW,eAAe,oBAAoB,SAAS,WAAW;AAMrF,YAAI,QAAQ,SAAS,YAAY;AAE/B,qBAAW,gBAAgB;AAC3B,iBAAO,iBAAiB,sBAAsB,2BAA2B;AAAA,QAC3E,OAAO;AACL,iBAAO,iBAAiB,sBAAsB,aAAa;AAAA,QAC7D;AAOA,+BAAuB,WAAW,eAAe,GAAI;AACrD;AAAA,MACF;AAAA,IACF;AACA,kBAAc;AAAA,EAChB;AACF;AACA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,CAAO,QAAQ,aAAa;AAKlD,MAAI,QAAQ,QAAW;AACrB;AAAA,EACF;AACA,QAAM,QAAQ,aAAa;AAC3B,QAAM,YAAY,aAAa;AAM/B,QAAM,iBAAiB,OAAO,UAAU,kBAAkB,GAAG;AAC7D,QAAM,eAAe,OAAO,WAAW,gBAAgB,IAAI;AAC3D,QAAM,YAAY,OAAO,WAAW,qBAAqB,KAAK;AAQ9D,QAAM,gBAAgB,OAAO,WAAW,iBAAiB,KAAK;AAC9D,QAAM,gBAAgB,OAAO,WAAW,iBAAiB,IAAI;AAC7D,QAAM,SAAS,MAAM,KAAK,IAAI,iBAAiB,yBAAyB,CAAC;AACzE,QAAM,eAAe,oBAAI,QAAQ;AACjC,QAAM,kBAAkB,oBAAI,QAAQ;AASpC,QAAM,qBAAqB,MAAM,SAAS,cAAc;AACxD,QAAM,gBAAgB,CAAM,gBAAe;AACzC,UAAM,IAAI,QAAQ,aAAW,iBAAiB,aAAa,OAAO,CAAC;AACnE,UAAM,YAAY,YAAY,cAAc;AAC5C,UAAM,UAAU,UAAU,cAAc,OAAO,KAAK,UAAU,cAAc,UAAU;AACtF,UAAM,WAAW,sBAAsB,WAAW;AAClD,UAAM,WAAW,CAAC,WAAW,YAAY,QAAQ,YAAY,IAAI;AACjE,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,CAAC,CAAC,YAAY,aAAa,CAAC,aAAa,IAAI,WAAW,GAAG;AAC7D,YAAM,OAAO,wBAAwB,aAAa,SAAS,QAAQ;AACnE,mBAAa,IAAI,aAAa,IAAI;AAAA,IACpC;AAOA,UAAM,cAAc,QAAQ,SAAS,UAAU,QAAQ,SAAS;AAChE,QAAI,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,aAAa,gBAAgB,CAAC,gBAAgB,IAAI,WAAW,GAAG;AACnG,YAAM,OAAO,mBAAmB,aAAa,SAAS,UAAU,UAAU,gBAAgB,eAAe,oBAAoB,SAAS;AACtI,sBAAgB,IAAI,aAAa,IAAI;AAAA,IACvC;AAAA,EACF;AACA,QAAM,kBAAkB,iBAAe;AACrC,QAAI,WAAW;AACb,YAAM,KAAK,aAAa,IAAI,WAAW;AACvC,UAAI,IAAI;AACN,WAAG;AAAA,MACL;AACA,mBAAa,OAAO,WAAW;AAAA,IACjC;AACA,QAAI,cAAc;AAChB,YAAM,KAAK,gBAAgB,IAAI,WAAW;AAC1C,UAAI,IAAI;AACN,WAAG;AAAA,MACL;AACA,sBAAgB,OAAO,WAAW;AAAA,IACpC;AAAA,EACF;AACA,MAAI,iBAAiB,gBAAgB;AACnC,wBAAoB;AAAA,EACtB;AAIA,aAAW,SAAS,QAAQ;AAC1B,kBAAc,KAAK;AAAA,EACrB;AACA,MAAI,iBAAiB,mBAAmB,QAAM;AAC5C,kBAAc,GAAG,MAAM;AAAA,EACzB,CAAC;AACD,MAAI,iBAAiB,qBAAqB,QAAM;AAC9C,oBAAgB,GAAG,MAAM;AAAA,EAC3B,CAAC;AACH;", "names": ["doc"]}