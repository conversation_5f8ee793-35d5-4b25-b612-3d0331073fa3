import{A as ne,a as W,d as K,m as Q,n as X,t as Z,w as ee,y as te}from"./chunk-TV52TPWK.js";import{b as J}from"./chunk-ZBJDNBYI.js";import"./chunk-YUFJ6257.js";import{Aa as E,C as i,Ca as F,D as h,E as v,F as u,G as l,H as _,I as n,Ia as T,J as o,K as c,Ka as R,La as z,M as f,Ma as L,N as x,Na as j,O as s,Q as P,Qa as V,Ua as q,Wa as A,Xa as N,ca as b,cb as G,fb as B,ga as w,gb as D,hb as U,na as M,nb as $,pb as H,qb as Y,ta as p,ua as y,va as O,xa as I,ya as k,za as S}from"./chunk-DVLCYDDQ.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{g as C}from"./chunk-OLRFWS6T.js";function ie(r,d){if(r&1&&(n(0,"ion-text",27),s(1),o()),r&2){let g=x();i(),P(" ",g.getFieldError("name")," ")}}function oe(r,d){if(r&1&&(n(0,"ion-text",27),s(1),o()),r&2){let g=x();i(),P(" ",g.getFieldError("email")," ")}}function re(r,d){if(r&1&&(n(0,"ion-text",27),s(1),o()),r&2){let g=x();i(),P(" ",g.getFieldError("password")," ")}}function ae(r,d){if(r&1&&(n(0,"ion-text",27),s(1),o()),r&2){let g=x();i(),P(" ",g.getFieldError("confirmPassword")," ")}}function se(r,d){r&1&&c(0,"ion-spinner",28)}function le(r,d){r&1&&(n(0,"span"),s(1,"Create Account"),o())}var Ce=(()=>{let d=class d{constructor(a,t,e,m){this.formBuilder=a,this.authService=t,this.router=e,this.toastController=m,this.showPassword=!1,this.showConfirmPassword=!1,this.isLoading=!1,W({person:ne,mail:te,lockClosed:ee,eye:Q,eyeOff:X,leaf:Z,arrowBack:K}),this.registerForm=this.formBuilder.group({name:["",[p.required,p.minLength(2)]],email:["",[p.required,p.email]],password:["",[p.required,p.minLength(6)]],confirmPassword:["",[p.required]]},{validators:this.passwordMatchValidator})}passwordMatchValidator(a){var m;let t=a.get("password"),e=a.get("confirmPassword");return t&&e&&t.value!==e.value?e.setErrors({passwordMismatch:!0}):(m=e==null?void 0:e.errors)!=null&&m.passwordMismatch&&(delete e.errors.passwordMismatch,Object.keys(e.errors).length===0&&e.setErrors(null)),null}onSubmit(){return C(this,null,function*(){if(this.registerForm.valid){let{name:a,email:t,password:e}=this.registerForm.value;try{yield this.authService.signUpWithEmail(t,e,a)}catch(m){console.error("Registration error:",m)}}else this.markFormGroupTouched()})}togglePasswordVisibility(){this.showPassword=!this.showPassword}toggleConfirmPasswordVisibility(){this.showConfirmPassword=!this.showConfirmPassword}navigateToLogin(){this.router.navigate(["/login"])}markFormGroupTouched(){Object.keys(this.registerForm.controls).forEach(a=>{let t=this.registerForm.get(a);t==null||t.markAsTouched()})}isFieldInvalid(a){let t=this.registerForm.get(a);return!!(t&&t.invalid&&t.touched)}getFieldError(a){let t=this.registerForm.get(a);if(t&&t.errors&&t.touched){if(t.errors.required)return`${a.charAt(0).toUpperCase()+a.slice(1)} is required`;if(t.errors.email)return"Please enter a valid email address";if(t.errors.minlength){let e=t.errors.minlength.requiredLength;return`${a.charAt(0).toUpperCase()+a.slice(1)} must be at least ${e} characters long`}if(t.errors.passwordMismatch)return"Passwords do not match"}return""}};d.\u0275fac=function(t){return new(t||d)(h(E),h(J),h(M),h($))},d.\u0275cmp=v({type:d,selectors:[["app-register"]],decls:57,vars:22,consts:[[3,"translucent"],["slot","start","fill","clear",3,"click"],["name","arrow-back"],[1,"register-content",3,"fullscreen"],[1,"register-container"],[1,"app-header"],[1,"logo-container"],["name","leaf","size","large","color","primary"],[1,"register-card"],[3,"ngSubmit","formGroup"],["lines","none",1,"form-item"],["name","person","slot","start","color","medium"],["position","stacked"],["type","text","formControlName","name","placeholder","Enter your full name","autocomplete","name"],["color","danger","class","error-text",4,"ngIf"],["name","mail","slot","start","color","medium"],["type","email","formControlName","email","placeholder","Enter your email","autocomplete","email"],["name","lock-closed","slot","start","color","medium"],["formControlName","password","placeholder","Create a password","autocomplete","new-password",3,"type"],["fill","clear","slot","end",1,"password-toggle",3,"click"],["color","medium",3,"name"],["formControlName","confirmPassword","placeholder","Confirm your password","autocomplete","new-password",3,"type"],["expand","block","type","submit",1,"signup-button",3,"disabled"],["name","crescent",4,"ngIf"],[4,"ngIf"],[1,"signin-link"],["fill","clear","size","small",1,"link-button",3,"click"],["color","danger",1,"error-text"],["name","crescent"]],template:function(t,e){t&1&&(n(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-button",1),f("click",function(){return e.navigateToLogin()}),c(3,"ion-icon",2),o(),n(4,"ion-title"),s(5,"Create Account"),o()()(),n(6,"ion-content",3)(7,"div",4)(8,"div",5)(9,"div",6),c(10,"ion-icon",7),o(),n(11,"h1"),s(12,"Join PlantDetect"),o(),n(13,"p"),s(14,"Start your plant identification journey"),o()(),n(15,"ion-card",8)(16,"ion-card-header")(17,"ion-card-title"),s(18,"Sign Up"),o()(),n(19,"ion-card-content")(20,"form",9),f("ngSubmit",function(){return e.onSubmit()}),n(21,"ion-item",10),c(22,"ion-icon",11),n(23,"ion-label",12),s(24,"Full Name"),o(),c(25,"ion-input",13),o(),u(26,ie,2,1,"ion-text",14),n(27,"ion-item",10),c(28,"ion-icon",15),n(29,"ion-label",12),s(30,"Email"),o(),c(31,"ion-input",16),o(),u(32,oe,2,1,"ion-text",14),n(33,"ion-item",10),c(34,"ion-icon",17),n(35,"ion-label",12),s(36,"Password"),o(),c(37,"ion-input",18),n(38,"ion-button",19),f("click",function(){return e.togglePasswordVisibility()}),c(39,"ion-icon",20),o()(),u(40,re,2,1,"ion-text",14),n(41,"ion-item",10),c(42,"ion-icon",17),n(43,"ion-label",12),s(44,"Confirm Password"),o(),c(45,"ion-input",21),n(46,"ion-button",19),f("click",function(){return e.toggleConfirmPasswordVisibility()}),c(47,"ion-icon",20),o()(),u(48,ae,2,1,"ion-text",14),n(49,"ion-button",22),u(50,se,1,0,"ion-spinner",23)(51,le,2,0,"span",24),o()(),n(52,"div",25)(53,"p"),s(54," Already have an account? "),n(55,"ion-button",26),f("click",function(){return e.navigateToLogin()}),s(56," Sign in here "),o()()()()()()()),t&2&&(l("translucent",!0),i(6),l("fullscreen",!0),i(14),l("formGroup",e.registerForm),i(),_("item-has-error",e.isFieldInvalid("name")),i(5),l("ngIf",e.isFieldInvalid("name")),i(),_("item-has-error",e.isFieldInvalid("email")),i(5),l("ngIf",e.isFieldInvalid("email")),i(),_("item-has-error",e.isFieldInvalid("password")),i(4),l("type",e.showPassword?"text":"password"),i(2),l("name",e.showPassword?"eye-off":"eye"),i(),l("ngIf",e.isFieldInvalid("password")),i(),_("item-has-error",e.isFieldInvalid("confirmPassword")),i(4),l("type",e.showConfirmPassword?"text":"password"),i(2),l("name",e.showConfirmPassword?"eye-off":"eye"),i(),l("ngIf",e.isFieldInvalid("confirmPassword")),i(),l("disabled",e.isLoading),i(),l("ngIf",e.isLoading),i(),l("ngIf",!e.isLoading))},dependencies:[w,b,F,I,y,O,k,S,V,q,D,U,R,L,j,z,A,N,Y,T,H,B,G],styles:[".register-content[_ngcontent-%COMP%]{--background: linear-gradient(135deg, var(--ion-color-secondary-tint), var(--ion-color-primary-tint))}.register-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:100vh;padding:20px;justify-content:center}.app-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:40px}.app-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]{margin-bottom:16px}.app-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:4rem;filter:drop-shadow(0 4px 8px rgba(0,0,0,.1))}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0 0 8px;font-size:2.5rem;font-weight:700;color:var(--ion-color-primary);text-shadow:0 2px 4px rgba(0,0,0,.1)}.app-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:1.1rem;font-weight:400}.register-card[_ngcontent-%COMP%]{border-radius:20px;box-shadow:0 10px 30px #0000001a;margin-bottom:20px;overflow:hidden}.register-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{background:var(--ion-color-light);text-align:center;padding:24px}.register-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:var(--ion-color-dark)}.register-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:24px}.form-item[_ngcontent-%COMP%]{--background: var(--ion-color-light);--border-radius: 12px;--padding-start: 16px;--padding-end: 16px;margin-bottom:16px;border:2px solid transparent;transition:all .3s ease}.form-item.item-has-error[_ngcontent-%COMP%]{--border-color: var(--ion-color-danger)}.form-item[_ngcontent-%COMP%]:focus-within{--border-color: var(--ion-color-primary);transform:translateY(-2px);box-shadow:0 4px 12px rgba(var(--ion-color-primary-rgb),.2)}.form-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-weight:500;color:var(--ion-color-dark);margin-bottom:4px}.form-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-top: 12px;--padding-bottom: 12px;font-size:1rem}.form-item[_ngcontent-%COMP%]   .password-toggle[_ngcontent-%COMP%]{--padding-start: 8px;--padding-end: 8px;margin:0}.error-text[_ngcontent-%COMP%]{display:block;font-size:.875rem;margin:-12px 0 16px 16px;font-weight:500}.signup-button[_ngcontent-%COMP%]{--background: var(--ion-color-secondary);--border-radius: 12px;--box-shadow: 0 4px 12px rgba(var(--ion-color-secondary-rgb), .3);height:56px;font-weight:600;font-size:1.1rem;margin:24px 0 16px;transition:all .3s ease}.signup-button[_ngcontent-%COMP%]:hover:not([disabled]){--background: var(--ion-color-secondary-shade);transform:translateY(-2px);--box-shadow: 0 6px 16px rgba(var(--ion-color-secondary-rgb), .4)}.signup-button[disabled][_ngcontent-%COMP%]{opacity:.6}.signup-button[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-right:8px}.signin-link[_ngcontent-%COMP%]{text-align:center;margin-top:24px}.signin-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:.95rem}.signin-link[_ngcontent-%COMP%]   .link-button[_ngcontent-%COMP%]{--color: var(--ion-color-primary);--padding-start: 4px;--padding-end: 4px;font-weight:600;text-decoration:underline}@media (prefers-color-scheme: dark){.register-content[_ngcontent-%COMP%]{--background: linear-gradient(135deg, var(--ion-color-secondary-shade), var(--ion-color-primary-shade))}.register-card[_ngcontent-%COMP%]{background:var(--ion-color-dark)}.register-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{background:var(--ion-color-dark-tint)}.form-item[_ngcontent-%COMP%]{--background: var(--ion-color-dark-tint)}}@media (max-width: 768px){.register-container[_ngcontent-%COMP%]{padding:16px}.app-header[_ngcontent-%COMP%]{margin-bottom:32px}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.app-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem}.register-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:20px}}@media (max-height: 700px){.register-container[_ngcontent-%COMP%]{justify-content:flex-start;padding-top:40px}.app-header[_ngcontent-%COMP%]{margin-bottom:24px}}"]});let r=d;return r})();export{Ce as RegisterPage};
