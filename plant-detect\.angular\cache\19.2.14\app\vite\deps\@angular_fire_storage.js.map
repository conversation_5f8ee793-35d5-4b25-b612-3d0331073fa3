{"version": 3, "sources": ["../../../../../../node_modules/@firebase/storage/dist/index.esm2017.js", "../../../../../../node_modules/rxfire/storage/index.esm.js", "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-storage.mjs"], "sourcesContent": ["import { _isFirebase<PERSON>erverApp, _getProvider, getApp, _registerComponent, registerVersion, SDK_VERSION } from '@firebase/app';\nimport { FirebaseError, isCloudWorkstation, pingServer, updateEmulatorBanner, createMockUserToken, getModularInstance, getDefaultEmulatorHostnameAndPort } from '@firebase/util';\nimport { Component } from '@firebase/component';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n/**\n * Domain name for firebase storage.\n */\nconst DEFAULT_HOST = 'firebasestorage.googleapis.com';\n/**\n * The key in Firebase config json for the storage bucket.\n */\nconst CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nconst DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nconst DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n/**\n * 1 second\n */\nconst DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nclass StorageError extends FirebaseError {\n  /**\n   * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n   *  added to the end of the message.\n   * @param message  - Error message.\n   * @param status_ - Corresponding HTTP Status Code\n   */\n  constructor(code, message, status_ = 0) {\n    super(prependCode(code), `Firebase Storage: ${message} (${prependCode(code)})`);\n    this.status_ = status_;\n    /**\n     * Stores custom error data unique to the `StorageError`.\n     */\n    this.customData = {\n      serverResponse: null\n    };\n    this._baseMessage = this.message;\n    // Without this, `instanceof StorageError`, in tests for example,\n    // returns false.\n    Object.setPrototypeOf(this, StorageError.prototype);\n  }\n  get status() {\n    return this.status_;\n  }\n  set status(status) {\n    this.status_ = status;\n  }\n  /**\n   * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n   */\n  _codeEquals(code) {\n    return prependCode(code) === this.code;\n  }\n  /**\n   * Optional response message that was added by the server.\n   */\n  get serverResponse() {\n    return this.customData.serverResponse;\n  }\n  set serverResponse(serverResponse) {\n    this.customData.serverResponse = serverResponse;\n    if (this.customData.serverResponse) {\n      this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n    } else {\n      this.message = this._baseMessage;\n    }\n  }\n}\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nvar StorageErrorCode;\n(function (StorageErrorCode) {\n  // Shared between all platforms\n  StorageErrorCode[\"UNKNOWN\"] = \"unknown\";\n  StorageErrorCode[\"OBJECT_NOT_FOUND\"] = \"object-not-found\";\n  StorageErrorCode[\"BUCKET_NOT_FOUND\"] = \"bucket-not-found\";\n  StorageErrorCode[\"PROJECT_NOT_FOUND\"] = \"project-not-found\";\n  StorageErrorCode[\"QUOTA_EXCEEDED\"] = \"quota-exceeded\";\n  StorageErrorCode[\"UNAUTHENTICATED\"] = \"unauthenticated\";\n  StorageErrorCode[\"UNAUTHORIZED\"] = \"unauthorized\";\n  StorageErrorCode[\"UNAUTHORIZED_APP\"] = \"unauthorized-app\";\n  StorageErrorCode[\"RETRY_LIMIT_EXCEEDED\"] = \"retry-limit-exceeded\";\n  StorageErrorCode[\"INVALID_CHECKSUM\"] = \"invalid-checksum\";\n  StorageErrorCode[\"CANCELED\"] = \"canceled\";\n  // JS specific\n  StorageErrorCode[\"INVALID_EVENT_NAME\"] = \"invalid-event-name\";\n  StorageErrorCode[\"INVALID_URL\"] = \"invalid-url\";\n  StorageErrorCode[\"INVALID_DEFAULT_BUCKET\"] = \"invalid-default-bucket\";\n  StorageErrorCode[\"NO_DEFAULT_BUCKET\"] = \"no-default-bucket\";\n  StorageErrorCode[\"CANNOT_SLICE_BLOB\"] = \"cannot-slice-blob\";\n  StorageErrorCode[\"SERVER_FILE_WRONG_SIZE\"] = \"server-file-wrong-size\";\n  StorageErrorCode[\"NO_DOWNLOAD_URL\"] = \"no-download-url\";\n  StorageErrorCode[\"INVALID_ARGUMENT\"] = \"invalid-argument\";\n  StorageErrorCode[\"INVALID_ARGUMENT_COUNT\"] = \"invalid-argument-count\";\n  StorageErrorCode[\"APP_DELETED\"] = \"app-deleted\";\n  StorageErrorCode[\"INVALID_ROOT_OPERATION\"] = \"invalid-root-operation\";\n  StorageErrorCode[\"INVALID_FORMAT\"] = \"invalid-format\";\n  StorageErrorCode[\"INTERNAL_ERROR\"] = \"internal-error\";\n  StorageErrorCode[\"UNSUPPORTED_ENVIRONMENT\"] = \"unsupported-environment\";\n})(StorageErrorCode || (StorageErrorCode = {}));\nfunction prependCode(code) {\n  return 'storage/' + code;\n}\nfunction unknown() {\n  const message = 'An unknown error occurred, please check the error payload for ' + 'server response.';\n  return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\nfunction objectNotFound(path) {\n  return new StorageError(StorageErrorCode.OBJECT_NOT_FOUND, \"Object '\" + path + \"' does not exist.\");\n}\nfunction quotaExceeded(bucket) {\n  return new StorageError(StorageErrorCode.QUOTA_EXCEEDED, \"Quota for bucket '\" + bucket + \"' exceeded, please view quota on \" + 'https://firebase.google.com/pricing/.');\n}\nfunction unauthenticated() {\n  const message = 'User is not authenticated, please authenticate using Firebase ' + 'Authentication and try again.';\n  return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\nfunction unauthorizedApp() {\n  return new StorageError(StorageErrorCode.UNAUTHORIZED_APP, 'This app does not have permission to access Firebase Storage on this project.');\n}\nfunction unauthorized(path) {\n  return new StorageError(StorageErrorCode.UNAUTHORIZED, \"User does not have permission to access '\" + path + \"'.\");\n}\nfunction retryLimitExceeded() {\n  return new StorageError(StorageErrorCode.RETRY_LIMIT_EXCEEDED, 'Max retry time for operation exceeded, please try again.');\n}\nfunction canceled() {\n  return new StorageError(StorageErrorCode.CANCELED, 'User canceled the upload/download.');\n}\nfunction invalidUrl(url) {\n  return new StorageError(StorageErrorCode.INVALID_URL, \"Invalid URL '\" + url + \"'.\");\n}\nfunction invalidDefaultBucket(bucket) {\n  return new StorageError(StorageErrorCode.INVALID_DEFAULT_BUCKET, \"Invalid default bucket '\" + bucket + \"'.\");\n}\nfunction noDefaultBucket() {\n  return new StorageError(StorageErrorCode.NO_DEFAULT_BUCKET, 'No default bucket ' + \"found. Did you set the '\" + CONFIG_STORAGE_BUCKET_KEY + \"' property when initializing the app?\");\n}\nfunction cannotSliceBlob() {\n  return new StorageError(StorageErrorCode.CANNOT_SLICE_BLOB, 'Cannot slice blob for upload. Please retry the upload.');\n}\nfunction serverFileWrongSize() {\n  return new StorageError(StorageErrorCode.SERVER_FILE_WRONG_SIZE, 'Server recorded incorrect upload file size, please retry the upload.');\n}\nfunction noDownloadURL() {\n  return new StorageError(StorageErrorCode.NO_DOWNLOAD_URL, 'The given file does not have any download URLs.');\n}\nfunction missingPolyFill(polyFill) {\n  return new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, `${polyFill} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`);\n}\n/**\n * @internal\n */\nfunction invalidArgument(message) {\n  return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\nfunction appDeleted() {\n  return new StorageError(StorageErrorCode.APP_DELETED, 'The Firebase app was deleted.');\n}\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nfunction invalidRootOperation(name) {\n  return new StorageError(StorageErrorCode.INVALID_ROOT_OPERATION, \"The operation '\" + name + \"' cannot be performed on a root reference, create a non-root \" + \"reference using child, such as .child('file.png').\");\n}\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nfunction invalidFormat(format, message) {\n  return new StorageError(StorageErrorCode.INVALID_FORMAT, \"String does not match format '\" + format + \"': \" + message);\n}\n/**\n * @param message - A message describing the internal error.\n */\nfunction internalError(message) {\n  throw new StorageError(StorageErrorCode.INTERNAL_ERROR, 'Internal error: ' + message);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nclass Location {\n  constructor(bucket, path) {\n    this.bucket = bucket;\n    this.path_ = path;\n  }\n  get path() {\n    return this.path_;\n  }\n  get isRoot() {\n    return this.path.length === 0;\n  }\n  fullServerUrl() {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n  }\n  bucketOnlyServerUrl() {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o';\n  }\n  static makeFromBucketSpec(bucketString, host) {\n    let bucketLocation;\n    try {\n      bucketLocation = Location.makeFromUrl(bucketString, host);\n    } catch (e) {\n      // Not valid URL, use as-is. This lets you put bare bucket names in\n      // config.\n      return new Location(bucketString, '');\n    }\n    if (bucketLocation.path === '') {\n      return bucketLocation;\n    } else {\n      throw invalidDefaultBucket(bucketString);\n    }\n  }\n  static makeFromUrl(url, host) {\n    let location = null;\n    const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n    function gsModify(loc) {\n      if (loc.path.charAt(loc.path.length - 1) === '/') {\n        loc.path_ = loc.path_.slice(0, -1);\n      }\n    }\n    const gsPath = '(/(.*))?$';\n    const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n    const gsIndices = {\n      bucket: 1,\n      path: 3\n    };\n    function httpModify(loc) {\n      loc.path_ = decodeURIComponent(loc.path);\n    }\n    const version = 'v[A-Za-z0-9_]+';\n    const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n    const firebaseStoragePath = '(/([^?#]*).*)?$';\n    const firebaseStorageRegExp = new RegExp(`^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`, 'i');\n    const firebaseStorageIndices = {\n      bucket: 1,\n      path: 3\n    };\n    const cloudStorageHost = host === DEFAULT_HOST ? '(?:storage.googleapis.com|storage.cloud.google.com)' : host;\n    const cloudStoragePath = '([^?#]*)';\n    const cloudStorageRegExp = new RegExp(`^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`, 'i');\n    const cloudStorageIndices = {\n      bucket: 1,\n      path: 2\n    };\n    const groups = [{\n      regex: gsRegex,\n      indices: gsIndices,\n      postModify: gsModify\n    }, {\n      regex: firebaseStorageRegExp,\n      indices: firebaseStorageIndices,\n      postModify: httpModify\n    }, {\n      regex: cloudStorageRegExp,\n      indices: cloudStorageIndices,\n      postModify: httpModify\n    }];\n    for (let i = 0; i < groups.length; i++) {\n      const group = groups[i];\n      const captures = group.regex.exec(url);\n      if (captures) {\n        const bucketValue = captures[group.indices.bucket];\n        let pathValue = captures[group.indices.path];\n        if (!pathValue) {\n          pathValue = '';\n        }\n        location = new Location(bucketValue, pathValue);\n        group.postModify(location);\n        break;\n      }\n    }\n    if (location == null) {\n      throw invalidUrl(url);\n    }\n    return location;\n  }\n}\n\n/**\n * A request whose promise always fails.\n */\nclass FailRequest {\n  constructor(error) {\n    this.promise_ = Promise.reject(error);\n  }\n  /** @inheritDoc */\n  getPromise() {\n    return this.promise_;\n  }\n  /** @inheritDoc */\n  cancel(_appDelete = false) {}\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nfunction start(doRequest,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nbackoffCompleteCb, timeout) {\n  // TODO(andysoto): make this code cleaner (probably refactor into an actual\n  // type instead of a bunch of functions with state shared in the closure)\n  let waitSeconds = 1;\n  // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n  // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let retryTimeoutId = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let globalTimeoutId = null;\n  let hitTimeout = false;\n  let cancelState = 0;\n  function canceled() {\n    return cancelState === 2;\n  }\n  let triggeredCallback = false;\n  function triggerCallback(...args) {\n    if (!triggeredCallback) {\n      triggeredCallback = true;\n      backoffCompleteCb.apply(null, args);\n    }\n  }\n  function callWithDelay(millis) {\n    retryTimeoutId = setTimeout(() => {\n      retryTimeoutId = null;\n      doRequest(responseHandler, canceled());\n    }, millis);\n  }\n  function clearGlobalTimeout() {\n    if (globalTimeoutId) {\n      clearTimeout(globalTimeoutId);\n    }\n  }\n  function responseHandler(success, ...args) {\n    if (triggeredCallback) {\n      clearGlobalTimeout();\n      return;\n    }\n    if (success) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    const mustStop = canceled() || hitTimeout;\n    if (mustStop) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    if (waitSeconds < 64) {\n      /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n      waitSeconds *= 2;\n    }\n    let waitMillis;\n    if (cancelState === 1) {\n      cancelState = 2;\n      waitMillis = 0;\n    } else {\n      waitMillis = (waitSeconds + Math.random()) * 1000;\n    }\n    callWithDelay(waitMillis);\n  }\n  let stopped = false;\n  function stop(wasTimeout) {\n    if (stopped) {\n      return;\n    }\n    stopped = true;\n    clearGlobalTimeout();\n    if (triggeredCallback) {\n      return;\n    }\n    if (retryTimeoutId !== null) {\n      if (!wasTimeout) {\n        cancelState = 2;\n      }\n      clearTimeout(retryTimeoutId);\n      callWithDelay(0);\n    } else {\n      if (!wasTimeout) {\n        cancelState = 1;\n      }\n    }\n  }\n  callWithDelay(0);\n  globalTimeoutId = setTimeout(() => {\n    hitTimeout = true;\n    stop(true);\n  }, timeout);\n  return stop;\n}\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nfunction stop(id) {\n  id(false);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isJustDef(p) {\n  return p !== void 0;\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isFunction(p) {\n  return typeof p === 'function';\n}\nfunction isNonArrayObject(p) {\n  return typeof p === 'object' && !Array.isArray(p);\n}\nfunction isString(p) {\n  return typeof p === 'string' || p instanceof String;\n}\nfunction isNativeBlob(p) {\n  return isNativeBlobDefined() && p instanceof Blob;\n}\nfunction isNativeBlobDefined() {\n  return typeof Blob !== 'undefined';\n}\nfunction validateNumber(argument, minValue, maxValue, value) {\n  if (value < minValue) {\n    throw invalidArgument(`Invalid value for '${argument}'. Expected ${minValue} or greater.`);\n  }\n  if (value > maxValue) {\n    throw invalidArgument(`Invalid value for '${argument}'. Expected ${maxValue} or less.`);\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction makeUrl(urlPart, host, protocol) {\n  let origin = host;\n  if (protocol == null) {\n    origin = `https://${host}`;\n  }\n  return `${protocol}://${origin}/v0${urlPart}`;\n}\nfunction makeQueryString(params) {\n  const encode = encodeURIComponent;\n  let queryPart = '?';\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      const nextPart = encode(key) + '=' + encode(params[key]);\n      queryPart = queryPart + nextPart + '&';\n    }\n  }\n  // Chop off the extra '&' or '?' on the end\n  queryPart = queryPart.slice(0, -1);\n  return queryPart;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nvar ErrorCode;\n(function (ErrorCode) {\n  ErrorCode[ErrorCode[\"NO_ERROR\"] = 0] = \"NO_ERROR\";\n  ErrorCode[ErrorCode[\"NETWORK_ERROR\"] = 1] = \"NETWORK_ERROR\";\n  ErrorCode[ErrorCode[\"ABORT\"] = 2] = \"ABORT\";\n})(ErrorCode || (ErrorCode = {}));\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nfunction isRetryStatusCode(status, additionalRetryCodes) {\n  // The codes for which to retry came from this page:\n  // https://cloud.google.com/storage/docs/exponential-backoff\n  const isFiveHundredCode = status >= 500 && status < 600;\n  const extraRetryCodes = [\n  // Request Timeout: web server didn't receive full request in time.\n  408,\n  // Too Many Requests: you're getting rate-limited, basically.\n  429];\n  const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n  const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n  return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest {\n  constructor(url_, method_, headers_, body_, successCodes_, additionalRetryCodes_, callback_, errorCallback_, timeout_, progressCallback_, connectionFactory_, retry = true, isUsingEmulator = false) {\n    this.url_ = url_;\n    this.method_ = method_;\n    this.headers_ = headers_;\n    this.body_ = body_;\n    this.successCodes_ = successCodes_;\n    this.additionalRetryCodes_ = additionalRetryCodes_;\n    this.callback_ = callback_;\n    this.errorCallback_ = errorCallback_;\n    this.timeout_ = timeout_;\n    this.progressCallback_ = progressCallback_;\n    this.connectionFactory_ = connectionFactory_;\n    this.retry = retry;\n    this.isUsingEmulator = isUsingEmulator;\n    this.pendingConnection_ = null;\n    this.backoffId_ = null;\n    this.canceled_ = false;\n    this.appDelete_ = false;\n    this.promise_ = new Promise((resolve, reject) => {\n      this.resolve_ = resolve;\n      this.reject_ = reject;\n      this.start_();\n    });\n  }\n  /**\n   * Actually starts the retry loop.\n   */\n  start_() {\n    const doTheRequest = (backoffCallback, canceled) => {\n      if (canceled) {\n        backoffCallback(false, new RequestEndStatus(false, null, true));\n        return;\n      }\n      const connection = this.connectionFactory_();\n      this.pendingConnection_ = connection;\n      const progressListener = progressEvent => {\n        const loaded = progressEvent.loaded;\n        const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n        if (this.progressCallback_ !== null) {\n          this.progressCallback_(loaded, total);\n        }\n      };\n      if (this.progressCallback_ !== null) {\n        connection.addUploadProgressListener(progressListener);\n      }\n      // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      connection.send(this.url_, this.method_, this.isUsingEmulator, this.body_, this.headers_).then(() => {\n        if (this.progressCallback_ !== null) {\n          connection.removeUploadProgressListener(progressListener);\n        }\n        this.pendingConnection_ = null;\n        const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n        const status = connection.getStatus();\n        if (!hitServer || isRetryStatusCode(status, this.additionalRetryCodes_) && this.retry) {\n          const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n          backoffCallback(false, new RequestEndStatus(false, null, wasCanceled));\n          return;\n        }\n        const successCode = this.successCodes_.indexOf(status) !== -1;\n        backoffCallback(true, new RequestEndStatus(successCode, connection));\n      });\n    };\n    /**\n     * @param requestWentThrough - True if the request eventually went\n     *     through, false if it hit the retry limit or was canceled.\n     */\n    const backoffDone = (requestWentThrough, status) => {\n      const resolve = this.resolve_;\n      const reject = this.reject_;\n      const connection = status.connection;\n      if (status.wasSuccessCode) {\n        try {\n          const result = this.callback_(connection, connection.getResponse());\n          if (isJustDef(result)) {\n            resolve(result);\n          } else {\n            resolve();\n          }\n        } catch (e) {\n          reject(e);\n        }\n      } else {\n        if (connection !== null) {\n          const err = unknown();\n          err.serverResponse = connection.getErrorText();\n          if (this.errorCallback_) {\n            reject(this.errorCallback_(connection, err));\n          } else {\n            reject(err);\n          }\n        } else {\n          if (status.canceled) {\n            const err = this.appDelete_ ? appDeleted() : canceled();\n            reject(err);\n          } else {\n            const err = retryLimitExceeded();\n            reject(err);\n          }\n        }\n      }\n    };\n    if (this.canceled_) {\n      backoffDone(false, new RequestEndStatus(false, null, true));\n    } else {\n      this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n    }\n  }\n  /** @inheritDoc */\n  getPromise() {\n    return this.promise_;\n  }\n  /** @inheritDoc */\n  cancel(appDelete) {\n    this.canceled_ = true;\n    this.appDelete_ = appDelete || false;\n    if (this.backoffId_ !== null) {\n      stop(this.backoffId_);\n    }\n    if (this.pendingConnection_ !== null) {\n      this.pendingConnection_.abort();\n    }\n  }\n}\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nclass RequestEndStatus {\n  constructor(wasSuccessCode, connection, canceled) {\n    this.wasSuccessCode = wasSuccessCode;\n    this.connection = connection;\n    this.canceled = !!canceled;\n  }\n}\nfunction addAuthHeader_(headers, authToken) {\n  if (authToken !== null && authToken.length > 0) {\n    headers['Authorization'] = 'Firebase ' + authToken;\n  }\n}\nfunction addVersionHeader_(headers, firebaseVersion) {\n  headers['X-Firebase-Storage-Version'] = 'webjs/' + (firebaseVersion !== null && firebaseVersion !== void 0 ? firebaseVersion : 'AppManager');\n}\nfunction addGmpidHeader_(headers, appId) {\n  if (appId) {\n    headers['X-Firebase-GMPID'] = appId;\n  }\n}\nfunction addAppCheckHeader_(headers, appCheckToken) {\n  if (appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n}\nfunction makeRequest(requestInfo, appId, authToken, appCheckToken, requestFactory, firebaseVersion, retry = true, isUsingEmulator = false) {\n  const queryPart = makeQueryString(requestInfo.urlParams);\n  const url = requestInfo.url + queryPart;\n  const headers = Object.assign({}, requestInfo.headers);\n  addGmpidHeader_(headers, appId);\n  addAuthHeader_(headers, authToken);\n  addVersionHeader_(headers, firebaseVersion);\n  addAppCheckHeader_(headers, appCheckToken);\n  return new NetworkRequest(url, requestInfo.method, headers, requestInfo.body, requestInfo.successCodes, requestInfo.additionalRetryCodes, requestInfo.handler, requestInfo.errorHandler, requestInfo.timeout, requestInfo.progressCallback, requestFactory, retry, isUsingEmulator);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getBlobBuilder() {\n  if (typeof BlobBuilder !== 'undefined') {\n    return BlobBuilder;\n  } else if (typeof WebKitBlobBuilder !== 'undefined') {\n    return WebKitBlobBuilder;\n  } else {\n    return undefined;\n  }\n}\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nfunction getBlob$1(...args) {\n  const BlobBuilder = getBlobBuilder();\n  if (BlobBuilder !== undefined) {\n    const bb = new BlobBuilder();\n    for (let i = 0; i < args.length; i++) {\n      bb.append(args[i]);\n    }\n    return bb.getBlob();\n  } else {\n    if (isNativeBlobDefined()) {\n      return new Blob(args);\n    } else {\n      throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, \"This browser doesn't seem to support creating Blobs\");\n    }\n  }\n}\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nfunction sliceBlob(blob, start, end) {\n  if (blob.webkitSlice) {\n    return blob.webkitSlice(start, end);\n  } else if (blob.mozSlice) {\n    return blob.mozSlice(start, end);\n  } else if (blob.slice) {\n    return blob.slice(start, end);\n  }\n  return null;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Converts a Base64 encoded string to a binary string. */\nfunction decodeBase64(encoded) {\n  if (typeof atob === 'undefined') {\n    throw missingPolyFill('base-64');\n  }\n  return atob(encoded);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nconst StringFormat = {\n  /**\n   * Indicates the string should be interpreted \"raw\", that is, as normal text.\n   * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n   * sequence.\n   * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n   * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n   */\n  RAW: 'raw',\n  /**\n   * Indicates the string should be interpreted as base64-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64: 'base64',\n  /**\n   * Indicates the string should be interpreted as base64url-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64URL: 'base64url',\n  /**\n   * Indicates the string is a data URL, such as one obtained from\n   * canvas.toDataURL().\n   * Example: the string 'data:application/octet-stream;base64,aaaa'\n   * becomes the byte sequence\n   * 69 a6 9a\n   * (the content-type \"application/octet-stream\" is also applied, but can\n   * be overridden in the metadata object).\n   */\n  DATA_URL: 'data_url'\n};\nclass StringData {\n  constructor(data, contentType) {\n    this.data = data;\n    this.contentType = contentType || null;\n  }\n}\n/**\n * @internal\n */\nfunction dataFromString(format, stringData) {\n  switch (format) {\n    case StringFormat.RAW:\n      return new StringData(utf8Bytes_(stringData));\n    case StringFormat.BASE64:\n    case StringFormat.BASE64URL:\n      return new StringData(base64Bytes_(format, stringData));\n    case StringFormat.DATA_URL:\n      return new StringData(dataURLBytes_(stringData), dataURLContentType_(stringData));\n    // do nothing\n  }\n  // assert(false);\n  throw unknown();\n}\nfunction utf8Bytes_(value) {\n  const b = [];\n  for (let i = 0; i < value.length; i++) {\n    let c = value.charCodeAt(i);\n    if (c <= 127) {\n      b.push(c);\n    } else {\n      if (c <= 2047) {\n        b.push(192 | c >> 6, 128 | c & 63);\n      } else {\n        if ((c & 64512) === 55296) {\n          // The start of a surrogate pair.\n          const valid = i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n          if (!valid) {\n            // The second surrogate wasn't there.\n            b.push(239, 191, 189);\n          } else {\n            const hi = c;\n            const lo = value.charCodeAt(++i);\n            c = 65536 | (hi & 1023) << 10 | lo & 1023;\n            b.push(240 | c >> 18, 128 | c >> 12 & 63, 128 | c >> 6 & 63, 128 | c & 63);\n          }\n        } else {\n          if ((c & 64512) === 56320) {\n            // Invalid low surrogate.\n            b.push(239, 191, 189);\n          } else {\n            b.push(224 | c >> 12, 128 | c >> 6 & 63, 128 | c & 63);\n          }\n        }\n      }\n    }\n  }\n  return new Uint8Array(b);\n}\nfunction percentEncodedBytes_(value) {\n  let decoded;\n  try {\n    decoded = decodeURIComponent(value);\n  } catch (e) {\n    throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n  }\n  return utf8Bytes_(decoded);\n}\nfunction base64Bytes_(format, value) {\n  switch (format) {\n    case StringFormat.BASE64:\n      {\n        const hasMinus = value.indexOf('-') !== -1;\n        const hasUnder = value.indexOf('_') !== -1;\n        if (hasMinus || hasUnder) {\n          const invalidChar = hasMinus ? '-' : '_';\n          throw invalidFormat(format, \"Invalid character '\" + invalidChar + \"' found: is it base64url encoded?\");\n        }\n        break;\n      }\n    case StringFormat.BASE64URL:\n      {\n        const hasPlus = value.indexOf('+') !== -1;\n        const hasSlash = value.indexOf('/') !== -1;\n        if (hasPlus || hasSlash) {\n          const invalidChar = hasPlus ? '+' : '/';\n          throw invalidFormat(format, \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\");\n        }\n        value = value.replace(/-/g, '+').replace(/_/g, '/');\n        break;\n      }\n    // do nothing\n  }\n  let bytes;\n  try {\n    bytes = decodeBase64(value);\n  } catch (e) {\n    if (e.message.includes('polyfill')) {\n      throw e;\n    }\n    throw invalidFormat(format, 'Invalid character found');\n  }\n  const array = new Uint8Array(bytes.length);\n  for (let i = 0; i < bytes.length; i++) {\n    array[i] = bytes.charCodeAt(i);\n  }\n  return array;\n}\nclass DataURLParts {\n  constructor(dataURL) {\n    this.base64 = false;\n    this.contentType = null;\n    const matches = dataURL.match(/^data:([^,]+)?,/);\n    if (matches === null) {\n      throw invalidFormat(StringFormat.DATA_URL, \"Must be formatted 'data:[<mediatype>][;base64],<data>\");\n    }\n    const middle = matches[1] || null;\n    if (middle != null) {\n      this.base64 = endsWith(middle, ';base64');\n      this.contentType = this.base64 ? middle.substring(0, middle.length - ';base64'.length) : middle;\n    }\n    this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n  }\n}\nfunction dataURLBytes_(dataUrl) {\n  const parts = new DataURLParts(dataUrl);\n  if (parts.base64) {\n    return base64Bytes_(StringFormat.BASE64, parts.rest);\n  } else {\n    return percentEncodedBytes_(parts.rest);\n  }\n}\nfunction dataURLContentType_(dataUrl) {\n  const parts = new DataURLParts(dataUrl);\n  return parts.contentType;\n}\nfunction endsWith(s, end) {\n  const longEnough = s.length >= end.length;\n  if (!longEnough) {\n    return false;\n  }\n  return s.substring(s.length - end.length) === end;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nclass FbsBlob {\n  constructor(data, elideCopy) {\n    let size = 0;\n    let blobType = '';\n    if (isNativeBlob(data)) {\n      this.data_ = data;\n      size = data.size;\n      blobType = data.type;\n    } else if (data instanceof ArrayBuffer) {\n      if (elideCopy) {\n        this.data_ = new Uint8Array(data);\n      } else {\n        this.data_ = new Uint8Array(data.byteLength);\n        this.data_.set(new Uint8Array(data));\n      }\n      size = this.data_.length;\n    } else if (data instanceof Uint8Array) {\n      if (elideCopy) {\n        this.data_ = data;\n      } else {\n        this.data_ = new Uint8Array(data.length);\n        this.data_.set(data);\n      }\n      size = data.length;\n    }\n    this.size_ = size;\n    this.type_ = blobType;\n  }\n  size() {\n    return this.size_;\n  }\n  type() {\n    return this.type_;\n  }\n  slice(startByte, endByte) {\n    if (isNativeBlob(this.data_)) {\n      const realBlob = this.data_;\n      const sliced = sliceBlob(realBlob, startByte, endByte);\n      if (sliced === null) {\n        return null;\n      }\n      return new FbsBlob(sliced);\n    } else {\n      const slice = new Uint8Array(this.data_.buffer, startByte, endByte - startByte);\n      return new FbsBlob(slice, true);\n    }\n  }\n  static getBlob(...args) {\n    if (isNativeBlobDefined()) {\n      const blobby = args.map(val => {\n        if (val instanceof FbsBlob) {\n          return val.data_;\n        } else {\n          return val;\n        }\n      });\n      return new FbsBlob(getBlob$1.apply(null, blobby));\n    } else {\n      const uint8Arrays = args.map(val => {\n        if (isString(val)) {\n          return dataFromString(StringFormat.RAW, val).data;\n        } else {\n          // Blobs don't exist, so this has to be a Uint8Array.\n          return val.data_;\n        }\n      });\n      let finalLength = 0;\n      uint8Arrays.forEach(array => {\n        finalLength += array.byteLength;\n      });\n      const merged = new Uint8Array(finalLength);\n      let index = 0;\n      uint8Arrays.forEach(array => {\n        for (let i = 0; i < array.length; i++) {\n          merged[index++] = array[i];\n        }\n      });\n      return new FbsBlob(merged, true);\n    }\n  }\n  uploadData() {\n    return this.data_;\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nfunction jsonObjectOrNull(s) {\n  let obj;\n  try {\n    obj = JSON.parse(s);\n  } catch (e) {\n    return null;\n  }\n  if (isNonArrayObject(obj)) {\n    return obj;\n  } else {\n    return null;\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n/**\n * @return Null if the path is already at the root.\n */\nfunction parent(path) {\n  if (path.length === 0) {\n    return null;\n  }\n  const index = path.lastIndexOf('/');\n  if (index === -1) {\n    return '';\n  }\n  const newPath = path.slice(0, index);\n  return newPath;\n}\nfunction child(path, childPath) {\n  const canonicalChildPath = childPath.split('/').filter(component => component.length > 0).join('/');\n  if (path.length === 0) {\n    return canonicalChildPath;\n  } else {\n    return path + '/' + canonicalChildPath;\n  }\n}\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nfunction lastComponent(path) {\n  const index = path.lastIndexOf('/', path.length - 2);\n  if (index === -1) {\n    return path;\n  } else {\n    return path.slice(index + 1);\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction noXform_(metadata, value) {\n  return value;\n}\nclass Mapping {\n  constructor(server, local, writable, xform) {\n    this.server = server;\n    this.local = local || server;\n    this.writable = !!writable;\n    this.xform = xform || noXform_;\n  }\n}\nlet mappings_ = null;\nfunction xformPath(fullPath) {\n  if (!isString(fullPath) || fullPath.length < 2) {\n    return fullPath;\n  } else {\n    return lastComponent(fullPath);\n  }\n}\nfunction getMappings() {\n  if (mappings_) {\n    return mappings_;\n  }\n  const mappings = [];\n  mappings.push(new Mapping('bucket'));\n  mappings.push(new Mapping('generation'));\n  mappings.push(new Mapping('metageneration'));\n  mappings.push(new Mapping('name', 'fullPath', true));\n  function mappingsXformPath(_metadata, fullPath) {\n    return xformPath(fullPath);\n  }\n  const nameMapping = new Mapping('name');\n  nameMapping.xform = mappingsXformPath;\n  mappings.push(nameMapping);\n  /**\n   * Coerces the second param to a number, if it is defined.\n   */\n  function xformSize(_metadata, size) {\n    if (size !== undefined) {\n      return Number(size);\n    } else {\n      return size;\n    }\n  }\n  const sizeMapping = new Mapping('size');\n  sizeMapping.xform = xformSize;\n  mappings.push(sizeMapping);\n  mappings.push(new Mapping('timeCreated'));\n  mappings.push(new Mapping('updated'));\n  mappings.push(new Mapping('md5Hash', null, true));\n  mappings.push(new Mapping('cacheControl', null, true));\n  mappings.push(new Mapping('contentDisposition', null, true));\n  mappings.push(new Mapping('contentEncoding', null, true));\n  mappings.push(new Mapping('contentLanguage', null, true));\n  mappings.push(new Mapping('contentType', null, true));\n  mappings.push(new Mapping('metadata', 'customMetadata', true));\n  mappings_ = mappings;\n  return mappings_;\n}\nfunction addRef(metadata, service) {\n  function generateRef() {\n    const bucket = metadata['bucket'];\n    const path = metadata['fullPath'];\n    const loc = new Location(bucket, path);\n    return service._makeStorageReference(loc);\n  }\n  Object.defineProperty(metadata, 'ref', {\n    get: generateRef\n  });\n}\nfunction fromResource(service, resource, mappings) {\n  const metadata = {};\n  metadata['type'] = 'file';\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    metadata[mapping.local] = mapping.xform(metadata, resource[mapping.server]);\n  }\n  addRef(metadata, service);\n  return metadata;\n}\nfunction fromResourceString(service, resourceString, mappings) {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj;\n  return fromResource(service, resource, mappings);\n}\nfunction downloadUrlFromResourceString(metadata, resourceString, host, protocol) {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  if (!isString(obj['downloadTokens'])) {\n    // This can happen if objects are uploaded through GCS and retrieved\n    // through list, so we don't want to throw an Error.\n    return null;\n  }\n  const tokens = obj['downloadTokens'];\n  if (tokens.length === 0) {\n    return null;\n  }\n  const encode = encodeURIComponent;\n  const tokensList = tokens.split(',');\n  const urls = tokensList.map(token => {\n    const bucket = metadata['bucket'];\n    const path = metadata['fullPath'];\n    const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n    const base = makeUrl(urlPart, host, protocol);\n    const queryString = makeQueryString({\n      alt: 'media',\n      token\n    });\n    return base + queryString;\n  });\n  return urls[0];\n}\nfunction toResourceString(metadata, mappings) {\n  const resource = {};\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    if (mapping.writable) {\n      resource[mapping.server] = metadata[mapping.local];\n    }\n  }\n  return JSON.stringify(resource);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\nfunction fromBackendResponse(service, bucket, resource) {\n  const listResult = {\n    prefixes: [],\n    items: [],\n    nextPageToken: resource['nextPageToken']\n  };\n  if (resource[PREFIXES_KEY]) {\n    for (const path of resource[PREFIXES_KEY]) {\n      const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n      const reference = service._makeStorageReference(new Location(bucket, pathWithoutTrailingSlash));\n      listResult.prefixes.push(reference);\n    }\n  }\n  if (resource[ITEMS_KEY]) {\n    for (const item of resource[ITEMS_KEY]) {\n      const reference = service._makeStorageReference(new Location(bucket, item['name']));\n      listResult.items.push(reference);\n    }\n  }\n  return listResult;\n}\nfunction fromResponseString(service, bucket, resourceString) {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj;\n  return fromBackendResponse(service, bucket, resource);\n}\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nclass RequestInfo {\n  constructor(url, method,\n  /**\n   * Returns the value with which to resolve the request's promise. Only called\n   * if the request is successful. Throw from this function to reject the\n   * returned Request's promise with the thrown error.\n   * Note: The XhrIo passed to this function may be reused after this callback\n   * returns. Do not keep a reference to it in any way.\n   */\n  handler, timeout) {\n    this.url = url;\n    this.method = method;\n    this.handler = handler;\n    this.timeout = timeout;\n    this.urlParams = {};\n    this.headers = {};\n    this.body = null;\n    this.errorHandler = null;\n    /**\n     * Called with the current number of bytes uploaded and total size (-1 if not\n     * computable) of the request body (i.e. used to report upload progress).\n     */\n    this.progressCallback = null;\n    this.successCodes = [200];\n    this.additionalRetryCodes = [];\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nfunction handlerCheck(cndn) {\n  if (!cndn) {\n    throw unknown();\n  }\n}\nfunction metadataHandler(service, mappings) {\n  function handler(xhr, text) {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return metadata;\n  }\n  return handler;\n}\nfunction listHandler(service, bucket) {\n  function handler(xhr, text) {\n    const listResult = fromResponseString(service, bucket, text);\n    handlerCheck(listResult !== null);\n    return listResult;\n  }\n  return handler;\n}\nfunction downloadUrlHandler(service, mappings) {\n  function handler(xhr, text) {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return downloadUrlFromResourceString(metadata, text, service.host, service._protocol);\n  }\n  return handler;\n}\nfunction sharedErrorHandler(location) {\n  function errorHandler(xhr, err) {\n    let newErr;\n    if (xhr.getStatus() === 401) {\n      if (\n      // This exact message string is the only consistent part of the\n      // server's error response that identifies it as an App Check error.\n      xhr.getErrorText().includes('Firebase App Check token is invalid')) {\n        newErr = unauthorizedApp();\n      } else {\n        newErr = unauthenticated();\n      }\n    } else {\n      if (xhr.getStatus() === 402) {\n        newErr = quotaExceeded(location.bucket);\n      } else {\n        if (xhr.getStatus() === 403) {\n          newErr = unauthorized(location.path);\n        } else {\n          newErr = err;\n        }\n      }\n    }\n    newErr.status = xhr.getStatus();\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\nfunction objectErrorHandler(location) {\n  const shared = sharedErrorHandler(location);\n  function errorHandler(xhr, err) {\n    let newErr = shared(xhr, err);\n    if (xhr.getStatus() === 404) {\n      newErr = objectNotFound(location.path);\n    }\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\nfunction getMetadata$2(service, location, mappings) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\nfunction list$2(service, location, delimiter, pageToken, maxResults) {\n  const urlParams = {};\n  if (location.isRoot) {\n    urlParams['prefix'] = '';\n  } else {\n    urlParams['prefix'] = location.path + '/';\n  }\n  if (delimiter && delimiter.length > 0) {\n    urlParams['delimiter'] = delimiter;\n  }\n  if (pageToken) {\n    urlParams['pageToken'] = pageToken;\n  }\n  if (maxResults) {\n    urlParams['maxResults'] = maxResults;\n  }\n  const urlPart = location.bucketOnlyServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, listHandler(service, location.bucket), timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\nfunction getBytes$1(service, location, maxDownloadSizeBytes) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, (_, data) => data, timeout);\n  requestInfo.errorHandler = objectErrorHandler(location);\n  if (maxDownloadSizeBytes !== undefined) {\n    requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n    requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n  }\n  return requestInfo;\n}\nfunction getDownloadUrl(service, location, mappings) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, downloadUrlHandler(service, mappings), timeout);\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\nfunction updateMetadata$2(service, location, metadata, mappings) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'PATCH';\n  const body = toResourceString(metadata, mappings);\n  const headers = {\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\nfunction deleteObject$2(service, location) {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'DELETE';\n  const timeout = service.maxOperationRetryTime;\n  function handler(_xhr, _text) {}\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.successCodes = [200, 204];\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\nfunction determineContentType_(metadata, blob) {\n  return metadata && metadata['contentType'] || blob && blob.type() || 'application/octet-stream';\n}\nfunction metadataForUpload_(location, blob, metadata) {\n  const metadataClone = Object.assign({}, metadata);\n  metadataClone['fullPath'] = location.path;\n  metadataClone['size'] = blob.size();\n  if (!metadataClone['contentType']) {\n    metadataClone['contentType'] = determineContentType_(null, blob);\n  }\n  return metadataClone;\n}\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nfunction multipartUpload(service, location, mappings, blob, metadata) {\n  const urlPart = location.bucketOnlyServerUrl();\n  const headers = {\n    'X-Goog-Upload-Protocol': 'multipart'\n  };\n  function genBoundary() {\n    let str = '';\n    for (let i = 0; i < 2; i++) {\n      str = str + Math.random().toString().slice(2);\n    }\n    return str;\n  }\n  const boundary = genBoundary();\n  headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n  const metadata_ = metadataForUpload_(location, blob, metadata);\n  const metadataString = toResourceString(metadata_, mappings);\n  const preBlobPart = '--' + boundary + '\\r\\n' + 'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' + metadataString + '\\r\\n--' + boundary + '\\r\\n' + 'Content-Type: ' + metadata_['contentType'] + '\\r\\n\\r\\n';\n  const postBlobPart = '\\r\\n--' + boundary + '--';\n  const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  const urlParams = {\n    name: metadata_['fullPath']\n  };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nclass ResumableUploadStatus {\n  constructor(current, total, finalized, metadata) {\n    this.current = current;\n    this.total = total;\n    this.finalized = !!finalized;\n    this.metadata = metadata || null;\n  }\n}\nfunction checkResumeHeader_(xhr, allowed) {\n  let status = null;\n  try {\n    status = xhr.getResponseHeader('X-Goog-Upload-Status');\n  } catch (e) {\n    handlerCheck(false);\n  }\n  const allowedStatus = allowed || ['active'];\n  handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n  return status;\n}\nfunction createResumableUpload(service, location, mappings, blob, metadata) {\n  const urlPart = location.bucketOnlyServerUrl();\n  const metadataForUpload = metadataForUpload_(location, blob, metadata);\n  const urlParams = {\n    name: metadataForUpload['fullPath']\n  };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const headers = {\n    'X-Goog-Upload-Protocol': 'resumable',\n    'X-Goog-Upload-Command': 'start',\n    'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n    'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType'],\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const body = toResourceString(metadataForUpload, mappings);\n  const timeout = service.maxUploadRetryTime;\n  function handler(xhr) {\n    checkResumeHeader_(xhr);\n    let url;\n    try {\n      url = xhr.getResponseHeader('X-Goog-Upload-URL');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    handlerCheck(isString(url));\n    return url;\n  }\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nfunction getResumableUploadStatus(service, location, url, blob) {\n  const headers = {\n    'X-Goog-Upload-Command': 'query'\n  };\n  function handler(xhr) {\n    const status = checkResumeHeader_(xhr, ['active', 'final']);\n    let sizeString = null;\n    try {\n      sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    if (!sizeString) {\n      // null or empty string\n      handlerCheck(false);\n    }\n    const size = Number(sizeString);\n    handlerCheck(!isNaN(size));\n    return new ResumableUploadStatus(size, blob.size(), status === 'final');\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nconst RESUMABLE_UPLOAD_CHUNK_SIZE = 256 * 1024;\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nfunction continueResumableUpload(location, service, url, blob, chunkSize, mappings, status, progressCallback) {\n  // TODO(andysoto): standardize on internal asserts\n  // assert(!(opt_status && opt_status.finalized));\n  const status_ = new ResumableUploadStatus(0, 0);\n  if (status) {\n    status_.current = status.current;\n    status_.total = status.total;\n  } else {\n    status_.current = 0;\n    status_.total = blob.size();\n  }\n  if (blob.size() !== status_.total) {\n    throw serverFileWrongSize();\n  }\n  const bytesLeft = status_.total - status_.current;\n  let bytesToUpload = bytesLeft;\n  if (chunkSize > 0) {\n    bytesToUpload = Math.min(bytesToUpload, chunkSize);\n  }\n  const startByte = status_.current;\n  const endByte = startByte + bytesToUpload;\n  let uploadCommand = '';\n  if (bytesToUpload === 0) {\n    uploadCommand = 'finalize';\n  } else if (bytesLeft === bytesToUpload) {\n    uploadCommand = 'upload, finalize';\n  } else {\n    uploadCommand = 'upload';\n  }\n  const headers = {\n    'X-Goog-Upload-Command': uploadCommand,\n    'X-Goog-Upload-Offset': `${status_.current}`\n  };\n  const body = blob.slice(startByte, endByte);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  function handler(xhr, text) {\n    // TODO(andysoto): Verify the MD5 of each uploaded range:\n    // the 'x-range-md5' header comes back with status code 308 responses.\n    // We'll only be able to bail out though, because you can't re-upload a\n    // range that you previously uploaded.\n    const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n    const newCurrent = status_.current + bytesToUpload;\n    const size = blob.size();\n    let metadata;\n    if (uploadStatus === 'final') {\n      metadata = metadataHandler(service, mappings)(xhr, text);\n    } else {\n      metadata = null;\n    }\n    return new ResumableUploadStatus(newCurrent, size, uploadStatus === 'final', metadata);\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.progressCallback = progressCallback || null;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An event that is triggered on a task.\n * @internal\n */\nconst TaskEvent = {\n  /**\n   * For this event,\n   * <ul>\n   *   <li>The `next` function is triggered on progress updates and when the\n   *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n   *       argument.</li>\n   *   <li>The `error` function is triggered if the upload is canceled or fails\n   *       for another reason.</li>\n   *   <li>The `complete` function is triggered if the upload completes\n   *       successfully.</li>\n   * </ul>\n   */\n  STATE_CHANGED: 'state_changed'\n};\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nconst TaskState = {\n  /** The task is currently transferring data. */\n  RUNNING: 'running',\n  /** The task was paused by the user. */\n  PAUSED: 'paused',\n  /** The task completed successfully. */\n  SUCCESS: 'success',\n  /** The task was canceled. */\n  CANCELED: 'canceled',\n  /** The task failed with an error. */\n  ERROR: 'error'\n};\nfunction taskStateFromInternalTaskState(state) {\n  switch (state) {\n    case \"running\" /* InternalTaskState.RUNNING */:\n    case \"pausing\" /* InternalTaskState.PAUSING */:\n    case \"canceling\" /* InternalTaskState.CANCELING */:\n      return TaskState.RUNNING;\n    case \"paused\" /* InternalTaskState.PAUSED */:\n      return TaskState.PAUSED;\n    case \"success\" /* InternalTaskState.SUCCESS */:\n      return TaskState.SUCCESS;\n    case \"canceled\" /* InternalTaskState.CANCELED */:\n      return TaskState.CANCELED;\n    case \"error\" /* InternalTaskState.ERROR */:\n      return TaskState.ERROR;\n    default:\n      // TODO(andysoto): assert(false);\n      return TaskState.ERROR;\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Observer {\n  constructor(nextOrObserver, error, complete) {\n    const asFunctions = isFunction(nextOrObserver) || error != null || complete != null;\n    if (asFunctions) {\n      this.next = nextOrObserver;\n      this.error = error !== null && error !== void 0 ? error : undefined;\n      this.complete = complete !== null && complete !== void 0 ? complete : undefined;\n    } else {\n      const observer = nextOrObserver;\n      this.next = observer.next;\n      this.error = observer.error;\n      this.complete = observer.complete;\n    }\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(f) {\n  return (...argsToForward) => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.resolve().then(() => f(...argsToForward));\n  };\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride = null;\n/**\n * Network layer for browsers. We use this instead of goog.net.XhrIo because\n * goog.net.XhrIo is hyuuuuge and doesn't work in React Native on Android.\n */\nclass XhrConnection {\n  constructor() {\n    this.sent_ = false;\n    this.xhr_ = new XMLHttpRequest();\n    this.initXhr();\n    this.errorCode_ = ErrorCode.NO_ERROR;\n    this.sendPromise_ = new Promise(resolve => {\n      this.xhr_.addEventListener('abort', () => {\n        this.errorCode_ = ErrorCode.ABORT;\n        resolve();\n      });\n      this.xhr_.addEventListener('error', () => {\n        this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        resolve();\n      });\n      this.xhr_.addEventListener('load', () => {\n        resolve();\n      });\n    });\n  }\n  send(url, method, isUsingEmulator, body, headers) {\n    if (this.sent_) {\n      throw internalError('cannot .send() more than once');\n    }\n    if (isCloudWorkstation(url) && isUsingEmulator) {\n      this.xhr_.withCredentials = true;\n    }\n    this.sent_ = true;\n    this.xhr_.open(method, url, true);\n    if (headers !== undefined) {\n      for (const key in headers) {\n        if (headers.hasOwnProperty(key)) {\n          this.xhr_.setRequestHeader(key, headers[key].toString());\n        }\n      }\n    }\n    if (body !== undefined) {\n      this.xhr_.send(body);\n    } else {\n      this.xhr_.send();\n    }\n    return this.sendPromise_;\n  }\n  getErrorCode() {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorCode() before sending');\n    }\n    return this.errorCode_;\n  }\n  getStatus() {\n    if (!this.sent_) {\n      throw internalError('cannot .getStatus() before sending');\n    }\n    try {\n      return this.xhr_.status;\n    } catch (e) {\n      return -1;\n    }\n  }\n  getResponse() {\n    if (!this.sent_) {\n      throw internalError('cannot .getResponse() before sending');\n    }\n    return this.xhr_.response;\n  }\n  getErrorText() {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorText() before sending');\n    }\n    return this.xhr_.statusText;\n  }\n  /** Aborts the request. */\n  abort() {\n    this.xhr_.abort();\n  }\n  getResponseHeader(header) {\n    return this.xhr_.getResponseHeader(header);\n  }\n  addUploadProgressListener(listener) {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.addEventListener('progress', listener);\n    }\n  }\n  removeUploadProgressListener(listener) {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.removeEventListener('progress', listener);\n    }\n  }\n}\nclass XhrTextConnection extends XhrConnection {\n  initXhr() {\n    this.xhr_.responseType = 'text';\n  }\n}\nfunction newTextConnection() {\n  return textFactoryOverride ? textFactoryOverride() : new XhrTextConnection();\n}\nclass XhrBytesConnection extends XhrConnection {\n  initXhr() {\n    this.xhr_.responseType = 'arraybuffer';\n  }\n}\nfunction newBytesConnection() {\n  return new XhrBytesConnection();\n}\nclass XhrBlobConnection extends XhrConnection {\n  initXhr() {\n    this.xhr_.responseType = 'blob';\n  }\n}\nfunction newBlobConnection() {\n  return new XhrBlobConnection();\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nclass UploadTask {\n  isExponentialBackoffExpired() {\n    return this.sleepTime > this.maxSleepTime;\n  }\n  /**\n   * @param ref - The firebaseStorage.Reference object this task came\n   *     from, untyped to avoid cyclic dependencies.\n   * @param blob - The blob to upload.\n   */\n  constructor(ref, blob, metadata = null) {\n    /**\n     * Number of bytes transferred so far.\n     */\n    this._transferred = 0;\n    this._needToFetchStatus = false;\n    this._needToFetchMetadata = false;\n    this._observers = [];\n    this._error = undefined;\n    this._uploadUrl = undefined;\n    this._request = undefined;\n    this._chunkMultiplier = 1;\n    this._resolve = undefined;\n    this._reject = undefined;\n    this._ref = ref;\n    this._blob = blob;\n    this._metadata = metadata;\n    this._mappings = getMappings();\n    this._resumable = this._shouldDoResumable(this._blob);\n    this._state = \"running\" /* InternalTaskState.RUNNING */;\n    this._errorHandler = error => {\n      this._request = undefined;\n      this._chunkMultiplier = 1;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this._needToFetchStatus = true;\n        this.completeTransitions_();\n      } else {\n        const backoffExpired = this.isExponentialBackoffExpired();\n        if (isRetryStatusCode(error.status, [])) {\n          if (backoffExpired) {\n            error = retryLimitExceeded();\n          } else {\n            this.sleepTime = Math.max(this.sleepTime * 2, DEFAULT_MIN_SLEEP_TIME_MILLIS);\n            this._needToFetchStatus = true;\n            this.completeTransitions_();\n            return;\n          }\n        }\n        this._error = error;\n        this._transition(\"error\" /* InternalTaskState.ERROR */);\n      }\n    };\n    this._metadataErrorHandler = error => {\n      this._request = undefined;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this.completeTransitions_();\n      } else {\n        this._error = error;\n        this._transition(\"error\" /* InternalTaskState.ERROR */);\n      }\n    };\n    this.sleepTime = 0;\n    this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n      this._start();\n    });\n    // Prevent uncaught rejections on the internal promise from bubbling out\n    // to the top level with a dummy handler.\n    this._promise.then(null, () => {});\n  }\n  _makeProgressCallback() {\n    const sizeBefore = this._transferred;\n    return loaded => this._updateProgress(sizeBefore + loaded);\n  }\n  _shouldDoResumable(blob) {\n    return blob.size() > 256 * 1024;\n  }\n  _start() {\n    if (this._state !== \"running\" /* InternalTaskState.RUNNING */) {\n      // This can happen if someone pauses us in a resume callback, for example.\n      return;\n    }\n    if (this._request !== undefined) {\n      return;\n    }\n    if (this._resumable) {\n      if (this._uploadUrl === undefined) {\n        this._createResumable();\n      } else {\n        if (this._needToFetchStatus) {\n          this._fetchStatus();\n        } else {\n          if (this._needToFetchMetadata) {\n            // Happens if we miss the metadata on upload completion.\n            this._fetchMetadata();\n          } else {\n            this.pendingTimeout = setTimeout(() => {\n              this.pendingTimeout = undefined;\n              this._continueUpload();\n            }, this.sleepTime);\n          }\n        }\n      }\n    } else {\n      this._oneShotUpload();\n    }\n  }\n  _resolveToken(callback) {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.all([this._ref.storage._getAuthToken(), this._ref.storage._getAppCheckToken()]).then(([authToken, appCheckToken]) => {\n      switch (this._state) {\n        case \"running\" /* InternalTaskState.RUNNING */:\n          callback(authToken, appCheckToken);\n          break;\n        case \"canceling\" /* InternalTaskState.CANCELING */:\n          this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\n          break;\n        case \"pausing\" /* InternalTaskState.PAUSING */:\n          this._transition(\"paused\" /* InternalTaskState.PAUSED */);\n          break;\n      }\n    });\n  }\n  // TODO(andysoto): assert false\n  _createResumable() {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = createResumableUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\n      const createRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n      this._request = createRequest;\n      createRequest.getPromise().then(url => {\n        this._request = undefined;\n        this._uploadUrl = url;\n        this._needToFetchStatus = false;\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n  _fetchStatus() {\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl;\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getResumableUploadStatus(this._ref.storage, this._ref._location, url, this._blob);\n      const statusRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n      this._request = statusRequest;\n      statusRequest.getPromise().then(status => {\n        status = status;\n        this._request = undefined;\n        this._updateProgress(status.current);\n        this._needToFetchStatus = false;\n        if (status.finalized) {\n          this._needToFetchMetadata = true;\n        }\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n  _continueUpload() {\n    const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    const status = new ResumableUploadStatus(this._transferred, this._blob.size());\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl;\n    this._resolveToken((authToken, appCheckToken) => {\n      let requestInfo;\n      try {\n        requestInfo = continueResumableUpload(this._ref._location, this._ref.storage, url, this._blob, chunkSize, this._mappings, status, this._makeProgressCallback());\n      } catch (e) {\n        this._error = e;\n        this._transition(\"error\" /* InternalTaskState.ERROR */);\n        return;\n      }\n      const uploadRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken, /*retry=*/false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n      );\n      this._request = uploadRequest;\n      uploadRequest.getPromise().then(newStatus => {\n        this._increaseMultiplier();\n        this._request = undefined;\n        this._updateProgress(newStatus.current);\n        if (newStatus.finalized) {\n          this._metadata = newStatus.metadata;\n          this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n        } else {\n          this.completeTransitions_();\n        }\n      }, this._errorHandler);\n    });\n  }\n  _increaseMultiplier() {\n    const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    // Max chunk size is 32M.\n    if (currentSize * 2 < 32 * 1024 * 1024) {\n      this._chunkMultiplier *= 2;\n    }\n  }\n  _fetchMetadata() {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getMetadata$2(this._ref.storage, this._ref._location, this._mappings);\n      const metadataRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n      this._request = metadataRequest;\n      metadataRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n      }, this._metadataErrorHandler);\n    });\n  }\n  _oneShotUpload() {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = multipartUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\n      const multipartRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n      this._request = multipartRequest;\n      multipartRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._updateProgress(this._blob.size());\n        this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n      }, this._errorHandler);\n    });\n  }\n  _updateProgress(transferred) {\n    const old = this._transferred;\n    this._transferred = transferred;\n    // A progress update can make the \"transferred\" value smaller (e.g. a\n    // partial upload not completed by server, after which the \"transferred\"\n    // value may reset to the value at the beginning of the request).\n    if (this._transferred !== old) {\n      this._notifyObservers();\n    }\n  }\n  _transition(state) {\n    if (this._state === state) {\n      return;\n    }\n    switch (state) {\n      case \"canceling\" /* InternalTaskState.CANCELING */:\n      case \"pausing\" /* InternalTaskState.PAUSING */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        if (this._request !== undefined) {\n          this._request.cancel();\n        } else if (this.pendingTimeout) {\n          clearTimeout(this.pendingTimeout);\n          this.pendingTimeout = undefined;\n          this.completeTransitions_();\n        }\n        break;\n      case \"running\" /* InternalTaskState.RUNNING */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        const wasPaused = this._state === \"paused\" /* InternalTaskState.PAUSED */;\n        this._state = state;\n        if (wasPaused) {\n          this._notifyObservers();\n          this._start();\n        }\n        break;\n      case \"paused\" /* InternalTaskState.PAUSED */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case \"canceled\" /* InternalTaskState.CANCELED */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._error = canceled();\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case \"error\" /* InternalTaskState.ERROR */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case \"success\" /* InternalTaskState.SUCCESS */:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n    }\n  }\n  completeTransitions_() {\n    switch (this._state) {\n      case \"pausing\" /* InternalTaskState.PAUSING */:\n        this._transition(\"paused\" /* InternalTaskState.PAUSED */);\n        break;\n      case \"canceling\" /* InternalTaskState.CANCELING */:\n        this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\n        break;\n      case \"running\" /* InternalTaskState.RUNNING */:\n        this._start();\n        break;\n    }\n  }\n  /**\n   * A snapshot of the current task state.\n   */\n  get snapshot() {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    return {\n      bytesTransferred: this._transferred,\n      totalBytes: this._blob.size(),\n      state: externalState,\n      metadata: this._metadata,\n      task: this,\n      ref: this._ref\n    };\n  }\n  /**\n   * Adds a callback for an event.\n   * @param type - The type of event to listen for.\n   * @param nextOrObserver -\n   *     The `next` function, which gets called for each item in\n   *     the event stream, or an observer object with some or all of these three\n   *     properties (`next`, `error`, `complete`).\n   * @param error - A function that gets called with a `StorageError`\n   *     if the event stream ends due to an error.\n   * @param completed - A function that gets called if the\n   *     event stream ends normally.\n   * @returns\n   *     If only the event argument is passed, returns a function you can use to\n   *     add callbacks (see the examples above). If more than just the event\n   *     argument is passed, returns a function you can call to unregister the\n   *     callbacks.\n   */\n  on(type, nextOrObserver, error, completed) {\n    // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n    const observer = new Observer(nextOrObserver || undefined, error || undefined, completed || undefined);\n    this._addObserver(observer);\n    return () => {\n      this._removeObserver(observer);\n    };\n  }\n  /**\n   * This object behaves like a Promise, and resolves with its snapshot data\n   * when the upload completes.\n   * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n   * @param onRejected - The rejection callback.\n   */\n  then(onFulfilled, onRejected) {\n    // These casts are needed so that TypeScript can infer the types of the\n    // resulting Promise.\n    return this._promise.then(onFulfilled, onRejected);\n  }\n  /**\n   * Equivalent to calling `then(null, onRejected)`.\n   */\n  catch(onRejected) {\n    return this.then(null, onRejected);\n  }\n  /**\n   * Adds the given observer.\n   */\n  _addObserver(observer) {\n    this._observers.push(observer);\n    this._notifyObserver(observer);\n  }\n  /**\n   * Removes the given observer.\n   */\n  _removeObserver(observer) {\n    const i = this._observers.indexOf(observer);\n    if (i !== -1) {\n      this._observers.splice(i, 1);\n    }\n  }\n  _notifyObservers() {\n    this._finishPromise();\n    const observers = this._observers.slice();\n    observers.forEach(observer => {\n      this._notifyObserver(observer);\n    });\n  }\n  _finishPromise() {\n    if (this._resolve !== undefined) {\n      let triggered = true;\n      switch (taskStateFromInternalTaskState(this._state)) {\n        case TaskState.SUCCESS:\n          async(this._resolve.bind(null, this.snapshot))();\n          break;\n        case TaskState.CANCELED:\n        case TaskState.ERROR:\n          const toCall = this._reject;\n          async(toCall.bind(null, this._error))();\n          break;\n        default:\n          triggered = false;\n          break;\n      }\n      if (triggered) {\n        this._resolve = undefined;\n        this._reject = undefined;\n      }\n    }\n  }\n  _notifyObserver(observer) {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    switch (externalState) {\n      case TaskState.RUNNING:\n      case TaskState.PAUSED:\n        if (observer.next) {\n          async(observer.next.bind(observer, this.snapshot))();\n        }\n        break;\n      case TaskState.SUCCESS:\n        if (observer.complete) {\n          async(observer.complete.bind(observer))();\n        }\n        break;\n      case TaskState.CANCELED:\n      case TaskState.ERROR:\n        if (observer.error) {\n          async(observer.error.bind(observer, this._error))();\n        }\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        if (observer.error) {\n          async(observer.error.bind(observer, this._error))();\n        }\n    }\n  }\n  /**\n   * Resumes a paused task. Has no effect on a currently running or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  resume() {\n    const valid = this._state === \"paused\" /* InternalTaskState.PAUSED */ || this._state === \"pausing\" /* InternalTaskState.PAUSING */;\n    if (valid) {\n      this._transition(\"running\" /* InternalTaskState.RUNNING */);\n    }\n    return valid;\n  }\n  /**\n   * Pauses a currently running task. Has no effect on a paused or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  pause() {\n    const valid = this._state === \"running\" /* InternalTaskState.RUNNING */;\n    if (valid) {\n      this._transition(\"pausing\" /* InternalTaskState.PAUSING */);\n    }\n    return valid;\n  }\n  /**\n   * Cancels a currently running or paused task. Has no effect on a complete or\n   * failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  cancel() {\n    const valid = this._state === \"running\" /* InternalTaskState.RUNNING */ || this._state === \"pausing\" /* InternalTaskState.PAUSING */;\n    if (valid) {\n      this._transition(\"canceling\" /* InternalTaskState.CANCELING */);\n    }\n    return valid;\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nclass Reference {\n  constructor(_service, location) {\n    this._service = _service;\n    if (location instanceof Location) {\n      this._location = location;\n    } else {\n      this._location = Location.makeFromUrl(location, _service.host);\n    }\n  }\n  /**\n   * Returns the URL for the bucket and path this object references,\n   *     in the form gs://<bucket>/<object-path>\n   * @override\n   */\n  toString() {\n    return 'gs://' + this._location.bucket + '/' + this._location.path;\n  }\n  _newRef(service, location) {\n    return new Reference(service, location);\n  }\n  /**\n   * A reference to the root of this object's bucket.\n   */\n  get root() {\n    const location = new Location(this._location.bucket, '');\n    return this._newRef(this._service, location);\n  }\n  /**\n   * The name of the bucket containing this reference's object.\n   */\n  get bucket() {\n    return this._location.bucket;\n  }\n  /**\n   * The full path of this object.\n   */\n  get fullPath() {\n    return this._location.path;\n  }\n  /**\n   * The short name of this object, which is the last component of the full path.\n   * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n   */\n  get name() {\n    return lastComponent(this._location.path);\n  }\n  /**\n   * The `StorageService` instance this `StorageReference` is associated with.\n   */\n  get storage() {\n    return this._service;\n  }\n  /**\n   * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n   * this reference is the root.\n   */\n  get parent() {\n    const newPath = parent(this._location.path);\n    if (newPath === null) {\n      return null;\n    }\n    const location = new Location(this._location.bucket, newPath);\n    return new Reference(this._service, location);\n  }\n  /**\n   * Utility function to throw an error in methods that do not accept a root reference.\n   */\n  _throwIfRoot(name) {\n    if (this._location.path === '') {\n      throw invalidRootOperation(name);\n    }\n  }\n}\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nfunction getBytesInternal(ref, maxDownloadSizeBytes) {\n  ref._throwIfRoot('getBytes');\n  const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\n  return ref.storage.makeRequestWithTokens(requestInfo, newBytesConnection).then(bytes => maxDownloadSizeBytes !== undefined ?\n  // GCS may not honor the Range header for small files\n  bytes.slice(0, maxDownloadSizeBytes) : bytes);\n}\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded blob.\n */\nfunction getBlobInternal(ref, maxDownloadSizeBytes) {\n  ref._throwIfRoot('getBlob');\n  const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\n  return ref.storage.makeRequestWithTokens(requestInfo, newBlobConnection).then(blob => maxDownloadSizeBytes !== undefined ?\n  // GCS may not honor the Range header for small files\n  blob.slice(0, maxDownloadSizeBytes) : blob);\n}\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadBytes$1(ref, data, metadata) {\n  ref._throwIfRoot('uploadBytes');\n  const requestInfo = multipartUpload(ref.storage, ref._location, getMappings(), new FbsBlob(data, true), metadata);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection).then(finalMetadata => {\n    return {\n      metadata: finalMetadata,\n      ref\n    };\n  });\n}\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nfunction uploadBytesResumable$1(ref, data, metadata) {\n  ref._throwIfRoot('uploadBytesResumable');\n  return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadString$1(ref, value, format = StringFormat.RAW, metadata) {\n  ref._throwIfRoot('uploadString');\n  const data = dataFromString(format, value);\n  const metadataClone = Object.assign({}, metadata);\n  if (metadataClone['contentType'] == null && data.contentType != null) {\n    metadataClone['contentType'] = data.contentType;\n  }\n  return uploadBytes$1(ref, data.data, metadataClone);\n}\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nfunction listAll$1(ref) {\n  const accumulator = {\n    prefixes: [],\n    items: []\n  };\n  return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(ref, accumulator, pageToken) {\n  const opt = {\n    // maxResults is 1000 by default.\n    pageToken\n  };\n  const nextPage = await list$1(ref, opt);\n  accumulator.prefixes.push(...nextPage.prefixes);\n  accumulator.items.push(...nextPage.items);\n  if (nextPage.nextPageToken != null) {\n    await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n  }\n}\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nfunction list$1(ref, options) {\n  if (options != null) {\n    if (typeof options.maxResults === 'number') {\n      validateNumber('options.maxResults', /* minValue= */1, /* maxValue= */1000, options.maxResults);\n    }\n  }\n  const op = options || {};\n  const requestInfo = list$2(ref.storage, ref._location, /*delimiter= */'/', op.pageToken, op.maxResults);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nfunction getMetadata$1(ref) {\n  ref._throwIfRoot('getMetadata');\n  const requestInfo = getMetadata$2(ref.storage, ref._location, getMappings());\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nfunction updateMetadata$1(ref, metadata) {\n  ref._throwIfRoot('updateMetadata');\n  const requestInfo = updateMetadata$2(ref.storage, ref._location, metadata, getMappings());\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nfunction getDownloadURL$1(ref) {\n  ref._throwIfRoot('getDownloadURL');\n  const requestInfo = getDownloadUrl(ref.storage, ref._location, getMappings());\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection).then(url => {\n    if (url === null) {\n      throw noDownloadURL();\n    }\n    return url;\n  });\n}\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nfunction deleteObject$1(ref) {\n  ref._throwIfRoot('deleteObject');\n  const requestInfo = deleteObject$2(ref.storage, ref._location);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nfunction _getChild$1(ref, childPath) {\n  const newPath = child(ref._location.path, childPath);\n  const location = new Location(ref._location.bucket, newPath);\n  return new Reference(ref.storage, location);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isUrl(path) {\n  return /^[A-Za-z]+:\\/\\//.test(path);\n}\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service, url) {\n  return new Reference(service, url);\n}\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(ref, path) {\n  if (ref instanceof FirebaseStorageImpl) {\n    const service = ref;\n    if (service._bucket == null) {\n      throw noDefaultBucket();\n    }\n    const reference = new Reference(service, service._bucket);\n    if (path != null) {\n      return refFromPath(reference, path);\n    } else {\n      return reference;\n    }\n  } else {\n    // ref is a Reference\n    if (path !== undefined) {\n      return _getChild$1(ref, path);\n    } else {\n      return ref;\n    }\n  }\n}\nfunction ref$1(serviceOrRef, pathOrUrl) {\n  if (pathOrUrl && isUrl(pathOrUrl)) {\n    if (serviceOrRef instanceof FirebaseStorageImpl) {\n      return refFromURL(serviceOrRef, pathOrUrl);\n    } else {\n      throw invalidArgument('To use ref(service, url), the first argument must be a Storage instance.');\n    }\n  } else {\n    return refFromPath(serviceOrRef, pathOrUrl);\n  }\n}\nfunction extractBucket(host, config) {\n  const bucketString = config === null || config === void 0 ? void 0 : config[CONFIG_STORAGE_BUCKET_KEY];\n  if (bucketString == null) {\n    return null;\n  }\n  return Location.makeFromBucketSpec(bucketString, host);\n}\nfunction connectStorageEmulator$1(storage, host, port, options = {}) {\n  storage.host = `${host}:${port}`;\n  const useSsl = isCloudWorkstation(host);\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(`https://${storage.host}`);\n    updateEmulatorBanner('Storage', true);\n  }\n  storage._isUsingEmulator = true;\n  storage._protocol = useSsl ? 'https' : 'http';\n  const {\n    mockUserToken\n  } = options;\n  if (mockUserToken) {\n    storage._overrideAuthToken = typeof mockUserToken === 'string' ? mockUserToken : createMockUserToken(mockUserToken, storage.app.options.projectId);\n  }\n}\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nclass FirebaseStorageImpl {\n  constructor(\n  /**\n   * FirebaseApp associated with this StorageService instance.\n   */\n  app, _authProvider,\n  /**\n   * @internal\n   */\n  _appCheckProvider,\n  /**\n   * @internal\n   */\n  _url, _firebaseVersion, _isUsingEmulator = false) {\n    this.app = app;\n    this._authProvider = _authProvider;\n    this._appCheckProvider = _appCheckProvider;\n    this._url = _url;\n    this._firebaseVersion = _firebaseVersion;\n    this._isUsingEmulator = _isUsingEmulator;\n    this._bucket = null;\n    /**\n     * This string can be in the formats:\n     * - host\n     * - host:port\n     */\n    this._host = DEFAULT_HOST;\n    this._protocol = 'https';\n    this._appId = null;\n    this._deleted = false;\n    this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n    this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n    this._requests = new Set();\n    if (_url != null) {\n      this._bucket = Location.makeFromBucketSpec(_url, this._host);\n    } else {\n      this._bucket = extractBucket(this._host, this.app.options);\n    }\n  }\n  /**\n   * The host string for this service, in the form of `host` or\n   * `host:port`.\n   */\n  get host() {\n    return this._host;\n  }\n  set host(host) {\n    this._host = host;\n    if (this._url != null) {\n      this._bucket = Location.makeFromBucketSpec(this._url, host);\n    } else {\n      this._bucket = extractBucket(host, this.app.options);\n    }\n  }\n  /**\n   * The maximum time to retry uploads in milliseconds.\n   */\n  get maxUploadRetryTime() {\n    return this._maxUploadRetryTime;\n  }\n  set maxUploadRetryTime(time) {\n    validateNumber('time', /* minValue=*/0, /* maxValue= */Number.POSITIVE_INFINITY, time);\n    this._maxUploadRetryTime = time;\n  }\n  /**\n   * The maximum time to retry operations other than uploads or downloads in\n   * milliseconds.\n   */\n  get maxOperationRetryTime() {\n    return this._maxOperationRetryTime;\n  }\n  set maxOperationRetryTime(time) {\n    validateNumber('time', /* minValue=*/0, /* maxValue= */Number.POSITIVE_INFINITY, time);\n    this._maxOperationRetryTime = time;\n  }\n  async _getAuthToken() {\n    if (this._overrideAuthToken) {\n      return this._overrideAuthToken;\n    }\n    const auth = this._authProvider.getImmediate({\n      optional: true\n    });\n    if (auth) {\n      const tokenData = await auth.getToken();\n      if (tokenData !== null) {\n        return tokenData.accessToken;\n      }\n    }\n    return null;\n  }\n  async _getAppCheckToken() {\n    if (_isFirebaseServerApp(this.app) && this.app.settings.appCheckToken) {\n      return this.app.settings.appCheckToken;\n    }\n    const appCheck = this._appCheckProvider.getImmediate({\n      optional: true\n    });\n    if (appCheck) {\n      const result = await appCheck.getToken();\n      // TODO: What do we want to do if there is an error getting the token?\n      // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n      // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n      // the token (actual or dummy) to send requests.\n      return result.token;\n    }\n    return null;\n  }\n  /**\n   * Stop running requests and prevent more from being created.\n   */\n  _delete() {\n    if (!this._deleted) {\n      this._deleted = true;\n      this._requests.forEach(request => request.cancel());\n      this._requests.clear();\n    }\n    return Promise.resolve();\n  }\n  /**\n   * Returns a new firebaseStorage.Reference object referencing this StorageService\n   * at the given Location.\n   */\n  _makeStorageReference(loc) {\n    return new Reference(this, loc);\n  }\n  /**\n   * @param requestInfo - HTTP RequestInfo object\n   * @param authToken - Firebase auth token\n   */\n  _makeRequest(requestInfo, requestFactory, authToken, appCheckToken, retry = true) {\n    if (!this._deleted) {\n      const request = makeRequest(requestInfo, this._appId, authToken, appCheckToken, requestFactory, this._firebaseVersion, retry, this._isUsingEmulator);\n      this._requests.add(request);\n      // Request removes itself from set when complete.\n      request.getPromise().then(() => this._requests.delete(request), () => this._requests.delete(request));\n      return request;\n    } else {\n      return new FailRequest(appDeleted());\n    }\n  }\n  async makeRequestWithTokens(requestInfo, requestFactory) {\n    const [authToken, appCheckToken] = await Promise.all([this._getAuthToken(), this._getAppCheckToken()]);\n    return this._makeRequest(requestInfo, requestFactory, authToken, appCheckToken).getPromise();\n  }\n}\nconst name = \"@firebase/storage\";\nconst version = \"0.13.12\";\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Type constant for Firebase Storage.\n */\nconst STORAGE_TYPE = 'storage';\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nfunction getBytes(ref, maxDownloadSizeBytes) {\n  ref = getModularInstance(ref);\n  return getBytesInternal(ref, maxDownloadSizeBytes);\n}\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadBytes(ref, data, metadata) {\n  ref = getModularInstance(ref);\n  return uploadBytes$1(ref, data, metadata);\n}\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadString(ref, value, format, metadata) {\n  ref = getModularInstance(ref);\n  return uploadString$1(ref, value, format, metadata);\n}\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nfunction uploadBytesResumable(ref, data, metadata) {\n  ref = getModularInstance(ref);\n  return uploadBytesResumable$1(ref, data, metadata);\n}\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nfunction getMetadata(ref) {\n  ref = getModularInstance(ref);\n  return getMetadata$1(ref);\n}\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nfunction updateMetadata(ref, metadata) {\n  ref = getModularInstance(ref);\n  return updateMetadata$1(ref, metadata);\n}\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nfunction list(ref, options) {\n  ref = getModularInstance(ref);\n  return list$1(ref, options);\n}\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nfunction listAll(ref) {\n  ref = getModularInstance(ref);\n  return listAll$1(ref);\n}\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nfunction getDownloadURL(ref) {\n  ref = getModularInstance(ref);\n  return getDownloadURL$1(ref);\n}\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nfunction deleteObject(ref) {\n  ref = getModularInstance(ref);\n  return deleteObject$1(ref);\n}\nfunction ref(serviceOrRef, pathOrUrl) {\n  serviceOrRef = getModularInstance(serviceOrRef);\n  return ref$1(serviceOrRef, pathOrUrl);\n}\n/**\n * @internal\n */\nfunction _getChild(ref, childPath) {\n  return _getChild$1(ref, childPath);\n}\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nfunction getStorage(app = getApp(), bucketUrl) {\n  app = getModularInstance(app);\n  const storageProvider = _getProvider(app, STORAGE_TYPE);\n  const storageInstance = storageProvider.getImmediate({\n    identifier: bucketUrl\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('storage');\n  if (emulator) {\n    connectStorageEmulator(storageInstance, ...emulator);\n  }\n  return storageInstance;\n}\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nfunction connectStorageEmulator(storage, host, port, options = {}) {\n  connectStorageEmulator$1(storage, host, port, options);\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * This API is not available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise that resolves with a Blob containing the object's bytes\n */\nfunction getBlob(ref, maxDownloadSizeBytes) {\n  ref = getModularInstance(ref);\n  return getBlobInternal(ref, maxDownloadSizeBytes);\n}\n/**\n * Downloads the data at the object's location. Raises an error event if the\n * object is not found.\n *\n * This API is only available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A stream with the object's data as bytes\n */\nfunction getStream(ref, maxDownloadSizeBytes) {\n  throw new Error('getStream() is only supported by NodeJS builds');\n}\n\n/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\nfunction factory(container, {\n  instanceIdentifier: url\n}) {\n  const app = container.getProvider('app').getImmediate();\n  const authProvider = container.getProvider('auth-internal');\n  const appCheckProvider = container.getProvider('app-check-internal');\n  return new FirebaseStorageImpl(app, authProvider, appCheckProvider, url, SDK_VERSION);\n}\nfunction registerStorage() {\n  _registerComponent(new Component(STORAGE_TYPE, factory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\n  //RUNTIME_ENV will be replaced during the compilation to \"node\" for nodejs and an empty string for browser\n  registerVersion(name, version, '');\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, 'esm2017');\n}\nregisterStorage();\nexport { StorageError, StorageErrorCode, StringFormat, FbsBlob as _FbsBlob, Location as _Location, TaskEvent as _TaskEvent, TaskState as _TaskState, UploadTask as _UploadTask, dataFromString as _dataFromString, _getChild, invalidArgument as _invalidArgument, invalidRootOperation as _invalidRootOperation, connectStorageEmulator, deleteObject, getBlob, getBytes, getDownloadURL, getMetadata, getStorage, getStream, list, listAll, ref, updateMetadata, uploadBytes, uploadBytesResumable, uploadString };\n", "import { getDownloadURL as getDownloadURL$1, getMetadata as getMetadata$1, uploadBytesResumable as uploadBytesResumable$1, uploadString as uploadString$1 } from 'firebase/storage';\nimport { Observable, from } from 'rxjs';\nimport { shareReplay, map } from 'rxjs/operators';\nfunction fromTask(task) {\n  return new Observable(function (subscriber) {\n    var lastSnapshot = null;\n    var complete = false;\n    var hasError = false;\n    var error = null;\n    var emit = function (snapshot) {\n      lastSnapshot = snapshot;\n      schedule();\n    };\n    var id = null;\n    /**\n     * Schedules an async event to check and emit\n     * the most recent snapshot, and complete or error\n     * if necessary.\n     */\n    var schedule = function () {\n      if (!id) {\n        id = setTimeout(function () {\n          id = null;\n          if (lastSnapshot) subscriber.next(lastSnapshot);\n          if (complete) subscriber.complete();\n          if (hasError) subscriber.error(error);\n        });\n      }\n    };\n    subscriber.add(function () {\n      // If we have any emissions checks scheduled, cancel them.\n      if (id) clearTimeout(id);\n    });\n    // Emit the initial snapshot\n    emit(task.snapshot);\n    // Take each update and schedule them to be emitted (see `emit`)\n    subscriber.add(task.on('state_changed', emit));\n    // task is a promise, so we can convert that to an observable,\n    // this is done for the ergonomics around making sure we don't\n    // try to push errors or completions through closed subscribers\n    subscriber.add(from(task).subscribe({\n      next: emit,\n      error: function (err) {\n        hasError = true;\n        error = err;\n        schedule();\n      },\n      complete: function () {\n        complete = true;\n        schedule();\n      }\n    }));\n  });\n}\nfunction getDownloadURL(ref) {\n  return from(getDownloadURL$1(ref));\n}\n// TODO: fix storage typing in firebase, then apply the same fix here\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getMetadata(ref) {\n  return from(getMetadata$1(ref));\n}\n// MARK: Breaking change (renaming put to uploadBytesResumable)\nfunction uploadBytesResumable(ref, data, metadata) {\n  return new Observable(function (subscriber) {\n    var task = uploadBytesResumable$1(ref, data, metadata);\n    var subscription = fromTask(task).subscribe(subscriber);\n    return function unsubscribe() {\n      subscription.unsubscribe();\n      task.cancel();\n    };\n  }).pipe(shareReplay({\n    bufferSize: 1,\n    refCount: true\n  }));\n}\n// MARK: Breaking change (renaming put to uploadString)\nfunction uploadString(ref, data, format, metadata) {\n  return from(uploadString$1(ref, data, format, metadata));\n}\nfunction percentage(task) {\n  return fromTask(task).pipe(map(function (snapshot) {\n    return {\n      progress: snapshot.bytesTransferred / snapshot.totalBytes * 100,\n      snapshot: snapshot\n    };\n  }));\n}\nexport { fromTask, getDownloadURL, getMetadata, percentage, uploadBytesResumable, uploadString };\n", "import { ɵgetAllInstancesOf as _getAllInstancesOf, ɵgetDefaultInstanceOf as _getDefaultInstanceOf, VERSION, ɵAngularFireSchedulers as _AngularFireSchedulers, ɵzoneWrap as _zoneWrap } from '@angular/fire';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, NgModule, makeEnvironmentProviders, NgZone, Injector } from '@angular/core';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { AppCheckInstances } from '@angular/fire/app-check';\nimport { AuthInstances } from '@angular/fire/auth';\nimport { registerVersion } from 'firebase/app';\nimport { fromTask as fromTask$1, percentage as percentage$1 } from 'rxfire/storage';\nimport { connectStorageEmulator as connectStorageEmulator$1, deleteObject as deleteObject$1, getBlob as getBlob$1, getBytes as getBytes$1, getDownloadURL as getDownloadURL$1, getMetadata as getMetadata$1, getStorage as getStorage$1, getStream as getStream$1, list as list$1, listAll as listAll$1, ref as ref$1, updateMetadata as updateMetadata$1, uploadBytes as uploadBytes$1, uploadBytesResumable as uploadBytesResumable$1, uploadString as uploadString$1 } from 'firebase/storage';\nexport * from 'firebase/storage';\nclass Storage {\n  constructor(auth) {\n    return auth;\n  }\n}\nconst STORAGE_PROVIDER_NAME = 'storage';\nclass StorageInstances {\n  constructor() {\n    return _getAllInstancesOf(STORAGE_PROVIDER_NAME);\n  }\n}\nconst storageInstance$ = timer(0, 300).pipe(concatMap(() => from(_getAllInstancesOf(STORAGE_PROVIDER_NAME))), distinct());\nconst PROVIDED_STORAGE_INSTANCES = new InjectionToken('angularfire2.storage-instances');\nfunction defaultStorageInstanceFactory(provided, defaultApp) {\n  const defaultStorage = _getDefaultInstanceOf(STORAGE_PROVIDER_NAME, provided, defaultApp);\n  return defaultStorage && new Storage(defaultStorage);\n}\nfunction storageInstanceFactory(fn) {\n  return (zone, injector) => {\n    const storage = zone.runOutsideAngular(() => fn(injector));\n    return new Storage(storage);\n  };\n}\nconst STORAGE_INSTANCES_PROVIDER = {\n  provide: StorageInstances,\n  deps: [[new Optional(), PROVIDED_STORAGE_INSTANCES]]\n};\nconst DEFAULT_STORAGE_INSTANCE_PROVIDER = {\n  provide: Storage,\n  useFactory: defaultStorageInstanceFactory,\n  deps: [[new Optional(), PROVIDED_STORAGE_INSTANCES], FirebaseApp]\n};\nclass StorageModule {\n  constructor() {\n    registerVersion('angularfire', VERSION.full, 'gcs');\n  }\n  static ɵfac = function StorageModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || StorageModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: StorageModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [DEFAULT_STORAGE_INSTANCE_PROVIDER, STORAGE_INSTANCES_PROVIDER]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StorageModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_STORAGE_INSTANCE_PROVIDER, STORAGE_INSTANCES_PROVIDER]\n    }]\n  }], () => [], null);\n})();\nfunction provideStorage(fn, ...deps) {\n  registerVersion('angularfire', VERSION.full, 'gcs');\n  return makeEnvironmentProviders([DEFAULT_STORAGE_INSTANCE_PROVIDER, STORAGE_INSTANCES_PROVIDER, {\n    provide: PROVIDED_STORAGE_INSTANCES,\n    useFactory: storageInstanceFactory(fn),\n    multi: true,\n    deps: [NgZone, Injector, _AngularFireSchedulers, FirebaseApps,\n    // Defensively load Auth first, if provided\n    [new Optional(), AuthInstances], [new Optional(), AppCheckInstances], ...deps]\n  }]);\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst fromTask = _zoneWrap(fromTask$1, true);\nconst percentage = _zoneWrap(percentage$1, true);\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst connectStorageEmulator = _zoneWrap(connectStorageEmulator$1, true);\nconst deleteObject = _zoneWrap(deleteObject$1, true, 2);\nconst getBlob = _zoneWrap(getBlob$1, true);\nconst getBytes = _zoneWrap(getBytes$1, true);\nconst getDownloadURL = _zoneWrap(getDownloadURL$1, true);\nconst getMetadata = _zoneWrap(getMetadata$1, true);\nconst getStorage = _zoneWrap(getStorage$1, true);\nconst getStream = _zoneWrap(getStream$1, true);\nconst list = _zoneWrap(list$1, true);\nconst listAll = _zoneWrap(listAll$1, true);\nconst ref = _zoneWrap(ref$1, true, 2);\nconst updateMetadata = _zoneWrap(updateMetadata$1, true, 2);\nconst uploadBytes = _zoneWrap(uploadBytes$1, true);\nconst uploadBytesResumable = _zoneWrap(uploadBytesResumable$1, true);\nconst uploadString = _zoneWrap(uploadString$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Storage, StorageInstances, StorageModule, connectStorageEmulator, deleteObject, fromTask, getBlob, getBytes, getDownloadURL, getMetadata, getStorage, getStream, list, listAll, percentage, provideStorage, ref, storageInstance$, updateMetadata, uploadBytes, uploadBytesResumable, uploadString };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAM,eAAe;AAIrB,IAAM,4BAA4B;AAMlC,IAAM,mCAAmC,IAAI,KAAK;AAMlD,IAAM,gCAAgC,KAAK,KAAK;AAIhD,IAAM,gCAAgC;AAsBtC,IAAM,eAAN,MAAM,sBAAqB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvC,YAAY,MAAM,SAAS,UAAU,GAAG;AACtC,UAAM,YAAY,IAAI,GAAG,qBAAqB,OAAO,KAAK,YAAY,IAAI,CAAC,GAAG;AAC9E,SAAK,UAAU;AAIf,SAAK,aAAa;AAAA,MAChB,gBAAgB;AAAA,IAClB;AACA,SAAK,eAAe,KAAK;AAGzB,WAAO,eAAe,MAAM,cAAa,SAAS;AAAA,EACpD;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,MAAM;AAChB,WAAO,YAAY,IAAI,MAAM,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,iBAAiB;AACnB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,IAAI,eAAe,gBAAgB;AACjC,SAAK,WAAW,iBAAiB;AACjC,QAAI,KAAK,WAAW,gBAAgB;AAClC,WAAK,UAAU,GAAG,KAAK,YAAY;AAAA,EAAK,KAAK,WAAW,cAAc;AAAA,IACxE,OAAO;AACL,WAAK,UAAU,KAAK;AAAA,IACtB;AAAA,EACF;AACF;AAKA,IAAI;AAAA,CACH,SAAUA,mBAAkB;AAE3B,EAAAA,kBAAiB,SAAS,IAAI;AAC9B,EAAAA,kBAAiB,kBAAkB,IAAI;AACvC,EAAAA,kBAAiB,kBAAkB,IAAI;AACvC,EAAAA,kBAAiB,mBAAmB,IAAI;AACxC,EAAAA,kBAAiB,gBAAgB,IAAI;AACrC,EAAAA,kBAAiB,iBAAiB,IAAI;AACtC,EAAAA,kBAAiB,cAAc,IAAI;AACnC,EAAAA,kBAAiB,kBAAkB,IAAI;AACvC,EAAAA,kBAAiB,sBAAsB,IAAI;AAC3C,EAAAA,kBAAiB,kBAAkB,IAAI;AACvC,EAAAA,kBAAiB,UAAU,IAAI;AAE/B,EAAAA,kBAAiB,oBAAoB,IAAI;AACzC,EAAAA,kBAAiB,aAAa,IAAI;AAClC,EAAAA,kBAAiB,wBAAwB,IAAI;AAC7C,EAAAA,kBAAiB,mBAAmB,IAAI;AACxC,EAAAA,kBAAiB,mBAAmB,IAAI;AACxC,EAAAA,kBAAiB,wBAAwB,IAAI;AAC7C,EAAAA,kBAAiB,iBAAiB,IAAI;AACtC,EAAAA,kBAAiB,kBAAkB,IAAI;AACvC,EAAAA,kBAAiB,wBAAwB,IAAI;AAC7C,EAAAA,kBAAiB,aAAa,IAAI;AAClC,EAAAA,kBAAiB,wBAAwB,IAAI;AAC7C,EAAAA,kBAAiB,gBAAgB,IAAI;AACrC,EAAAA,kBAAiB,gBAAgB,IAAI;AACrC,EAAAA,kBAAiB,yBAAyB,IAAI;AAChD,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,SAAS,YAAY,MAAM;AACzB,SAAO,aAAa;AACtB;AACA,SAAS,UAAU;AACjB,QAAM,UAAU;AAChB,SAAO,IAAI,aAAa,iBAAiB,SAAS,OAAO;AAC3D;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,IAAI,aAAa,iBAAiB,kBAAkB,aAAa,OAAO,mBAAmB;AACpG;AACA,SAAS,cAAc,QAAQ;AAC7B,SAAO,IAAI,aAAa,iBAAiB,gBAAgB,uBAAuB,SAAS,wEAA6E;AACxK;AACA,SAAS,kBAAkB;AACzB,QAAM,UAAU;AAChB,SAAO,IAAI,aAAa,iBAAiB,iBAAiB,OAAO;AACnE;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,aAAa,iBAAiB,kBAAkB,+EAA+E;AAC5I;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,IAAI,aAAa,iBAAiB,cAAc,8CAA8C,OAAO,IAAI;AAClH;AACA,SAAS,qBAAqB;AAC5B,SAAO,IAAI,aAAa,iBAAiB,sBAAsB,0DAA0D;AAC3H;AACA,SAAS,WAAW;AAClB,SAAO,IAAI,aAAa,iBAAiB,UAAU,oCAAoC;AACzF;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,aAAa,iBAAiB,aAAa,kBAAkB,MAAM,IAAI;AACpF;AACA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,IAAI,aAAa,iBAAiB,wBAAwB,6BAA6B,SAAS,IAAI;AAC7G;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,aAAa,iBAAiB,mBAAmB,+CAAoD,4BAA4B,uCAAuC;AACrL;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,aAAa,iBAAiB,mBAAmB,wDAAwD;AACtH;AACA,SAAS,sBAAsB;AAC7B,SAAO,IAAI,aAAa,iBAAiB,wBAAwB,sEAAsE;AACzI;AACA,SAAS,gBAAgB;AACvB,SAAO,IAAI,aAAa,iBAAiB,iBAAiB,iDAAiD;AAC7G;AACA,SAAS,gBAAgB,UAAU;AACjC,SAAO,IAAI,aAAa,iBAAiB,yBAAyB,GAAG,QAAQ,wJAAwJ;AACvO;AAIA,SAAS,gBAAgB,SAAS;AAChC,SAAO,IAAI,aAAa,iBAAiB,kBAAkB,OAAO;AACpE;AACA,SAAS,aAAa;AACpB,SAAO,IAAI,aAAa,iBAAiB,aAAa,+BAA+B;AACvF;AAMA,SAAS,qBAAqBC,OAAM;AAClC,SAAO,IAAI,aAAa,iBAAiB,wBAAwB,oBAAoBA,QAAO,iHAAsH;AACpN;AAKA,SAAS,cAAc,QAAQ,SAAS;AACtC,SAAO,IAAI,aAAa,iBAAiB,gBAAgB,mCAAmC,SAAS,QAAQ,OAAO;AACtH;AAIA,SAAS,cAAc,SAAS;AAC9B,QAAM,IAAI,aAAa,iBAAiB,gBAAgB,qBAAqB,OAAO;AACtF;AAuBA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAAY,QAAQ,MAAM;AACxB,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK,WAAW;AAAA,EAC9B;AAAA,EACA,gBAAgB;AACd,UAAM,SAAS;AACf,WAAO,QAAQ,OAAO,KAAK,MAAM,IAAI,QAAQ,OAAO,KAAK,IAAI;AAAA,EAC/D;AAAA,EACA,sBAAsB;AACpB,UAAM,SAAS;AACf,WAAO,QAAQ,OAAO,KAAK,MAAM,IAAI;AAAA,EACvC;AAAA,EACA,OAAO,mBAAmB,cAAc,MAAM;AAC5C,QAAI;AACJ,QAAI;AACF,uBAAiB,UAAS,YAAY,cAAc,IAAI;AAAA,IAC1D,SAAS,GAAG;AAGV,aAAO,IAAI,UAAS,cAAc,EAAE;AAAA,IACtC;AACA,QAAI,eAAe,SAAS,IAAI;AAC9B,aAAO;AAAA,IACT,OAAO;AACL,YAAM,qBAAqB,YAAY;AAAA,IACzC;AAAA,EACF;AAAA,EACA,OAAO,YAAY,KAAK,MAAM;AAC5B,QAAI,WAAW;AACf,UAAM,eAAe;AACrB,aAAS,SAAS,KAAK;AACrB,UAAI,IAAI,KAAK,OAAO,IAAI,KAAK,SAAS,CAAC,MAAM,KAAK;AAChD,YAAI,QAAQ,IAAI,MAAM,MAAM,GAAG,EAAE;AAAA,MACnC;AAAA,IACF;AACA,UAAM,SAAS;AACf,UAAM,UAAU,IAAI,OAAO,WAAW,eAAe,QAAQ,GAAG;AAChE,UAAM,YAAY;AAAA,MAChB,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AACA,aAAS,WAAW,KAAK;AACvB,UAAI,QAAQ,mBAAmB,IAAI,IAAI;AAAA,IACzC;AACA,UAAMC,WAAU;AAChB,UAAM,sBAAsB,KAAK,QAAQ,QAAQ,KAAK;AACtD,UAAM,sBAAsB;AAC5B,UAAM,wBAAwB,IAAI,OAAO,aAAa,mBAAmB,IAAIA,QAAO,MAAM,YAAY,KAAK,mBAAmB,IAAI,GAAG;AACrI,UAAM,yBAAyB;AAAA,MAC7B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AACA,UAAM,mBAAmB,SAAS,eAAe,wDAAwD;AACzG,UAAM,mBAAmB;AACzB,UAAM,qBAAqB,IAAI,OAAO,aAAa,gBAAgB,IAAI,YAAY,IAAI,gBAAgB,IAAI,GAAG;AAC9G,UAAM,sBAAsB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AACA,UAAM,SAAS,CAAC;AAAA,MACd,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,IACd,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,IACd,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AACD,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,MAAM,KAAK,GAAG;AACrC,UAAI,UAAU;AACZ,cAAM,cAAc,SAAS,MAAM,QAAQ,MAAM;AACjD,YAAI,YAAY,SAAS,MAAM,QAAQ,IAAI;AAC3C,YAAI,CAAC,WAAW;AACd,sBAAY;AAAA,QACd;AACA,mBAAW,IAAI,UAAS,aAAa,SAAS;AAC9C,cAAM,WAAW,QAAQ;AACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY,MAAM;AACpB,YAAM,WAAW,GAAG;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AACF;AAKA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,OAAO;AACjB,SAAK,WAAW,QAAQ,OAAO,KAAK;AAAA,EACtC;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,OAAO,aAAa,OAAO;AAAA,EAAC;AAC9B;AA8BA,SAAS,MAAM,WAEf,mBAAmB,SAAS;AAG1B,MAAI,cAAc;AAIlB,MAAI,iBAAiB;AAErB,MAAI,kBAAkB;AACtB,MAAI,aAAa;AACjB,MAAI,cAAc;AAClB,WAASC,YAAW;AAClB,WAAO,gBAAgB;AAAA,EACzB;AACA,MAAI,oBAAoB;AACxB,WAAS,mBAAmB,MAAM;AAChC,QAAI,CAAC,mBAAmB;AACtB,0BAAoB;AACpB,wBAAkB,MAAM,MAAM,IAAI;AAAA,IACpC;AAAA,EACF;AACA,WAAS,cAAc,QAAQ;AAC7B,qBAAiB,WAAW,MAAM;AAChC,uBAAiB;AACjB,gBAAU,iBAAiBA,UAAS,CAAC;AAAA,IACvC,GAAG,MAAM;AAAA,EACX;AACA,WAAS,qBAAqB;AAC5B,QAAI,iBAAiB;AACnB,mBAAa,eAAe;AAAA,IAC9B;AAAA,EACF;AACA,WAAS,gBAAgB,YAAY,MAAM;AACzC,QAAI,mBAAmB;AACrB,yBAAmB;AACnB;AAAA,IACF;AACA,QAAI,SAAS;AACX,yBAAmB;AACnB,sBAAgB,KAAK,MAAM,SAAS,GAAG,IAAI;AAC3C;AAAA,IACF;AACA,UAAM,WAAWA,UAAS,KAAK;AAC/B,QAAI,UAAU;AACZ,yBAAmB;AACnB,sBAAgB,KAAK,MAAM,SAAS,GAAG,IAAI;AAC3C;AAAA,IACF;AACA,QAAI,cAAc,IAAI;AAEpB,qBAAe;AAAA,IACjB;AACA,QAAI;AACJ,QAAI,gBAAgB,GAAG;AACrB,oBAAc;AACd,mBAAa;AAAA,IACf,OAAO;AACL,oBAAc,cAAc,KAAK,OAAO,KAAK;AAAA,IAC/C;AACA,kBAAc,UAAU;AAAA,EAC1B;AACA,MAAI,UAAU;AACd,WAASC,MAAK,YAAY;AACxB,QAAI,SAAS;AACX;AAAA,IACF;AACA,cAAU;AACV,uBAAmB;AACnB,QAAI,mBAAmB;AACrB;AAAA,IACF;AACA,QAAI,mBAAmB,MAAM;AAC3B,UAAI,CAAC,YAAY;AACf,sBAAc;AAAA,MAChB;AACA,mBAAa,cAAc;AAC3B,oBAAc,CAAC;AAAA,IACjB,OAAO;AACL,UAAI,CAAC,YAAY;AACf,sBAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,gBAAc,CAAC;AACf,oBAAkB,WAAW,MAAM;AACjC,iBAAa;AACb,IAAAA,MAAK,IAAI;AAAA,EACX,GAAG,OAAO;AACV,SAAOA;AACT;AAQA,SAAS,KAAK,IAAI;AAChB,KAAG,KAAK;AACV;AAkBA,SAAS,UAAU,GAAG;AACpB,SAAO,MAAM;AACf;AAEA,SAAS,WAAW,GAAG;AACrB,SAAO,OAAO,MAAM;AACtB;AACA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,OAAO,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC;AAClD;AACA,SAAS,SAAS,GAAG;AACnB,SAAO,OAAO,MAAM,YAAY,aAAa;AAC/C;AACA,SAAS,aAAa,GAAG;AACvB,SAAO,oBAAoB,KAAK,aAAa;AAC/C;AACA,SAAS,sBAAsB;AAC7B,SAAO,OAAO,SAAS;AACzB;AACA,SAAS,eAAe,UAAU,UAAU,UAAU,OAAO;AAC3D,MAAI,QAAQ,UAAU;AACpB,UAAM,gBAAgB,sBAAsB,QAAQ,eAAe,QAAQ,cAAc;AAAA,EAC3F;AACA,MAAI,QAAQ,UAAU;AACpB,UAAM,gBAAgB,sBAAsB,QAAQ,eAAe,QAAQ,WAAW;AAAA,EACxF;AACF;AAkBA,SAAS,QAAQ,SAAS,MAAM,UAAU;AACxC,MAAI,SAAS;AACb,MAAI,YAAY,MAAM;AACpB,aAAS,WAAW,IAAI;AAAA,EAC1B;AACA,SAAO,GAAG,QAAQ,MAAM,MAAM,MAAM,OAAO;AAC7C;AACA,SAAS,gBAAgB,QAAQ;AAC/B,QAAM,SAAS;AACf,MAAI,YAAY;AAChB,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,YAAM,WAAW,OAAO,GAAG,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC;AACvD,kBAAY,YAAY,WAAW;AAAA,IACrC;AAAA,EACF;AAEA,cAAY,UAAU,MAAM,GAAG,EAAE;AACjC,SAAO;AACT;AAKA,IAAI;AAAA,CACH,SAAUC,YAAW;AACpB,EAAAA,WAAUA,WAAU,UAAU,IAAI,CAAC,IAAI;AACvC,EAAAA,WAAUA,WAAU,eAAe,IAAI,CAAC,IAAI;AAC5C,EAAAA,WAAUA,WAAU,OAAO,IAAI,CAAC,IAAI;AACtC,GAAG,cAAc,YAAY,CAAC,EAAE;AAwBhC,SAAS,kBAAkB,QAAQ,sBAAsB;AAGvD,QAAM,oBAAoB,UAAU,OAAO,SAAS;AACpD,QAAM,kBAAkB;AAAA;AAAA,IAExB;AAAA;AAAA,IAEA;AAAA,EAAG;AACH,QAAM,mBAAmB,gBAAgB,QAAQ,MAAM,MAAM;AAC7D,QAAM,wBAAwB,qBAAqB,QAAQ,MAAM,MAAM;AACvE,SAAO,qBAAqB,oBAAoB;AAClD;AA0BA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,MAAM,SAAS,UAAU,OAAO,eAAe,uBAAuB,WAAW,gBAAgB,UAAU,mBAAmB,oBAAoB,QAAQ,MAAM,kBAAkB,OAAO;AACnM,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,OAAO;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,UAAM,eAAe,CAAC,iBAAiBF,cAAa;AAClD,UAAIA,WAAU;AACZ,wBAAgB,OAAO,IAAI,iBAAiB,OAAO,MAAM,IAAI,CAAC;AAC9D;AAAA,MACF;AACA,YAAM,aAAa,KAAK,mBAAmB;AAC3C,WAAK,qBAAqB;AAC1B,YAAM,mBAAmB,mBAAiB;AACxC,cAAM,SAAS,cAAc;AAC7B,cAAM,QAAQ,cAAc,mBAAmB,cAAc,QAAQ;AACrE,YAAI,KAAK,sBAAsB,MAAM;AACnC,eAAK,kBAAkB,QAAQ,KAAK;AAAA,QACtC;AAAA,MACF;AACA,UAAI,KAAK,sBAAsB,MAAM;AACnC,mBAAW,0BAA0B,gBAAgB;AAAA,MACvD;AAGA,iBAAW,KAAK,KAAK,MAAM,KAAK,SAAS,KAAK,iBAAiB,KAAK,OAAO,KAAK,QAAQ,EAAE,KAAK,MAAM;AACnG,YAAI,KAAK,sBAAsB,MAAM;AACnC,qBAAW,6BAA6B,gBAAgB;AAAA,QAC1D;AACA,aAAK,qBAAqB;AAC1B,cAAM,YAAY,WAAW,aAAa,MAAM,UAAU;AAC1D,cAAM,SAAS,WAAW,UAAU;AACpC,YAAI,CAAC,aAAa,kBAAkB,QAAQ,KAAK,qBAAqB,KAAK,KAAK,OAAO;AACrF,gBAAM,cAAc,WAAW,aAAa,MAAM,UAAU;AAC5D,0BAAgB,OAAO,IAAI,iBAAiB,OAAO,MAAM,WAAW,CAAC;AACrE;AAAA,QACF;AACA,cAAM,cAAc,KAAK,cAAc,QAAQ,MAAM,MAAM;AAC3D,wBAAgB,MAAM,IAAI,iBAAiB,aAAa,UAAU,CAAC;AAAA,MACrE,CAAC;AAAA,IACH;AAKA,UAAM,cAAc,CAAC,oBAAoB,WAAW;AAClD,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,KAAK;AACpB,YAAM,aAAa,OAAO;AAC1B,UAAI,OAAO,gBAAgB;AACzB,YAAI;AACF,gBAAM,SAAS,KAAK,UAAU,YAAY,WAAW,YAAY,CAAC;AAClE,cAAI,UAAU,MAAM,GAAG;AACrB,oBAAQ,MAAM;AAAA,UAChB,OAAO;AACL,oBAAQ;AAAA,UACV;AAAA,QACF,SAAS,GAAG;AACV,iBAAO,CAAC;AAAA,QACV;AAAA,MACF,OAAO;AACL,YAAI,eAAe,MAAM;AACvB,gBAAM,MAAM,QAAQ;AACpB,cAAI,iBAAiB,WAAW,aAAa;AAC7C,cAAI,KAAK,gBAAgB;AACvB,mBAAO,KAAK,eAAe,YAAY,GAAG,CAAC;AAAA,UAC7C,OAAO;AACL,mBAAO,GAAG;AAAA,UACZ;AAAA,QACF,OAAO;AACL,cAAI,OAAO,UAAU;AACnB,kBAAM,MAAM,KAAK,aAAa,WAAW,IAAI,SAAS;AACtD,mBAAO,GAAG;AAAA,UACZ,OAAO;AACL,kBAAM,MAAM,mBAAmB;AAC/B,mBAAO,GAAG;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,WAAW;AAClB,kBAAY,OAAO,IAAI,iBAAiB,OAAO,MAAM,IAAI,CAAC;AAAA,IAC5D,OAAO;AACL,WAAK,aAAa,MAAM,cAAc,aAAa,KAAK,QAAQ;AAAA,IAClE;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,OAAO,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,aAAa,aAAa;AAC/B,QAAI,KAAK,eAAe,MAAM;AAC5B,WAAK,KAAK,UAAU;AAAA,IACtB;AACA,QAAI,KAAK,uBAAuB,MAAM;AACpC,WAAK,mBAAmB,MAAM;AAAA,IAChC;AAAA,EACF;AACF;AAKA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,gBAAgB,YAAYA,WAAU;AAChD,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,WAAW,CAAC,CAACA;AAAA,EACpB;AACF;AACA,SAAS,eAAe,SAAS,WAAW;AAC1C,MAAI,cAAc,QAAQ,UAAU,SAAS,GAAG;AAC9C,YAAQ,eAAe,IAAI,cAAc;AAAA,EAC3C;AACF;AACA,SAAS,kBAAkB,SAAS,iBAAiB;AACnD,UAAQ,4BAA4B,IAAI,YAAY,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB;AACjI;AACA,SAAS,gBAAgB,SAAS,OAAO;AACvC,MAAI,OAAO;AACT,YAAQ,kBAAkB,IAAI;AAAA,EAChC;AACF;AACA,SAAS,mBAAmB,SAAS,eAAe;AAClD,MAAI,kBAAkB,MAAM;AAC1B,YAAQ,qBAAqB,IAAI;AAAA,EACnC;AACF;AACA,SAAS,YAAY,aAAa,OAAO,WAAW,eAAe,gBAAgB,iBAAiB,QAAQ,MAAM,kBAAkB,OAAO;AACzI,QAAM,YAAY,gBAAgB,YAAY,SAAS;AACvD,QAAM,MAAM,YAAY,MAAM;AAC9B,QAAM,UAAU,OAAO,OAAO,CAAC,GAAG,YAAY,OAAO;AACrD,kBAAgB,SAAS,KAAK;AAC9B,iBAAe,SAAS,SAAS;AACjC,oBAAkB,SAAS,eAAe;AAC1C,qBAAmB,SAAS,aAAa;AACzC,SAAO,IAAI,eAAe,KAAK,YAAY,QAAQ,SAAS,YAAY,MAAM,YAAY,cAAc,YAAY,sBAAsB,YAAY,SAAS,YAAY,cAAc,YAAY,SAAS,YAAY,kBAAkB,gBAAgB,OAAO,eAAe;AACpR;AAkBA,SAAS,iBAAiB;AACxB,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO;AAAA,EACT,WAAW,OAAO,sBAAsB,aAAa;AACnD,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAOA,SAAS,aAAa,MAAM;AAC1B,QAAMG,eAAc,eAAe;AACnC,MAAIA,iBAAgB,QAAW;AAC7B,UAAM,KAAK,IAAIA,aAAY;AAC3B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,SAAG,OAAO,KAAK,CAAC,CAAC;AAAA,IACnB;AACA,WAAO,GAAG,QAAQ;AAAA,EACpB,OAAO;AACL,QAAI,oBAAoB,GAAG;AACzB,aAAO,IAAI,KAAK,IAAI;AAAA,IACtB,OAAO;AACL,YAAM,IAAI,aAAa,iBAAiB,yBAAyB,qDAAqD;AAAA,IACxH;AAAA,EACF;AACF;AAUA,SAAS,UAAU,MAAMC,QAAO,KAAK;AACnC,MAAI,KAAK,aAAa;AACpB,WAAO,KAAK,YAAYA,QAAO,GAAG;AAAA,EACpC,WAAW,KAAK,UAAU;AACxB,WAAO,KAAK,SAASA,QAAO,GAAG;AAAA,EACjC,WAAW,KAAK,OAAO;AACrB,WAAO,KAAK,MAAMA,QAAO,GAAG;AAAA,EAC9B;AACA,SAAO;AACT;AAmBA,SAAS,aAAa,SAAS;AAC7B,MAAI,OAAO,SAAS,aAAa;AAC/B,UAAM,gBAAgB,SAAS;AAAA,EACjC;AACA,SAAO,KAAK,OAAO;AACrB;AAsBA,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOL,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUX,UAAU;AACZ;AACA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,MAAM,aAAa;AAC7B,SAAK,OAAO;AACZ,SAAK,cAAc,eAAe;AAAA,EACpC;AACF;AAIA,SAAS,eAAe,QAAQ,YAAY;AAC1C,UAAQ,QAAQ;AAAA,IACd,KAAK,aAAa;AAChB,aAAO,IAAI,WAAW,WAAW,UAAU,CAAC;AAAA,IAC9C,KAAK,aAAa;AAAA,IAClB,KAAK,aAAa;AAChB,aAAO,IAAI,WAAW,aAAa,QAAQ,UAAU,CAAC;AAAA,IACxD,KAAK,aAAa;AAChB,aAAO,IAAI,WAAW,cAAc,UAAU,GAAG,oBAAoB,UAAU,CAAC;AAAA,EAEpF;AAEA,QAAM,QAAQ;AAChB;AACA,SAAS,WAAW,OAAO;AACzB,QAAM,IAAI,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,IAAI,MAAM,WAAW,CAAC;AAC1B,QAAI,KAAK,KAAK;AACZ,QAAE,KAAK,CAAC;AAAA,IACV,OAAO;AACL,UAAI,KAAK,MAAM;AACb,UAAE,KAAK,MAAM,KAAK,GAAG,MAAM,IAAI,EAAE;AAAA,MACnC,OAAO;AACL,aAAK,IAAI,WAAW,OAAO;AAEzB,gBAAM,QAAQ,IAAI,MAAM,SAAS,MAAM,MAAM,WAAW,IAAI,CAAC,IAAI,WAAW;AAC5E,cAAI,CAAC,OAAO;AAEV,cAAE,KAAK,KAAK,KAAK,GAAG;AAAA,UACtB,OAAO;AACL,kBAAM,KAAK;AACX,kBAAM,KAAK,MAAM,WAAW,EAAE,CAAC;AAC/B,gBAAI,SAAS,KAAK,SAAS,KAAK,KAAK;AACrC,cAAE,KAAK,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,IAAI,EAAE;AAAA,UAC3E;AAAA,QACF,OAAO;AACL,eAAK,IAAI,WAAW,OAAO;AAEzB,cAAE,KAAK,KAAK,KAAK,GAAG;AAAA,UACtB,OAAO;AACL,cAAE,KAAK,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,IAAI,EAAE;AAAA,UACvD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,WAAW,CAAC;AACzB;AACA,SAAS,qBAAqB,OAAO;AACnC,MAAI;AACJ,MAAI;AACF,cAAU,mBAAmB,KAAK;AAAA,EACpC,SAAS,GAAG;AACV,UAAM,cAAc,aAAa,UAAU,qBAAqB;AAAA,EAClE;AACA,SAAO,WAAW,OAAO;AAC3B;AACA,SAAS,aAAa,QAAQ,OAAO;AACnC,UAAQ,QAAQ;AAAA,IACd,KAAK,aAAa,QAChB;AACE,YAAM,WAAW,MAAM,QAAQ,GAAG,MAAM;AACxC,YAAM,WAAW,MAAM,QAAQ,GAAG,MAAM;AACxC,UAAI,YAAY,UAAU;AACxB,cAAM,cAAc,WAAW,MAAM;AACrC,cAAM,cAAc,QAAQ,wBAAwB,cAAc,mCAAmC;AAAA,MACvG;AACA;AAAA,IACF;AAAA,IACF,KAAK,aAAa,WAChB;AACE,YAAM,UAAU,MAAM,QAAQ,GAAG,MAAM;AACvC,YAAM,WAAW,MAAM,QAAQ,GAAG,MAAM;AACxC,UAAI,WAAW,UAAU;AACvB,cAAM,cAAc,UAAU,MAAM;AACpC,cAAM,cAAc,QAAQ,wBAAwB,cAAc,gCAAgC;AAAA,MACpG;AACA,cAAQ,MAAM,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAClD;AAAA,IACF;AAAA,EAEJ;AACA,MAAI;AACJ,MAAI;AACF,YAAQ,aAAa,KAAK;AAAA,EAC5B,SAAS,GAAG;AACV,QAAI,EAAE,QAAQ,SAAS,UAAU,GAAG;AAClC,YAAM;AAAA,IACR;AACA,UAAM,cAAc,QAAQ,yBAAyB;AAAA,EACvD;AACA,QAAM,QAAQ,IAAI,WAAW,MAAM,MAAM;AACzC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,CAAC,IAAI,MAAM,WAAW,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,SAAS;AACnB,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,UAAM,UAAU,QAAQ,MAAM,iBAAiB;AAC/C,QAAI,YAAY,MAAM;AACpB,YAAM,cAAc,aAAa,UAAU,uDAAuD;AAAA,IACpG;AACA,UAAM,SAAS,QAAQ,CAAC,KAAK;AAC7B,QAAI,UAAU,MAAM;AAClB,WAAK,SAAS,SAAS,QAAQ,SAAS;AACxC,WAAK,cAAc,KAAK,SAAS,OAAO,UAAU,GAAG,OAAO,SAAS,UAAU,MAAM,IAAI;AAAA,IAC3F;AACA,SAAK,OAAO,QAAQ,UAAU,QAAQ,QAAQ,GAAG,IAAI,CAAC;AAAA,EACxD;AACF;AACA,SAAS,cAAc,SAAS;AAC9B,QAAM,QAAQ,IAAI,aAAa,OAAO;AACtC,MAAI,MAAM,QAAQ;AAChB,WAAO,aAAa,aAAa,QAAQ,MAAM,IAAI;AAAA,EACrD,OAAO;AACL,WAAO,qBAAqB,MAAM,IAAI;AAAA,EACxC;AACF;AACA,SAAS,oBAAoB,SAAS;AACpC,QAAM,QAAQ,IAAI,aAAa,OAAO;AACtC,SAAO,MAAM;AACf;AACA,SAAS,SAAS,GAAG,KAAK;AACxB,QAAM,aAAa,EAAE,UAAU,IAAI;AACnC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,SAAO,EAAE,UAAU,EAAE,SAAS,IAAI,MAAM,MAAM;AAChD;AAyBA,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,YAAY,MAAM,WAAW;AAC3B,QAAI,OAAO;AACX,QAAI,WAAW;AACf,QAAI,aAAa,IAAI,GAAG;AACtB,WAAK,QAAQ;AACb,aAAO,KAAK;AACZ,iBAAW,KAAK;AAAA,IAClB,WAAW,gBAAgB,aAAa;AACtC,UAAI,WAAW;AACb,aAAK,QAAQ,IAAI,WAAW,IAAI;AAAA,MAClC,OAAO;AACL,aAAK,QAAQ,IAAI,WAAW,KAAK,UAAU;AAC3C,aAAK,MAAM,IAAI,IAAI,WAAW,IAAI,CAAC;AAAA,MACrC;AACA,aAAO,KAAK,MAAM;AAAA,IACpB,WAAW,gBAAgB,YAAY;AACrC,UAAI,WAAW;AACb,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ,IAAI,WAAW,KAAK,MAAM;AACvC,aAAK,MAAM,IAAI,IAAI;AAAA,MACrB;AACA,aAAO,KAAK;AAAA,IACd;AACA,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAAA,EACA,MAAM,WAAW,SAAS;AACxB,QAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,YAAM,WAAW,KAAK;AACtB,YAAM,SAAS,UAAU,UAAU,WAAW,OAAO;AACrD,UAAI,WAAW,MAAM;AACnB,eAAO;AAAA,MACT;AACA,aAAO,IAAI,SAAQ,MAAM;AAAA,IAC3B,OAAO;AACL,YAAM,QAAQ,IAAI,WAAW,KAAK,MAAM,QAAQ,WAAW,UAAU,SAAS;AAC9E,aAAO,IAAI,SAAQ,OAAO,IAAI;AAAA,IAChC;AAAA,EACF;AAAA,EACA,OAAO,WAAW,MAAM;AACtB,QAAI,oBAAoB,GAAG;AACzB,YAAM,SAAS,KAAK,IAAI,SAAO;AAC7B,YAAI,eAAe,UAAS;AAC1B,iBAAO,IAAI;AAAA,QACb,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,aAAO,IAAI,SAAQ,UAAU,MAAM,MAAM,MAAM,CAAC;AAAA,IAClD,OAAO;AACL,YAAM,cAAc,KAAK,IAAI,SAAO;AAClC,YAAI,SAAS,GAAG,GAAG;AACjB,iBAAO,eAAe,aAAa,KAAK,GAAG,EAAE;AAAA,QAC/C,OAAO;AAEL,iBAAO,IAAI;AAAA,QACb;AAAA,MACF,CAAC;AACD,UAAI,cAAc;AAClB,kBAAY,QAAQ,WAAS;AAC3B,uBAAe,MAAM;AAAA,MACvB,CAAC;AACD,YAAM,SAAS,IAAI,WAAW,WAAW;AACzC,UAAI,QAAQ;AACZ,kBAAY,QAAQ,WAAS;AAC3B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,iBAAO,OAAO,IAAI,MAAM,CAAC;AAAA,QAC3B;AAAA,MACF,CAAC;AACD,aAAO,IAAI,SAAQ,QAAQ,IAAI;AAAA,IACjC;AAAA,EACF;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AACF;AAsBA,SAAS,iBAAiB,GAAG;AAC3B,MAAI;AACJ,MAAI;AACF,UAAM,KAAK,MAAM,CAAC;AAAA,EACpB,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,GAAG,GAAG;AACzB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAwBA,SAAS,OAAO,MAAM;AACpB,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,MAAI,UAAU,IAAI;AAChB,WAAO;AAAA,EACT;AACA,QAAM,UAAU,KAAK,MAAM,GAAG,KAAK;AACnC,SAAO;AACT;AACA,SAAS,MAAM,MAAM,WAAW;AAC9B,QAAM,qBAAqB,UAAU,MAAM,GAAG,EAAE,OAAO,eAAa,UAAU,SAAS,CAAC,EAAE,KAAK,GAAG;AAClG,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO,MAAM;AAAA,EACtB;AACF;AAOA,SAAS,cAAc,MAAM;AAC3B,QAAM,QAAQ,KAAK,YAAY,KAAK,KAAK,SAAS,CAAC;AACnD,MAAI,UAAU,IAAI;AAChB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,KAAK,MAAM,QAAQ,CAAC;AAAA,EAC7B;AACF;AAkBA,SAAS,SAAS,UAAU,OAAO;AACjC,SAAO;AACT;AACA,IAAM,UAAN,MAAc;AAAA,EACZ,YAAY,QAAQ,OAAO,UAAU,OAAO;AAC1C,SAAK,SAAS;AACd,SAAK,QAAQ,SAAS;AACtB,SAAK,WAAW,CAAC,CAAC;AAClB,SAAK,QAAQ,SAAS;AAAA,EACxB;AACF;AACA,IAAI,YAAY;AAChB,SAAS,UAAU,UAAU;AAC3B,MAAI,CAAC,SAAS,QAAQ,KAAK,SAAS,SAAS,GAAG;AAC9C,WAAO;AAAA,EACT,OAAO;AACL,WAAO,cAAc,QAAQ;AAAA,EAC/B;AACF;AACA,SAAS,cAAc;AACrB,MAAI,WAAW;AACb,WAAO;AAAA,EACT;AACA,QAAM,WAAW,CAAC;AAClB,WAAS,KAAK,IAAI,QAAQ,QAAQ,CAAC;AACnC,WAAS,KAAK,IAAI,QAAQ,YAAY,CAAC;AACvC,WAAS,KAAK,IAAI,QAAQ,gBAAgB,CAAC;AAC3C,WAAS,KAAK,IAAI,QAAQ,QAAQ,YAAY,IAAI,CAAC;AACnD,WAAS,kBAAkB,WAAW,UAAU;AAC9C,WAAO,UAAU,QAAQ;AAAA,EAC3B;AACA,QAAM,cAAc,IAAI,QAAQ,MAAM;AACtC,cAAY,QAAQ;AACpB,WAAS,KAAK,WAAW;AAIzB,WAAS,UAAU,WAAW,MAAM;AAClC,QAAI,SAAS,QAAW;AACtB,aAAO,OAAO,IAAI;AAAA,IACpB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,cAAc,IAAI,QAAQ,MAAM;AACtC,cAAY,QAAQ;AACpB,WAAS,KAAK,WAAW;AACzB,WAAS,KAAK,IAAI,QAAQ,aAAa,CAAC;AACxC,WAAS,KAAK,IAAI,QAAQ,SAAS,CAAC;AACpC,WAAS,KAAK,IAAI,QAAQ,WAAW,MAAM,IAAI,CAAC;AAChD,WAAS,KAAK,IAAI,QAAQ,gBAAgB,MAAM,IAAI,CAAC;AACrD,WAAS,KAAK,IAAI,QAAQ,sBAAsB,MAAM,IAAI,CAAC;AAC3D,WAAS,KAAK,IAAI,QAAQ,mBAAmB,MAAM,IAAI,CAAC;AACxD,WAAS,KAAK,IAAI,QAAQ,mBAAmB,MAAM,IAAI,CAAC;AACxD,WAAS,KAAK,IAAI,QAAQ,eAAe,MAAM,IAAI,CAAC;AACpD,WAAS,KAAK,IAAI,QAAQ,YAAY,kBAAkB,IAAI,CAAC;AAC7D,cAAY;AACZ,SAAO;AACT;AACA,SAAS,OAAO,UAAU,SAAS;AACjC,WAAS,cAAc;AACrB,UAAM,SAAS,SAAS,QAAQ;AAChC,UAAM,OAAO,SAAS,UAAU;AAChC,UAAM,MAAM,IAAI,SAAS,QAAQ,IAAI;AACrC,WAAO,QAAQ,sBAAsB,GAAG;AAAA,EAC1C;AACA,SAAO,eAAe,UAAU,OAAO;AAAA,IACrC,KAAK;AAAA,EACP,CAAC;AACH;AACA,SAAS,aAAa,SAAS,UAAU,UAAU;AACjD,QAAM,WAAW,CAAC;AAClB,WAAS,MAAM,IAAI;AACnB,QAAM,MAAM,SAAS;AACrB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,UAAU,SAAS,CAAC;AAC1B,aAAS,QAAQ,KAAK,IAAI,QAAQ,MAAM,UAAU,SAAS,QAAQ,MAAM,CAAC;AAAA,EAC5E;AACA,SAAO,UAAU,OAAO;AACxB,SAAO;AACT;AACA,SAAS,mBAAmB,SAAS,gBAAgB,UAAU;AAC7D,QAAM,MAAM,iBAAiB,cAAc;AAC3C,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AACA,QAAM,WAAW;AACjB,SAAO,aAAa,SAAS,UAAU,QAAQ;AACjD;AACA,SAAS,8BAA8B,UAAU,gBAAgB,MAAM,UAAU;AAC/E,QAAM,MAAM,iBAAiB,cAAc;AAC3C,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,SAAS,IAAI,gBAAgB,CAAC,GAAG;AAGpC,WAAO;AAAA,EACT;AACA,QAAM,SAAS,IAAI,gBAAgB;AACnC,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AACf,QAAM,aAAa,OAAO,MAAM,GAAG;AACnC,QAAM,OAAO,WAAW,IAAI,WAAS;AACnC,UAAM,SAAS,SAAS,QAAQ;AAChC,UAAM,OAAO,SAAS,UAAU;AAChC,UAAM,UAAU,QAAQ,OAAO,MAAM,IAAI,QAAQ,OAAO,IAAI;AAC5D,UAAM,OAAO,QAAQ,SAAS,MAAM,QAAQ;AAC5C,UAAM,cAAc,gBAAgB;AAAA,MAClC,KAAK;AAAA,MACL;AAAA,IACF,CAAC;AACD,WAAO,OAAO;AAAA,EAChB,CAAC;AACD,SAAO,KAAK,CAAC;AACf;AACA,SAAS,iBAAiB,UAAU,UAAU;AAC5C,QAAM,WAAW,CAAC;AAClB,QAAM,MAAM,SAAS;AACrB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,UAAU,SAAS,CAAC;AAC1B,QAAI,QAAQ,UAAU;AACpB,eAAS,QAAQ,MAAM,IAAI,SAAS,QAAQ,KAAK;AAAA,IACnD;AAAA,EACF;AACA,SAAO,KAAK,UAAU,QAAQ;AAChC;AAkBA,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,SAAS,oBAAoB,SAAS,QAAQ,UAAU;AACtD,QAAM,aAAa;AAAA,IACjB,UAAU,CAAC;AAAA,IACX,OAAO,CAAC;AAAA,IACR,eAAe,SAAS,eAAe;AAAA,EACzC;AACA,MAAI,SAAS,YAAY,GAAG;AAC1B,eAAW,QAAQ,SAAS,YAAY,GAAG;AACzC,YAAM,2BAA2B,KAAK,QAAQ,OAAO,EAAE;AACvD,YAAM,YAAY,QAAQ,sBAAsB,IAAI,SAAS,QAAQ,wBAAwB,CAAC;AAC9F,iBAAW,SAAS,KAAK,SAAS;AAAA,IACpC;AAAA,EACF;AACA,MAAI,SAAS,SAAS,GAAG;AACvB,eAAW,QAAQ,SAAS,SAAS,GAAG;AACtC,YAAM,YAAY,QAAQ,sBAAsB,IAAI,SAAS,QAAQ,KAAK,MAAM,CAAC,CAAC;AAClF,iBAAW,MAAM,KAAK,SAAS;AAAA,IACjC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,SAAS,QAAQ,gBAAgB;AAC3D,QAAM,MAAM,iBAAiB,cAAc;AAC3C,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AACA,QAAM,WAAW;AACjB,SAAO,oBAAoB,SAAS,QAAQ,QAAQ;AACtD;AAQA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,KAAK,QAQjB,SAAS,SAAS;AAChB,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU,CAAC;AAChB,SAAK,OAAO;AACZ,SAAK,eAAe;AAKpB,SAAK,mBAAmB;AACxB,SAAK,eAAe,CAAC,GAAG;AACxB,SAAK,uBAAuB,CAAC;AAAA,EAC/B;AACF;AAqBA,SAAS,aAAa,MAAM;AAC1B,MAAI,CAAC,MAAM;AACT,UAAM,QAAQ;AAAA,EAChB;AACF;AACA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,WAAS,QAAQ,KAAK,MAAM;AAC1B,UAAM,WAAW,mBAAmB,SAAS,MAAM,QAAQ;AAC3D,iBAAa,aAAa,IAAI;AAC9B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,YAAY,SAAS,QAAQ;AACpC,WAAS,QAAQ,KAAK,MAAM;AAC1B,UAAM,aAAa,mBAAmB,SAAS,QAAQ,IAAI;AAC3D,iBAAa,eAAe,IAAI;AAChC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,SAAS,UAAU;AAC7C,WAAS,QAAQ,KAAK,MAAM;AAC1B,UAAM,WAAW,mBAAmB,SAAS,MAAM,QAAQ;AAC3D,iBAAa,aAAa,IAAI;AAC9B,WAAO,8BAA8B,UAAU,MAAM,QAAQ,MAAM,QAAQ,SAAS;AAAA,EACtF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,UAAU;AACpC,WAAS,aAAa,KAAK,KAAK;AAC9B,QAAI;AACJ,QAAI,IAAI,UAAU,MAAM,KAAK;AAC3B;AAAA;AAAA;AAAA,QAGA,IAAI,aAAa,EAAE,SAAS,qCAAqC;AAAA,QAAG;AAClE,iBAAS,gBAAgB;AAAA,MAC3B,OAAO;AACL,iBAAS,gBAAgB;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,UAAI,IAAI,UAAU,MAAM,KAAK;AAC3B,iBAAS,cAAc,SAAS,MAAM;AAAA,MACxC,OAAO;AACL,YAAI,IAAI,UAAU,MAAM,KAAK;AAC3B,mBAAS,aAAa,SAAS,IAAI;AAAA,QACrC,OAAO;AACL,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS,IAAI,UAAU;AAC9B,WAAO,iBAAiB,IAAI;AAC5B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,UAAU;AACpC,QAAM,SAAS,mBAAmB,QAAQ;AAC1C,WAAS,aAAa,KAAK,KAAK;AAC9B,QAAI,SAAS,OAAO,KAAK,GAAG;AAC5B,QAAI,IAAI,UAAU,MAAM,KAAK;AAC3B,eAAS,eAAe,SAAS,IAAI;AAAA,IACvC;AACA,WAAO,iBAAiB,IAAI;AAC5B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,cAAc,SAAS,UAAU,UAAU;AAClD,QAAM,UAAU,SAAS,cAAc;AACvC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,gBAAgB,SAAS,QAAQ,GAAG,OAAO;AAC5F,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AACA,SAAS,OAAO,SAAS,UAAU,WAAW,WAAW,YAAY;AACnE,QAAM,YAAY,CAAC;AACnB,MAAI,SAAS,QAAQ;AACnB,cAAU,QAAQ,IAAI;AAAA,EACxB,OAAO;AACL,cAAU,QAAQ,IAAI,SAAS,OAAO;AAAA,EACxC;AACA,MAAI,aAAa,UAAU,SAAS,GAAG;AACrC,cAAU,WAAW,IAAI;AAAA,EAC3B;AACA,MAAI,WAAW;AACb,cAAU,WAAW,IAAI;AAAA,EAC3B;AACA,MAAI,YAAY;AACd,cAAU,YAAY,IAAI;AAAA,EAC5B;AACA,QAAM,UAAU,SAAS,oBAAoB;AAC7C,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,YAAY,SAAS,SAAS,MAAM,GAAG,OAAO;AAC/F,cAAY,YAAY;AACxB,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AACA,SAAS,WAAW,SAAS,UAAU,sBAAsB;AAC3D,QAAM,UAAU,SAAS,cAAc;AACvC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI;AAChE,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,CAAC,GAAG,SAAS,MAAM,OAAO;AAC3E,cAAY,eAAe,mBAAmB,QAAQ;AACtD,MAAI,yBAAyB,QAAW;AACtC,gBAAY,QAAQ,OAAO,IAAI,WAAW,oBAAoB;AAC9D,gBAAY,eAAe;AAAA,MAAC;AAAA,MAAc;AAAA;AAAA,IAAyB;AAAA,EACrE;AACA,SAAO;AACT;AACA,SAAS,eAAe,SAAS,UAAU,UAAU;AACnD,QAAM,UAAU,SAAS,cAAc;AACvC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,mBAAmB,SAAS,QAAQ,GAAG,OAAO;AAC/F,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AACA,SAAS,iBAAiB,SAAS,UAAU,UAAU,UAAU;AAC/D,QAAM,UAAU,SAAS,cAAc;AACvC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,OAAO,iBAAiB,UAAU,QAAQ;AAChD,QAAM,UAAU;AAAA,IACd,gBAAgB;AAAA,EAClB;AACA,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,gBAAgB,SAAS,QAAQ,GAAG,OAAO;AAC5F,cAAY,UAAU;AACtB,cAAY,OAAO;AACnB,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AACA,SAAS,eAAe,SAAS,UAAU;AACzC,QAAM,UAAU,SAAS,cAAc;AACvC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,WAAS,QAAQ,MAAM,OAAO;AAAA,EAAC;AAC/B,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,SAAS,OAAO;AACjE,cAAY,eAAe,CAAC,KAAK,GAAG;AACpC,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AACA,SAAS,sBAAsB,UAAU,MAAM;AAC7C,SAAO,YAAY,SAAS,aAAa,KAAK,QAAQ,KAAK,KAAK,KAAK;AACvE;AACA,SAAS,mBAAmB,UAAU,MAAM,UAAU;AACpD,QAAM,gBAAgB,OAAO,OAAO,CAAC,GAAG,QAAQ;AAChD,gBAAc,UAAU,IAAI,SAAS;AACrC,gBAAc,MAAM,IAAI,KAAK,KAAK;AAClC,MAAI,CAAC,cAAc,aAAa,GAAG;AACjC,kBAAc,aAAa,IAAI,sBAAsB,MAAM,IAAI;AAAA,EACjE;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU,UAAU,MAAM,UAAU;AACpE,QAAM,UAAU,SAAS,oBAAoB;AAC7C,QAAM,UAAU;AAAA,IACd,0BAA0B;AAAA,EAC5B;AACA,WAAS,cAAc;AACrB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,MAAM,KAAK,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AACA,QAAM,WAAW,YAAY;AAC7B,UAAQ,cAAc,IAAI,iCAAiC;AAC3D,QAAM,YAAY,mBAAmB,UAAU,MAAM,QAAQ;AAC7D,QAAM,iBAAiB,iBAAiB,WAAW,QAAQ;AAC3D,QAAM,cAAc,OAAO,WAAW,8DAAmE,iBAAiB,WAAW,WAAW,uBAA4B,UAAU,aAAa,IAAI;AACvM,QAAM,eAAe,WAAW,WAAW;AAC3C,QAAM,OAAO,QAAQ,QAAQ,aAAa,MAAM,YAAY;AAC5D,MAAI,SAAS,MAAM;AACjB,UAAM,gBAAgB;AAAA,EACxB;AACA,QAAM,YAAY;AAAA,IAChB,MAAM,UAAU,UAAU;AAAA,EAC5B;AACA,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,gBAAgB,SAAS,QAAQ,GAAG,OAAO;AAC5F,cAAY,YAAY;AACxB,cAAY,UAAU;AACtB,cAAY,OAAO,KAAK,WAAW;AACnC,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAQA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,SAAS,OAAO,WAAW,UAAU;AAC/C,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,YAAY,CAAC,CAAC;AACnB,SAAK,WAAW,YAAY;AAAA,EAC9B;AACF;AACA,SAAS,mBAAmB,KAAK,SAAS;AACxC,MAAI,SAAS;AACb,MAAI;AACF,aAAS,IAAI,kBAAkB,sBAAsB;AAAA,EACvD,SAAS,GAAG;AACV,iBAAa,KAAK;AAAA,EACpB;AACA,QAAM,gBAAgB,WAAW,CAAC,QAAQ;AAC1C,eAAa,CAAC,CAAC,UAAU,cAAc,QAAQ,MAAM,MAAM,EAAE;AAC7D,SAAO;AACT;AACA,SAAS,sBAAsB,SAAS,UAAU,UAAU,MAAM,UAAU;AAC1E,QAAM,UAAU,SAAS,oBAAoB;AAC7C,QAAM,oBAAoB,mBAAmB,UAAU,MAAM,QAAQ;AACrE,QAAM,YAAY;AAAA,IAChB,MAAM,kBAAkB,UAAU;AAAA,EACpC;AACA,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU;AAAA,IACd,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,uCAAuC,GAAG,KAAK,KAAK,CAAC;AAAA,IACrD,qCAAqC,kBAAkB,aAAa;AAAA,IACpE,gBAAgB;AAAA,EAClB;AACA,QAAM,OAAO,iBAAiB,mBAAmB,QAAQ;AACzD,QAAM,UAAU,QAAQ;AACxB,WAAS,QAAQ,KAAK;AACpB,uBAAmB,GAAG;AACtB,QAAIC;AACJ,QAAI;AACF,MAAAA,OAAM,IAAI,kBAAkB,mBAAmB;AAAA,IACjD,SAAS,GAAG;AACV,mBAAa,KAAK;AAAA,IACpB;AACA,iBAAa,SAASA,IAAG,CAAC;AAC1B,WAAOA;AAAA,EACT;AACA,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,SAAS,OAAO;AACjE,cAAY,YAAY;AACxB,cAAY,UAAU;AACtB,cAAY,OAAO;AACnB,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAIA,SAAS,yBAAyB,SAAS,UAAU,KAAK,MAAM;AAC9D,QAAM,UAAU;AAAA,IACd,yBAAyB;AAAA,EAC3B;AACA,WAAS,QAAQ,KAAK;AACpB,UAAM,SAAS,mBAAmB,KAAK,CAAC,UAAU,OAAO,CAAC;AAC1D,QAAI,aAAa;AACjB,QAAI;AACF,mBAAa,IAAI,kBAAkB,6BAA6B;AAAA,IAClE,SAAS,GAAG;AACV,mBAAa,KAAK;AAAA,IACpB;AACA,QAAI,CAAC,YAAY;AAEf,mBAAa,KAAK;AAAA,IACpB;AACA,UAAM,OAAO,OAAO,UAAU;AAC9B,iBAAa,CAAC,MAAM,IAAI,CAAC;AACzB,WAAO,IAAI,sBAAsB,MAAM,KAAK,KAAK,GAAG,WAAW,OAAO;AAAA,EACxE;AACA,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,SAAS,OAAO;AACjE,cAAY,UAAU;AACtB,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAKA,IAAM,8BAA8B,MAAM;AAU1C,SAAS,wBAAwB,UAAU,SAAS,KAAK,MAAM,WAAW,UAAU,QAAQ,kBAAkB;AAG5G,QAAM,UAAU,IAAI,sBAAsB,GAAG,CAAC;AAC9C,MAAI,QAAQ;AACV,YAAQ,UAAU,OAAO;AACzB,YAAQ,QAAQ,OAAO;AAAA,EACzB,OAAO;AACL,YAAQ,UAAU;AAClB,YAAQ,QAAQ,KAAK,KAAK;AAAA,EAC5B;AACA,MAAI,KAAK,KAAK,MAAM,QAAQ,OAAO;AACjC,UAAM,oBAAoB;AAAA,EAC5B;AACA,QAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,MAAI,gBAAgB;AACpB,MAAI,YAAY,GAAG;AACjB,oBAAgB,KAAK,IAAI,eAAe,SAAS;AAAA,EACnD;AACA,QAAM,YAAY,QAAQ;AAC1B,QAAM,UAAU,YAAY;AAC5B,MAAI,gBAAgB;AACpB,MAAI,kBAAkB,GAAG;AACvB,oBAAgB;AAAA,EAClB,WAAW,cAAc,eAAe;AACtC,oBAAgB;AAAA,EAClB,OAAO;AACL,oBAAgB;AAAA,EAClB;AACA,QAAM,UAAU;AAAA,IACd,yBAAyB;AAAA,IACzB,wBAAwB,GAAG,QAAQ,OAAO;AAAA,EAC5C;AACA,QAAM,OAAO,KAAK,MAAM,WAAW,OAAO;AAC1C,MAAI,SAAS,MAAM;AACjB,UAAM,gBAAgB;AAAA,EACxB;AACA,WAAS,QAAQ,KAAK,MAAM;AAK1B,UAAM,eAAe,mBAAmB,KAAK,CAAC,UAAU,OAAO,CAAC;AAChE,UAAM,aAAa,QAAQ,UAAU;AACrC,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI;AACJ,QAAI,iBAAiB,SAAS;AAC5B,iBAAW,gBAAgB,SAAS,QAAQ,EAAE,KAAK,IAAI;AAAA,IACzD,OAAO;AACL,iBAAW;AAAA,IACb;AACA,WAAO,IAAI,sBAAsB,YAAY,MAAM,iBAAiB,SAAS,QAAQ;AAAA,EACvF;AACA,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,SAAS,OAAO;AACjE,cAAY,UAAU;AACtB,cAAY,OAAO,KAAK,WAAW;AACnC,cAAY,mBAAmB,oBAAoB;AACnD,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAsBA,IAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAahB,eAAe;AACjB;AAMA,IAAM,YAAY;AAAA;AAAA,EAEhB,SAAS;AAAA;AAAA,EAET,QAAQ;AAAA;AAAA,EAER,SAAS;AAAA;AAAA,EAET,UAAU;AAAA;AAAA,EAEV,OAAO;AACT;AACA,SAAS,+BAA+B,OAAO;AAC7C,UAAQ,OAAO;AAAA,IACb,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU;AAAA,IACnB,KAAK;AACH,aAAO,UAAU;AAAA,IACnB,KAAK;AACH,aAAO,UAAU;AAAA,IACnB,KAAK;AACH,aAAO,UAAU;AAAA,IACnB,KAAK;AACH,aAAO,UAAU;AAAA,IACnB;AAEE,aAAO,UAAU;AAAA,EACrB;AACF;AAkBA,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,gBAAgB,OAAO,UAAU;AAC3C,UAAM,cAAc,WAAW,cAAc,KAAK,SAAS,QAAQ,YAAY;AAC/E,QAAI,aAAa;AACf,WAAK,OAAO;AACZ,WAAK,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ;AAC1D,WAAK,WAAW,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,IACxE,OAAO;AACL,YAAM,WAAW;AACjB,WAAK,OAAO,SAAS;AACrB,WAAK,QAAQ,SAAS;AACtB,WAAK,WAAW,SAAS;AAAA,IAC3B;AAAA,EACF;AACF;AAwBA,SAAS,MAAM,GAAG;AAChB,SAAO,IAAI,kBAAkB;AAE3B,YAAQ,QAAQ,EAAE,KAAK,MAAM,EAAE,GAAG,aAAa,CAAC;AAAA,EAClD;AACF;AAmBA,IAAI,sBAAsB;AAK1B,IAAM,gBAAN,MAAoB;AAAA,EAClB,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,OAAO,IAAI,eAAe;AAC/B,SAAK,QAAQ;AACb,SAAK,aAAa,UAAU;AAC5B,SAAK,eAAe,IAAI,QAAQ,aAAW;AACzC,WAAK,KAAK,iBAAiB,SAAS,MAAM;AACxC,aAAK,aAAa,UAAU;AAC5B,gBAAQ;AAAA,MACV,CAAC;AACD,WAAK,KAAK,iBAAiB,SAAS,MAAM;AACxC,aAAK,aAAa,UAAU;AAC5B,gBAAQ;AAAA,MACV,CAAC;AACD,WAAK,KAAK,iBAAiB,QAAQ,MAAM;AACvC,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,KAAK,KAAK,QAAQ,iBAAiB,MAAM,SAAS;AAChD,QAAI,KAAK,OAAO;AACd,YAAM,cAAc,+BAA+B;AAAA,IACrD;AACA,QAAI,mBAAmB,GAAG,KAAK,iBAAiB;AAC9C,WAAK,KAAK,kBAAkB;AAAA,IAC9B;AACA,SAAK,QAAQ;AACb,SAAK,KAAK,KAAK,QAAQ,KAAK,IAAI;AAChC,QAAI,YAAY,QAAW;AACzB,iBAAW,OAAO,SAAS;AACzB,YAAI,QAAQ,eAAe,GAAG,GAAG;AAC/B,eAAK,KAAK,iBAAiB,KAAK,QAAQ,GAAG,EAAE,SAAS,CAAC;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,QAAW;AACtB,WAAK,KAAK,KAAK,IAAI;AAAA,IACrB,OAAO;AACL,WAAK,KAAK,KAAK;AAAA,IACjB;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,cAAc,uCAAuC;AAAA,IAC7D;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY;AACV,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,cAAc,oCAAoC;AAAA,IAC1D;AACA,QAAI;AACF,aAAO,KAAK,KAAK;AAAA,IACnB,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,cAAc,sCAAsC;AAAA,IAC5D;AACA,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,cAAc,uCAAuC;AAAA,IAC7D;AACA,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,KAAK,MAAM;AAAA,EAClB;AAAA,EACA,kBAAkB,QAAQ;AACxB,WAAO,KAAK,KAAK,kBAAkB,MAAM;AAAA,EAC3C;AAAA,EACA,0BAA0B,UAAU;AAClC,QAAI,KAAK,KAAK,UAAU,MAAM;AAC5B,WAAK,KAAK,OAAO,iBAAiB,YAAY,QAAQ;AAAA,IACxD;AAAA,EACF;AAAA,EACA,6BAA6B,UAAU;AACrC,QAAI,KAAK,KAAK,UAAU,MAAM;AAC5B,WAAK,KAAK,OAAO,oBAAoB,YAAY,QAAQ;AAAA,IAC3D;AAAA,EACF;AACF;AACA,IAAM,oBAAN,cAAgC,cAAc;AAAA,EAC5C,UAAU;AACR,SAAK,KAAK,eAAe;AAAA,EAC3B;AACF;AACA,SAAS,oBAAoB;AAC3B,SAAO,sBAAsB,oBAAoB,IAAI,IAAI,kBAAkB;AAC7E;AACA,IAAM,qBAAN,cAAiC,cAAc;AAAA,EAC7C,UAAU;AACR,SAAK,KAAK,eAAe;AAAA,EAC3B;AACF;AACA,SAAS,qBAAqB;AAC5B,SAAO,IAAI,mBAAmB;AAChC;AACA,IAAM,oBAAN,cAAgC,cAAc;AAAA,EAC5C,UAAU;AACR,SAAK,KAAK,eAAe;AAAA,EAC3B;AACF;AACA,SAAS,oBAAoB;AAC3B,SAAO,IAAI,kBAAkB;AAC/B;AAuBA,IAAM,aAAN,MAAiB;AAAA,EACf,8BAA8B;AAC5B,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAYC,MAAK,MAAM,WAAW,MAAM;AAItC,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,aAAa,CAAC;AACnB,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,OAAOA;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,YAAY,YAAY;AAC7B,SAAK,aAAa,KAAK,mBAAmB,KAAK,KAAK;AACpD,SAAK,SAAS;AACd,SAAK,gBAAgB,WAAS;AAC5B,WAAK,WAAW;AAChB,WAAK,mBAAmB;AACxB,UAAI,MAAM,YAAY,iBAAiB,QAAQ,GAAG;AAChD,aAAK,qBAAqB;AAC1B,aAAK,qBAAqB;AAAA,MAC5B,OAAO;AACL,cAAM,iBAAiB,KAAK,4BAA4B;AACxD,YAAI,kBAAkB,MAAM,QAAQ,CAAC,CAAC,GAAG;AACvC,cAAI,gBAAgB;AAClB,oBAAQ,mBAAmB;AAAA,UAC7B,OAAO;AACL,iBAAK,YAAY,KAAK,IAAI,KAAK,YAAY,GAAG,6BAA6B;AAC3E,iBAAK,qBAAqB;AAC1B,iBAAK,qBAAqB;AAC1B;AAAA,UACF;AAAA,QACF;AACA,aAAK,SAAS;AACd,aAAK;AAAA,UAAY;AAAA;AAAA,QAAqC;AAAA,MACxD;AAAA,IACF;AACA,SAAK,wBAAwB,WAAS;AACpC,WAAK,WAAW;AAChB,UAAI,MAAM,YAAY,iBAAiB,QAAQ,GAAG;AAChD,aAAK,qBAAqB;AAAA,MAC5B,OAAO;AACL,aAAK,SAAS;AACd,aAAK;AAAA,UAAY;AAAA;AAAA,QAAqC;AAAA,MACxD;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,eAAe,KAAK,KAAK,QAAQ;AACtC,SAAK,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,OAAO;AAAA,IACd,CAAC;AAGD,SAAK,SAAS,KAAK,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACnC;AAAA,EACA,wBAAwB;AACtB,UAAM,aAAa,KAAK;AACxB,WAAO,YAAU,KAAK,gBAAgB,aAAa,MAAM;AAAA,EAC3D;AAAA,EACA,mBAAmB,MAAM;AACvB,WAAO,KAAK,KAAK,IAAI,MAAM;AAAA,EAC7B;AAAA,EACA,SAAS;AACP,QAAI,KAAK,WAAW,WAA2C;AAE7D;AAAA,IACF;AACA,QAAI,KAAK,aAAa,QAAW;AAC/B;AAAA,IACF;AACA,QAAI,KAAK,YAAY;AACnB,UAAI,KAAK,eAAe,QAAW;AACjC,aAAK,iBAAiB;AAAA,MACxB,OAAO;AACL,YAAI,KAAK,oBAAoB;AAC3B,eAAK,aAAa;AAAA,QACpB,OAAO;AACL,cAAI,KAAK,sBAAsB;AAE7B,iBAAK,eAAe;AAAA,UACtB,OAAO;AACL,iBAAK,iBAAiB,WAAW,MAAM;AACrC,mBAAK,iBAAiB;AACtB,mBAAK,gBAAgB;AAAA,YACvB,GAAG,KAAK,SAAS;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,cAAc,UAAU;AAEtB,YAAQ,IAAI,CAAC,KAAK,KAAK,QAAQ,cAAc,GAAG,KAAK,KAAK,QAAQ,kBAAkB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,WAAW,aAAa,MAAM;AAC3H,cAAQ,KAAK,QAAQ;AAAA,QACnB,KAAK;AACH,mBAAS,WAAW,aAAa;AACjC;AAAA,QACF,KAAK;AACH,eAAK;AAAA,YAAY;AAAA;AAAA,UAA2C;AAC5D;AAAA,QACF,KAAK;AACH,eAAK;AAAA,YAAY;AAAA;AAAA,UAAuC;AACxD;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,cAAc,CAAC,WAAW,kBAAkB;AAC/C,YAAM,cAAc,sBAAsB,KAAK,KAAK,SAAS,KAAK,KAAK,WAAW,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS;AAC5H,YAAM,gBAAgB,KAAK,KAAK,QAAQ,aAAa,aAAa,mBAAmB,WAAW,aAAa;AAC7G,WAAK,WAAW;AAChB,oBAAc,WAAW,EAAE,KAAK,SAAO;AACrC,aAAK,WAAW;AAChB,aAAK,aAAa;AAClB,aAAK,qBAAqB;AAC1B,aAAK,qBAAqB;AAAA,MAC5B,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AAEb,UAAM,MAAM,KAAK;AACjB,SAAK,cAAc,CAAC,WAAW,kBAAkB;AAC/C,YAAM,cAAc,yBAAyB,KAAK,KAAK,SAAS,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK;AACpG,YAAM,gBAAgB,KAAK,KAAK,QAAQ,aAAa,aAAa,mBAAmB,WAAW,aAAa;AAC7G,WAAK,WAAW;AAChB,oBAAc,WAAW,EAAE,KAAK,YAAU;AACxC,iBAAS;AACT,aAAK,WAAW;AAChB,aAAK,gBAAgB,OAAO,OAAO;AACnC,aAAK,qBAAqB;AAC1B,YAAI,OAAO,WAAW;AACpB,eAAK,uBAAuB;AAAA,QAC9B;AACA,aAAK,qBAAqB;AAAA,MAC5B,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,UAAM,YAAY,8BAA8B,KAAK;AACrD,UAAM,SAAS,IAAI,sBAAsB,KAAK,cAAc,KAAK,MAAM,KAAK,CAAC;AAE7E,UAAM,MAAM,KAAK;AACjB,SAAK,cAAc,CAAC,WAAW,kBAAkB;AAC/C,UAAI;AACJ,UAAI;AACF,sBAAc,wBAAwB,KAAK,KAAK,WAAW,KAAK,KAAK,SAAS,KAAK,KAAK,OAAO,WAAW,KAAK,WAAW,QAAQ,KAAK,sBAAsB,CAAC;AAAA,MAChK,SAAS,GAAG;AACV,aAAK,SAAS;AACd,aAAK;AAAA,UAAY;AAAA;AAAA,QAAqC;AACtD;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK,KAAK,QAAQ;AAAA,QAAa;AAAA,QAAa;AAAA,QAAmB;AAAA,QAAW;AAAA;AAAA,QAAyB;AAAA;AAAA,MACzH;AACA,WAAK,WAAW;AAChB,oBAAc,WAAW,EAAE,KAAK,eAAa;AAC3C,aAAK,oBAAoB;AACzB,aAAK,WAAW;AAChB,aAAK,gBAAgB,UAAU,OAAO;AACtC,YAAI,UAAU,WAAW;AACvB,eAAK,YAAY,UAAU;AAC3B,eAAK;AAAA,YAAY;AAAA;AAAA,UAAyC;AAAA,QAC5D,OAAO;AACL,eAAK,qBAAqB;AAAA,QAC5B;AAAA,MACF,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,UAAM,cAAc,8BAA8B,KAAK;AAEvD,QAAI,cAAc,IAAI,KAAK,OAAO,MAAM;AACtC,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,SAAK,cAAc,CAAC,WAAW,kBAAkB;AAC/C,YAAM,cAAc,cAAc,KAAK,KAAK,SAAS,KAAK,KAAK,WAAW,KAAK,SAAS;AACxF,YAAM,kBAAkB,KAAK,KAAK,QAAQ,aAAa,aAAa,mBAAmB,WAAW,aAAa;AAC/G,WAAK,WAAW;AAChB,sBAAgB,WAAW,EAAE,KAAK,cAAY;AAC5C,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK;AAAA,UAAY;AAAA;AAAA,QAAyC;AAAA,MAC5D,GAAG,KAAK,qBAAqB;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,SAAK,cAAc,CAAC,WAAW,kBAAkB;AAC/C,YAAM,cAAc,gBAAgB,KAAK,KAAK,SAAS,KAAK,KAAK,WAAW,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS;AACtH,YAAM,mBAAmB,KAAK,KAAK,QAAQ,aAAa,aAAa,mBAAmB,WAAW,aAAa;AAChH,WAAK,WAAW;AAChB,uBAAiB,WAAW,EAAE,KAAK,cAAY;AAC7C,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK,gBAAgB,KAAK,MAAM,KAAK,CAAC;AACtC,aAAK;AAAA,UAAY;AAAA;AAAA,QAAyC;AAAA,MAC5D,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,aAAa;AAC3B,UAAM,MAAM,KAAK;AACjB,SAAK,eAAe;AAIpB,QAAI,KAAK,iBAAiB,KAAK;AAC7B,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,KAAK,WAAW,OAAO;AACzB;AAAA,IACF;AACA,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAIH,aAAK,SAAS;AACd,YAAI,KAAK,aAAa,QAAW;AAC/B,eAAK,SAAS,OAAO;AAAA,QACvB,WAAW,KAAK,gBAAgB;AAC9B,uBAAa,KAAK,cAAc;AAChC,eAAK,iBAAiB;AACtB,eAAK,qBAAqB;AAAA,QAC5B;AACA;AAAA,MACF,KAAK;AAIH,cAAM,YAAY,KAAK,WAAW;AAClC,aAAK,SAAS;AACd,YAAI,WAAW;AACb,eAAK,iBAAiB;AACtB,eAAK,OAAO;AAAA,QACd;AACA;AAAA,MACF,KAAK;AAGH,aAAK,SAAS;AACd,aAAK,iBAAiB;AACtB;AAAA,MACF,KAAK;AAIH,aAAK,SAAS,SAAS;AACvB,aAAK,SAAS;AACd,aAAK,iBAAiB;AACtB;AAAA,MACF,KAAK;AAKH,aAAK,SAAS;AACd,aAAK,iBAAiB;AACtB;AAAA,MACF,KAAK;AAKH,aAAK,SAAS;AACd,aAAK,iBAAiB;AACtB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AACH,aAAK;AAAA,UAAY;AAAA;AAAA,QAAuC;AACxD;AAAA,MACF,KAAK;AACH,aAAK;AAAA,UAAY;AAAA;AAAA,QAA2C;AAC5D;AAAA,MACF,KAAK;AACH,aAAK,OAAO;AACZ;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AACb,UAAM,gBAAgB,+BAA+B,KAAK,MAAM;AAChE,WAAO;AAAA,MACL,kBAAkB,KAAK;AAAA,MACvB,YAAY,KAAK,MAAM,KAAK;AAAA,MAC5B,OAAO;AAAA,MACP,UAAU,KAAK;AAAA,MACf,MAAM;AAAA,MACN,KAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,GAAG,MAAM,gBAAgB,OAAO,WAAW;AAEzC,UAAM,WAAW,IAAI,SAAS,kBAAkB,QAAW,SAAS,QAAW,aAAa,MAAS;AACrG,SAAK,aAAa,QAAQ;AAC1B,WAAO,MAAM;AACX,WAAK,gBAAgB,QAAQ;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,aAAa,YAAY;AAG5B,WAAO,KAAK,SAAS,KAAK,aAAa,UAAU;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,YAAY;AAChB,WAAO,KAAK,KAAK,MAAM,UAAU;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,UAAU;AACrB,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,gBAAgB,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,UAAU;AACxB,UAAM,IAAI,KAAK,WAAW,QAAQ,QAAQ;AAC1C,QAAI,MAAM,IAAI;AACZ,WAAK,WAAW,OAAO,GAAG,CAAC;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,SAAK,eAAe;AACpB,UAAM,YAAY,KAAK,WAAW,MAAM;AACxC,cAAU,QAAQ,cAAY;AAC5B,WAAK,gBAAgB,QAAQ;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,aAAa,QAAW;AAC/B,UAAI,YAAY;AAChB,cAAQ,+BAA+B,KAAK,MAAM,GAAG;AAAA,QACnD,KAAK,UAAU;AACb,gBAAM,KAAK,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAG;AAC/C;AAAA,QACF,KAAK,UAAU;AAAA,QACf,KAAK,UAAU;AACb,gBAAM,SAAS,KAAK;AACpB,gBAAM,OAAO,KAAK,MAAM,KAAK,MAAM,GAAG;AACtC;AAAA,QACF;AACE,sBAAY;AACZ;AAAA,MACJ;AACA,UAAI,WAAW;AACb,aAAK,WAAW;AAChB,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU;AACxB,UAAM,gBAAgB,+BAA+B,KAAK,MAAM;AAChE,YAAQ,eAAe;AAAA,MACrB,KAAK,UAAU;AAAA,MACf,KAAK,UAAU;AACb,YAAI,SAAS,MAAM;AACjB,gBAAM,SAAS,KAAK,KAAK,UAAU,KAAK,QAAQ,GAAG;AAAA,QACrD;AACA;AAAA,MACF,KAAK,UAAU;AACb,YAAI,SAAS,UAAU;AACrB,gBAAM,SAAS,SAAS,KAAK,QAAQ,GAAG;AAAA,QAC1C;AACA;AAAA,MACF,KAAK,UAAU;AAAA,MACf,KAAK,UAAU;AACb,YAAI,SAAS,OAAO;AAClB,gBAAM,SAAS,MAAM,KAAK,UAAU,KAAK,MAAM,GAAG;AAAA,QACpD;AACA;AAAA,MACF;AAEE,YAAI,SAAS,OAAO;AAClB,gBAAM,SAAS,MAAM,KAAK,UAAU,KAAK,MAAM,GAAG;AAAA,QACpD;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,UAAM,QAAQ,KAAK,WAAW,YAA2C,KAAK,WAAW;AACzF,QAAI,OAAO;AACT,WAAK;AAAA,QAAY;AAAA;AAAA,MAAyC;AAAA,IAC5D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,UAAM,QAAQ,KAAK,WAAW;AAC9B,QAAI,OAAO;AACT,WAAK;AAAA,QAAY;AAAA;AAAA,MAAyC;AAAA,IAC5D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,UAAM,QAAQ,KAAK,WAAW,aAA6C,KAAK,WAAW;AAC3F,QAAI,OAAO;AACT,WAAK;AAAA,QAAY;AAAA;AAAA,MAA6C;AAAA,IAChE;AACA,WAAO;AAAA,EACT;AACF;AA8BA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,YAAY,UAAU,UAAU;AAC9B,SAAK,WAAW;AAChB,QAAI,oBAAoB,UAAU;AAChC,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,WAAK,YAAY,SAAS,YAAY,UAAU,SAAS,IAAI;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,UAAU,KAAK,UAAU,SAAS,MAAM,KAAK,UAAU;AAAA,EAChE;AAAA,EACA,QAAQ,SAAS,UAAU;AACzB,WAAO,IAAI,WAAU,SAAS,QAAQ;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,UAAM,WAAW,IAAI,SAAS,KAAK,UAAU,QAAQ,EAAE;AACvD,WAAO,KAAK,QAAQ,KAAK,UAAU,QAAQ;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,cAAc,KAAK,UAAU,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,UAAM,UAAU,OAAO,KAAK,UAAU,IAAI;AAC1C,QAAI,YAAY,MAAM;AACpB,aAAO;AAAA,IACT;AACA,UAAM,WAAW,IAAI,SAAS,KAAK,UAAU,QAAQ,OAAO;AAC5D,WAAO,IAAI,WAAU,KAAK,UAAU,QAAQ;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,aAAaR,OAAM;AACjB,QAAI,KAAK,UAAU,SAAS,IAAI;AAC9B,YAAM,qBAAqBA,KAAI;AAAA,IACjC;AAAA,EACF;AACF;AAKA,SAAS,iBAAiBQ,MAAK,sBAAsB;AACnD,EAAAA,KAAI,aAAa,UAAU;AAC3B,QAAM,cAAc,WAAWA,KAAI,SAASA,KAAI,WAAW,oBAAoB;AAC/E,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,kBAAkB,EAAE,KAAK,WAAS,yBAAyB;AAAA;AAAA,IAEjH,MAAM,MAAM,GAAG,oBAAoB;AAAA,MAAI,KAAK;AAC9C;AAKA,SAAS,gBAAgBA,MAAK,sBAAsB;AAClD,EAAAA,KAAI,aAAa,SAAS;AAC1B,QAAM,cAAc,WAAWA,KAAI,SAASA,KAAI,WAAW,oBAAoB;AAC/E,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB,EAAE,KAAK,UAAQ,yBAAyB;AAAA;AAAA,IAE/G,KAAK,MAAM,GAAG,oBAAoB;AAAA,MAAI,IAAI;AAC5C;AAUA,SAAS,cAAcA,MAAK,MAAM,UAAU;AAC1C,EAAAA,KAAI,aAAa,aAAa;AAC9B,QAAM,cAAc,gBAAgBA,KAAI,SAASA,KAAI,WAAW,YAAY,GAAG,IAAI,QAAQ,MAAM,IAAI,GAAG,QAAQ;AAChH,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB,EAAE,KAAK,mBAAiB;AAC7F,WAAO;AAAA,MACL,UAAU;AAAA,MACV,KAAAA;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAUA,SAAS,uBAAuBA,MAAK,MAAM,UAAU;AACnD,EAAAA,KAAI,aAAa,sBAAsB;AACvC,SAAO,IAAI,WAAWA,MAAK,IAAI,QAAQ,IAAI,GAAG,QAAQ;AACxD;AAWA,SAAS,eAAeA,MAAK,OAAO,SAAS,aAAa,KAAK,UAAU;AACvE,EAAAA,KAAI,aAAa,cAAc;AAC/B,QAAM,OAAO,eAAe,QAAQ,KAAK;AACzC,QAAM,gBAAgB,OAAO,OAAO,CAAC,GAAG,QAAQ;AAChD,MAAI,cAAc,aAAa,KAAK,QAAQ,KAAK,eAAe,MAAM;AACpE,kBAAc,aAAa,IAAI,KAAK;AAAA,EACtC;AACA,SAAO,cAAcA,MAAK,KAAK,MAAM,aAAa;AACpD;AAoBA,SAAS,UAAUA,MAAK;AACtB,QAAM,cAAc;AAAA,IAClB,UAAU,CAAC;AAAA,IACX,OAAO,CAAC;AAAA,EACV;AACA,SAAO,cAAcA,MAAK,WAAW,EAAE,KAAK,MAAM,WAAW;AAC/D;AAOA,SAAe,cAAcA,MAAK,aAAa,WAAW;AAAA;AACxD,UAAM,MAAM;AAAA;AAAA,MAEV;AAAA,IACF;AACA,UAAM,WAAW,MAAM,OAAOA,MAAK,GAAG;AACtC,gBAAY,SAAS,KAAK,GAAG,SAAS,QAAQ;AAC9C,gBAAY,MAAM,KAAK,GAAG,SAAS,KAAK;AACxC,QAAI,SAAS,iBAAiB,MAAM;AAClC,YAAM,cAAcA,MAAK,aAAa,SAAS,aAAa;AAAA,IAC9D;AAAA,EACF;AAAA;AAuBA,SAAS,OAAOA,MAAK,SAAS;AAC5B,MAAI,WAAW,MAAM;AACnB,QAAI,OAAO,QAAQ,eAAe,UAAU;AAC1C;AAAA,QAAe;AAAA;AAAA,QAAqC;AAAA;AAAA,QAAkB;AAAA,QAAM,QAAQ;AAAA,MAAU;AAAA,IAChG;AAAA,EACF;AACA,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,cAAc;AAAA,IAAOA,KAAI;AAAA,IAASA,KAAI;AAAA;AAAA,IAA0B;AAAA,IAAK,GAAG;AAAA,IAAW,GAAG;AAAA,EAAU;AACtG,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB;AACzE;AAQA,SAAS,cAAcA,MAAK;AAC1B,EAAAA,KAAI,aAAa,aAAa;AAC9B,QAAM,cAAc,cAAcA,KAAI,SAASA,KAAI,WAAW,YAAY,CAAC;AAC3E,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB;AACzE;AAYA,SAAS,iBAAiBA,MAAK,UAAU;AACvC,EAAAA,KAAI,aAAa,gBAAgB;AACjC,QAAM,cAAc,iBAAiBA,KAAI,SAASA,KAAI,WAAW,UAAU,YAAY,CAAC;AACxF,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB;AACzE;AAOA,SAAS,iBAAiBA,MAAK;AAC7B,EAAAA,KAAI,aAAa,gBAAgB;AACjC,QAAM,cAAc,eAAeA,KAAI,SAASA,KAAI,WAAW,YAAY,CAAC;AAC5E,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB,EAAE,KAAK,SAAO;AACnF,QAAI,QAAQ,MAAM;AAChB,YAAM,cAAc;AAAA,IACtB;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAOA,SAAS,eAAeA,MAAK;AAC3B,EAAAA,KAAI,aAAa,cAAc;AAC/B,QAAM,cAAc,eAAeA,KAAI,SAASA,KAAI,SAAS;AAC7D,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB;AACzE;AAWA,SAAS,YAAYA,MAAK,WAAW;AACnC,QAAM,UAAU,MAAMA,KAAI,UAAU,MAAM,SAAS;AACnD,QAAM,WAAW,IAAI,SAASA,KAAI,UAAU,QAAQ,OAAO;AAC3D,SAAO,IAAI,UAAUA,KAAI,SAAS,QAAQ;AAC5C;AAkBA,SAAS,MAAM,MAAM;AACnB,SAAO,kBAAkB,KAAK,IAAI;AACpC;AAIA,SAAS,WAAW,SAAS,KAAK;AAChC,SAAO,IAAI,UAAU,SAAS,GAAG;AACnC;AAKA,SAAS,YAAYA,MAAK,MAAM;AAC9B,MAAIA,gBAAe,qBAAqB;AACtC,UAAM,UAAUA;AAChB,QAAI,QAAQ,WAAW,MAAM;AAC3B,YAAM,gBAAgB;AAAA,IACxB;AACA,UAAM,YAAY,IAAI,UAAU,SAAS,QAAQ,OAAO;AACxD,QAAI,QAAQ,MAAM;AAChB,aAAO,YAAY,WAAW,IAAI;AAAA,IACpC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AAEL,QAAI,SAAS,QAAW;AACtB,aAAO,YAAYA,MAAK,IAAI;AAAA,IAC9B,OAAO;AACL,aAAOA;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,MAAM,cAAc,WAAW;AACtC,MAAI,aAAa,MAAM,SAAS,GAAG;AACjC,QAAI,wBAAwB,qBAAqB;AAC/C,aAAO,WAAW,cAAc,SAAS;AAAA,IAC3C,OAAO;AACL,YAAM,gBAAgB,0EAA0E;AAAA,IAClG;AAAA,EACF,OAAO;AACL,WAAO,YAAY,cAAc,SAAS;AAAA,EAC5C;AACF;AACA,SAAS,cAAc,MAAM,QAAQ;AACnC,QAAM,eAAe,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,yBAAyB;AACrG,MAAI,gBAAgB,MAAM;AACxB,WAAO;AAAA,EACT;AACA,SAAO,SAAS,mBAAmB,cAAc,IAAI;AACvD;AACA,SAAS,yBAAyB,SAAS,MAAM,MAAM,UAAU,CAAC,GAAG;AACnE,UAAQ,OAAO,GAAG,IAAI,IAAI,IAAI;AAC9B,QAAM,SAAS,mBAAmB,IAAI;AAEtC,MAAI,QAAQ;AACV,SAAK,WAAW,WAAW,QAAQ,IAAI,EAAE;AACzC,yBAAqB,WAAW,IAAI;AAAA,EACtC;AACA,UAAQ,mBAAmB;AAC3B,UAAQ,YAAY,SAAS,UAAU;AACvC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,eAAe;AACjB,YAAQ,qBAAqB,OAAO,kBAAkB,WAAW,gBAAgB,oBAAoB,eAAe,QAAQ,IAAI,QAAQ,SAAS;AAAA,EACnJ;AACF;AAOA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAIA,KAAK,eAIL,mBAIA,MAAM,kBAAkB,mBAAmB,OAAO;AAChD,SAAK,MAAM;AACX,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,UAAU;AAMf,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,yBAAyB;AAC9B,SAAK,sBAAsB;AAC3B,SAAK,YAAY,oBAAI,IAAI;AACzB,QAAI,QAAQ,MAAM;AAChB,WAAK,UAAU,SAAS,mBAAmB,MAAM,KAAK,KAAK;AAAA,IAC7D,OAAO;AACL,WAAK,UAAU,cAAc,KAAK,OAAO,KAAK,IAAI,OAAO;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,MAAM;AACb,SAAK,QAAQ;AACb,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,UAAU,SAAS,mBAAmB,KAAK,MAAM,IAAI;AAAA,IAC5D,OAAO;AACL,WAAK,UAAU,cAAc,MAAM,KAAK,IAAI,OAAO;AAAA,IACrD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,qBAAqB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,mBAAmB,MAAM;AAC3B;AAAA,MAAe;AAAA;AAAA,MAAsB;AAAA;AAAA,MAAkB,OAAO;AAAA,MAAmB;AAAA,IAAI;AACrF,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,wBAAwB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,sBAAsB,MAAM;AAC9B;AAAA,MAAe;AAAA;AAAA,MAAsB;AAAA;AAAA,MAAkB,OAAO;AAAA,MAAmB;AAAA,IAAI;AACrF,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACM,gBAAgB;AAAA;AACpB,UAAI,KAAK,oBAAoB;AAC3B,eAAO,KAAK;AAAA,MACd;AACA,YAAM,OAAO,KAAK,cAAc,aAAa;AAAA,QAC3C,UAAU;AAAA,MACZ,CAAC;AACD,UAAI,MAAM;AACR,cAAM,YAAY,MAAM,KAAK,SAAS;AACtC,YAAI,cAAc,MAAM;AACtB,iBAAO,UAAU;AAAA,QACnB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACM,oBAAoB;AAAA;AACxB,UAAI,qBAAqB,KAAK,GAAG,KAAK,KAAK,IAAI,SAAS,eAAe;AACrE,eAAO,KAAK,IAAI,SAAS;AAAA,MAC3B;AACA,YAAM,WAAW,KAAK,kBAAkB,aAAa;AAAA,QACnD,UAAU;AAAA,MACZ,CAAC;AACD,UAAI,UAAU;AACZ,cAAM,SAAS,MAAM,SAAS,SAAS;AAKvC,eAAO,OAAO;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,UAAU,QAAQ,aAAW,QAAQ,OAAO,CAAC;AAClD,WAAK,UAAU,MAAM;AAAA,IACvB;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,KAAK;AACzB,WAAO,IAAI,UAAU,MAAM,GAAG;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,aAAa,gBAAgB,WAAW,eAAe,QAAQ,MAAM;AAChF,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,UAAU,YAAY,aAAa,KAAK,QAAQ,WAAW,eAAe,gBAAgB,KAAK,kBAAkB,OAAO,KAAK,gBAAgB;AACnJ,WAAK,UAAU,IAAI,OAAO;AAE1B,cAAQ,WAAW,EAAE,KAAK,MAAM,KAAK,UAAU,OAAO,OAAO,GAAG,MAAM,KAAK,UAAU,OAAO,OAAO,CAAC;AACpG,aAAO;AAAA,IACT,OAAO;AACL,aAAO,IAAI,YAAY,WAAW,CAAC;AAAA,IACrC;AAAA,EACF;AAAA,EACM,sBAAsB,aAAa,gBAAgB;AAAA;AACvD,YAAM,CAAC,WAAW,aAAa,IAAI,MAAM,QAAQ,IAAI,CAAC,KAAK,cAAc,GAAG,KAAK,kBAAkB,CAAC,CAAC;AACrG,aAAO,KAAK,aAAa,aAAa,gBAAgB,WAAW,aAAa,EAAE,WAAW;AAAA,IAC7F;AAAA;AACF;AACA,IAAM,OAAO;AACb,IAAM,UAAU;AAqBhB,IAAM,eAAe;AAgCrB,SAAS,SAASA,MAAK,sBAAsB;AAC3C,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,iBAAiBA,MAAK,oBAAoB;AACnD;AAUA,SAAS,YAAYA,MAAK,MAAM,UAAU;AACxC,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,cAAcA,MAAK,MAAM,QAAQ;AAC1C;AAWA,SAAS,aAAaA,MAAK,OAAO,QAAQ,UAAU;AAClD,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,eAAeA,MAAK,OAAO,QAAQ,QAAQ;AACpD;AAUA,SAAS,qBAAqBA,MAAK,MAAM,UAAU;AACjD,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,uBAAuBA,MAAK,MAAM,QAAQ;AACnD;AAQA,SAAS,YAAYA,MAAK;AACxB,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,cAAcA,IAAG;AAC1B;AAUA,SAAS,eAAeA,MAAK,UAAU;AACrC,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,iBAAiBA,MAAK,QAAQ;AACvC;AAuBA,SAAS,KAAKA,MAAK,SAAS;AAC1B,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,OAAOA,MAAK,OAAO;AAC5B;AAoBA,SAAS,QAAQA,MAAK;AACpB,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,UAAUA,IAAG;AACtB;AAQA,SAAS,eAAeA,MAAK;AAC3B,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,iBAAiBA,IAAG;AAC7B;AAOA,SAAS,aAAaA,MAAK;AACzB,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,eAAeA,IAAG;AAC3B;AACA,SAAS,IAAI,cAAc,WAAW;AACpC,iBAAe,mBAAmB,YAAY;AAC9C,SAAO,MAAM,cAAc,SAAS;AACtC;AAIA,SAAS,UAAUA,MAAK,WAAW;AACjC,SAAO,YAAYA,MAAK,SAAS;AACnC;AASA,SAAS,WAAW,MAAM,OAAO,GAAG,WAAW;AAC7C,QAAM,mBAAmB,GAAG;AAC5B,QAAM,kBAAkB,aAAa,KAAK,YAAY;AACtD,QAAM,kBAAkB,gBAAgB,aAAa;AAAA,IACnD,YAAY;AAAA,EACd,CAAC;AACD,QAAM,WAAW,kCAAkC,SAAS;AAC5D,MAAI,UAAU;AACZ,2BAAuB,iBAAiB,GAAG,QAAQ;AAAA,EACrD;AACA,SAAO;AACT;AAWA,SAAS,uBAAuB,SAAS,MAAM,MAAM,UAAU,CAAC,GAAG;AACjE,2BAAyB,SAAS,MAAM,MAAM,OAAO;AACvD;AAkCA,SAAS,QAAQA,MAAK,sBAAsB;AAC1C,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,gBAAgBA,MAAK,oBAAoB;AAClD;AAaA,SAAS,UAAUA,MAAK,sBAAsB;AAC5C,QAAM,IAAI,MAAM,gDAAgD;AAClE;AAOA,SAAS,QAAQ,WAAW;AAAA,EAC1B,oBAAoB;AACtB,GAAG;AACD,QAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAa;AACtD,QAAM,eAAe,UAAU,YAAY,eAAe;AAC1D,QAAM,mBAAmB,UAAU,YAAY,oBAAoB;AACnE,SAAO,IAAI,oBAAoB,KAAK,cAAc,kBAAkB,KAAK,WAAW;AACtF;AACA,SAAS,kBAAkB;AACzB,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAc;AAAA,IAAS;AAAA;AAAA,EAAmC,EAAE,qBAAqB,IAAI,CAAC;AAEvH,kBAAgB,MAAM,SAAS,EAAE;AAEjC,kBAAgB,MAAM,SAAS,SAAS;AAC1C;AACA,gBAAgB;;;ACl9GhB,SAAS,SAAS,MAAM;AACtB,SAAO,IAAI,WAAW,SAAU,YAAY;AAC1C,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,QAAQ;AACZ,QAAI,OAAO,SAAU,UAAU;AAC7B,qBAAe;AACf,eAAS;AAAA,IACX;AACA,QAAI,KAAK;AAMT,QAAI,WAAW,WAAY;AACzB,UAAI,CAAC,IAAI;AACP,aAAK,WAAW,WAAY;AAC1B,eAAK;AACL,cAAI,aAAc,YAAW,KAAK,YAAY;AAC9C,cAAI,SAAU,YAAW,SAAS;AAClC,cAAI,SAAU,YAAW,MAAM,KAAK;AAAA,QACtC,CAAC;AAAA,MACH;AAAA,IACF;AACA,eAAW,IAAI,WAAY;AAEzB,UAAI,GAAI,cAAa,EAAE;AAAA,IACzB,CAAC;AAED,SAAK,KAAK,QAAQ;AAElB,eAAW,IAAI,KAAK,GAAG,iBAAiB,IAAI,CAAC;AAI7C,eAAW,IAAI,KAAK,IAAI,EAAE,UAAU;AAAA,MAClC,MAAM;AAAA,MACN,OAAO,SAAU,KAAK;AACpB,mBAAW;AACX,gBAAQ;AACR,iBAAS;AAAA,MACX;AAAA,MACA,UAAU,WAAY;AACpB,mBAAW;AACX,iBAAS;AAAA,MACX;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AA2BA,SAAS,WAAW,MAAM;AACxB,SAAO,SAAS,IAAI,EAAE,KAAK,IAAI,SAAU,UAAU;AACjD,WAAO;AAAA,MACL,UAAU,SAAS,mBAAmB,SAAS,aAAa;AAAA,MAC5D;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ;;;AC3EA,IAAM,UAAN,MAAc;AAAA,EACZ,YAAY,MAAM;AAChB,WAAO;AAAA,EACT;AACF;AACA,IAAM,wBAAwB;AAC9B,IAAM,mBAAN,MAAuB;AAAA,EACrB,cAAc;AACZ,WAAO,mBAAmB,qBAAqB;AAAA,EACjD;AACF;AACA,IAAM,mBAAmB,MAAM,GAAG,GAAG,EAAE,KAAK,UAAU,MAAM,KAAK,mBAAmB,qBAAqB,CAAC,CAAC,GAAG,SAAS,CAAC;AACxH,IAAM,6BAA6B,IAAI,eAAe,gCAAgC;AACtF,SAAS,8BAA8B,UAAU,YAAY;AAC3D,QAAM,iBAAiB,sBAAsB,uBAAuB,UAAU,UAAU;AACxF,SAAO,kBAAkB,IAAI,QAAQ,cAAc;AACrD;AACA,SAAS,uBAAuB,IAAI;AAClC,SAAO,CAAC,MAAM,aAAa;AACzB,UAAM,UAAU,KAAK,kBAAkB,MAAM,GAAG,QAAQ,CAAC;AACzD,WAAO,IAAI,QAAQ,OAAO;AAAA,EAC5B;AACF;AACA,IAAM,6BAA6B;AAAA,EACjC,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,0BAA0B,CAAC;AACrD;AACA,IAAM,oCAAoC;AAAA,EACxC,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,0BAA0B,GAAG,WAAW;AAClE;AACA,IAAM,iBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,oBAAgB,eAAe,QAAQ,MAAM,KAAK;AAAA,EACpD;AAUF;AATE,cAJI,gBAIG,QAAO,SAAS,sBAAsB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,gBAAe;AAClD;AACA,cAPI,gBAOG,QAAyB,iBAAiB;AAAA,EAC/C,MAAM;AACR,CAAC;AACD,cAVI,gBAUG,QAAyB,iBAAiB;AAAA,EAC/C,WAAW,CAAC,mCAAmC,0BAA0B;AAC3E,CAAC;AAZH,IAAM,gBAAN;AAAA,CAcC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,mCAAmC,0BAA0B;AAAA,IAC3E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,eAAe,OAAO,MAAM;AACnC,kBAAgB,eAAe,QAAQ,MAAM,KAAK;AAClD,SAAO,yBAAyB,CAAC,mCAAmC,4BAA4B;AAAA,IAC9F,SAAS;AAAA,IACT,YAAY,uBAAuB,EAAE;AAAA,IACrC,OAAO;AAAA,IACP,MAAM;AAAA,MAAC;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAwB;AAAA;AAAA,MAEjD,CAAC,IAAI,SAAS,GAAG,aAAa;AAAA,MAAG,CAAC,IAAI,SAAS,GAAG,iBAAiB;AAAA,MAAG,GAAG;AAAA,IAAI;AAAA,EAC/E,CAAC,CAAC;AACJ;AAGA,IAAMC,YAAW,UAAU,UAAY,IAAI;AAC3C,IAAMC,cAAa,UAAU,YAAc,IAAI;AAG/C,IAAMC,0BAAyB,UAAU,wBAA0B,IAAI;AACvE,IAAMC,gBAAe,UAAU,cAAgB,MAAM,CAAC;AACtD,IAAMC,WAAU,UAAU,SAAW,IAAI;AACzC,IAAMC,YAAW,UAAU,UAAY,IAAI;AAC3C,IAAMC,kBAAiB,UAAU,gBAAkB,IAAI;AACvD,IAAMC,eAAc,UAAU,aAAe,IAAI;AACjD,IAAMC,cAAa,UAAU,YAAc,IAAI;AAC/C,IAAMC,aAAY,UAAU,WAAa,IAAI;AAC7C,IAAMC,QAAO,UAAU,MAAQ,IAAI;AACnC,IAAMC,WAAU,UAAU,SAAW,IAAI;AACzC,IAAMC,OAAM,UAAU,KAAO,MAAM,CAAC;AACpC,IAAMC,kBAAiB,UAAU,gBAAkB,MAAM,CAAC;AAC1D,IAAMC,eAAc,UAAU,aAAe,IAAI;AACjD,IAAMC,wBAAuB,UAAU,sBAAwB,IAAI;AACnE,IAAMC,gBAAe,UAAU,cAAgB,IAAI;", "names": ["StorageErrorCode", "name", "version", "canceled", "stop", "ErrorCode", "BlobBuilder", "start", "url", "ref", "fromTask", "percentage", "connectStorageEmulator", "deleteObject", "getBlob", "getBytes", "getDownloadURL", "getMetadata", "getStorage", "getStream", "list", "listAll", "ref", "updateMetadata", "uploadBytes", "uploadBytesResumable", "uploadString"]}