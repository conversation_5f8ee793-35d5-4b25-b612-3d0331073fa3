<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      {{ currentStep === 'capture' ? 'Identify Plant' :
         currentStep === 'configure' ? 'Configure Identification' :
         currentStep === 'processing' ? 'Processing...' : 'Results' }}
    </ion-title>
    <ion-button
      *ngIf="currentStep !== 'capture'"
      slot="start"
      fill="clear"
      (click)="resetIdentification()">
      <ion-icon name="arrow-back"></ion-icon>
    </ion-button>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <!-- Demo Mode Banner -->
  <div *ngIf="isDemoMode" class="demo-banner">
    <ion-chip color="warning">
      <ion-icon name="construct"></ion-icon>
      <ion-label>Demo Mode - Using sample data</ion-label>
    </ion-chip>
  </div>

  <!-- Step 1: Image Capture -->
  <div *ngIf="currentStep === 'capture'" class="capture-container">
    <div class="capture-content">
      <div class="capture-header">
        <ion-icon name="camera" size="large" color="primary"></ion-icon>
        <h2>Take a Photo</h2>
        <p>Capture a clear image of the plant you want to identify</p>
      </div>

      <div class="capture-tips">
        <ion-card>
          <ion-card-header>
            <ion-card-title>📸 Photography Tips</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ul>
              <li>Ensure good lighting</li>
              <li>Fill the frame with the plant part</li>
              <li>Keep the image sharp and in focus</li>
              <li>Avoid shadows and reflections</li>
            </ul>
          </ion-card-content>
        </ion-card>
      </div>

      <ion-button
        expand="block"
        size="large"
        (click)="presentImageSourceActionSheet()"
        class="capture-button">
        <ion-icon name="camera" slot="start"></ion-icon>
        Take Photo
      </ion-button>
    </div>
  </div>

  <!-- Step 2: Configuration -->
  <div *ngIf="currentStep === 'configure'" class="configure-container">
    <!-- Image Preview -->
    <ion-card class="image-preview-card">
      <ion-img [src]="capturedImage" [alt]="'Captured plant image'"></ion-img>
      <div class="image-overlay">
        <ion-button fill="clear" (click)="retakePhoto()" class="retake-button">
          <ion-icon name="camera" slot="start"></ion-icon>
          Retake
        </ion-button>
      </div>
    </ion-card>

    <!-- Configuration Options -->
    <ion-card class="config-card">
      <ion-card-header>
        <ion-card-title>Identification Settings</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- Plant Part Selection -->
        <ion-item lines="none" class="config-item">
          <ion-label position="stacked">Plant Part</ion-label>
          <ion-select
            [(ngModel)]="selectedPlantPart"
            placeholder="Select plant part"
            interface="action-sheet">
            <ion-select-option
              *ngFor="let part of plantParts"
              [value]="part.value">
              {{ part.label }}
            </ion-select-option>
          </ion-select>
        </ion-item>

        <!-- Flora Region Selection -->
        <ion-item lines="none" class="config-item">
          <ion-label position="stacked">Flora Region</ion-label>
          <ion-select
            [(ngModel)]="selectedFloraRegion"
            placeholder="Select region"
            interface="action-sheet">
            <ion-select-option
              *ngFor="let flora of floraRegions"
              [value]="flora.id">
              {{ flora.name }}
            </ion-select-option>
          </ion-select>
        </ion-item>

        <!-- Location Toggle -->
        <ion-item lines="none" class="config-item">
          <ion-icon name="location" slot="start" color="medium"></ion-icon>
          <ion-label>
            <h3>Use Location</h3>
            <p>Improve accuracy with location context</p>
          </ion-label>
          <ion-checkbox [(ngModel)]="useLocation" slot="end"></ion-checkbox>
        </ion-item>

        <ion-button
          expand="block"
          (click)="startIdentification()"
          [disabled]="!selectedPlantPart || !selectedFloraRegion"
          class="identify-button"
          aria-label="Start plant identification process">
          <ion-icon name="analytics" slot="start" aria-hidden="true"></ion-icon>
          Identify Plant
        </ion-button>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Step 3: Processing -->
  <div *ngIf="currentStep === 'processing'" class="processing-container" role="status" aria-live="polite">
    <div class="processing-content">
      <div class="processing-header">
        <ion-spinner name="crescent" color="primary" aria-label="Processing plant identification"></ion-spinner>
        <h2>Analyzing Plant</h2>
        <p>Our AI is identifying your plant...</p>
      </div>

      <ion-card class="processing-card">
        <ion-card-content>
          <div class="progress-container">
            <ion-progress-bar
              [value]="processingProgress / 100"
              color="primary">
            </ion-progress-bar>
            <div class="progress-text">{{ processingProgress }}%</div>
          </div>

          <div class="processing-steps">
            <div class="step" [class.active]="processingProgress >= 20">
              <ion-icon name="camera"></ion-icon>
              <span>Analyzing image</span>
            </div>
            <div class="step" [class.active]="processingProgress >= 50">
              <ion-icon name="analytics"></ion-icon>
              <span>Identifying features</span>
            </div>
            <div class="step" [class.active]="processingProgress >= 80">
              <ion-icon name="leaf"></ion-icon>
              <span>Matching species</span>
            </div>
            <div class="step" [class.active]="processingProgress >= 100">
              <ion-icon name="checkmark-circle"></ion-icon>
              <span>Complete</span>
            </div>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>

  <!-- Step 4: Results -->
  <div *ngIf="currentStep === 'results'" class="results-container">
    <div *ngIf="identificationResult && identificationResult.species.length > 0">
      <!-- Image and Quick Actions -->
      <ion-card class="result-image-card">
        <ion-img [src]="capturedImage" [alt]="'Identified plant'"></ion-img>
        <div class="result-actions">
          <ion-button fill="clear" (click)="saveAsObservation()" aria-label="Save identification as observation">
            <ion-icon name="save" slot="start" aria-hidden="true"></ion-icon>
            Save
          </ion-button>
          <ion-button fill="clear" (click)="shareResults()" aria-label="Share identification results">
            <ion-icon name="share" slot="start" aria-hidden="true"></ion-icon>
            Share
          </ion-button>
          <ion-button fill="clear" (click)="resetIdentification()" aria-label="Start new identification">
            <ion-icon name="refresh" slot="start" aria-hidden="true"></ion-icon>
            New
          </ion-button>
        </div>
      </ion-card>

      <!-- Species Results -->
      <div class="species-results">
        <h3>Identification Results</h3>
        <ion-card
          *ngFor="let species of identificationResult.species; let i = index"
          class="species-card"
          [class.top-result]="i === 0">
          <ion-card-content>
            <div class="species-header">
              <div class="species-info">
                <h4>{{ species.name }}</h4>
                <p class="scientific-name">{{ species.scientificName }}</p>
                <div class="taxonomy" *ngIf="species.taxonomy">
                  <span class="family">{{ species.taxonomy.family }}</span>
                </div>
              </div>
              <div class="confidence-badge">
                <ion-chip [color]="getConfidenceColor(species.confidence)">
                  {{ species.confidence }}%
                </ion-chip>
                <small>{{ getConfidenceText(species.confidence) }}</small>
              </div>
            </div>

            <div class="species-details" *ngIf="species.description || species.habitat">
              <p *ngIf="species.description">{{ species.description }}</p>
              <p *ngIf="species.habitat"><strong>Habitat:</strong> {{ species.habitat }}</p>
            </div>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Analysis Details -->
      <ion-card class="analysis-card" *ngIf="identificationResult.imageAnalysis">
        <ion-card-header>
          <ion-card-title>Analysis Details</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <div class="analysis-grid">
            <div class="analysis-item">
              <h5>Image Quality</h5>
              <ion-progress-bar
                [value]="identificationResult.imageAnalysis.quality.overall / 100"
                color="primary">
              </ion-progress-bar>
              <span>{{ identificationResult.imageAnalysis.quality.overall }}%</span>
            </div>

            <div class="analysis-item">
              <h5>Plant Coverage</h5>
              <ion-progress-bar
                [value]="identificationResult.imageAnalysis.composition.plantCoverage / 100"
                color="secondary">
              </ion-progress-bar>
              <span>{{ identificationResult.imageAnalysis.composition.plantCoverage }}%</span>
            </div>
          </div>

          <div class="detected-features" *ngIf="identificationResult.imageAnalysis.features.length > 0">
            <h5>Detected Features</h5>
            <div class="features-list">
              <ion-chip
                *ngFor="let feature of identificationResult.imageAnalysis.features"
                color="tertiary">
                {{ feature.description }}
              </ion-chip>
            </div>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- No Results -->
    <div *ngIf="!identificationResult || identificationResult.species.length === 0" class="no-results">
      <ion-card>
        <ion-card-content>
          <div class="no-results-content">
            <ion-icon name="close-circle" size="large" color="medium" aria-hidden="true"></ion-icon>
            <h3>No Match Found</h3>
            <p>We couldn't identify this plant. Try taking another photo with better lighting or a different angle.</p>
            <ion-button expand="block" (click)="resetIdentification()" aria-label="Try plant identification again">
              <ion-icon name="camera" slot="start" aria-hidden="true"></ion-icon>
              Try Again
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>

  <!-- Error Message -->
  <ion-card *ngIf="errorMessage" class="error-card" role="alert" aria-live="polite">
    <ion-card-content>
      <div class="error-content">
        <ion-icon name="close-circle" color="danger" aria-hidden="true"></ion-icon>
        <p>{{ errorMessage }}</p>
        <ion-button fill="clear" (click)="errorMessage = null" aria-label="Dismiss error message">Dismiss</ion-button>
      </div>
    </ion-card-content>
  </ion-card>
</ion-content>
