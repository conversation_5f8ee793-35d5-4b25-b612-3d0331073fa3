// For information on how to create your own theme, please see:
// http://ionicframework.com/docs/theming/

/** Nature-Inspired Color Theme **/
:root {
  /** primary - <PERSON> **/
  --ion-color-primary: #2d5a27;
  --ion-color-primary-rgb: 45, 90, 39;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #284f22;
  --ion-color-primary-tint: #42693d;

  /** secondary - Fresh Green **/
  --ion-color-secondary: #4caf50;
  --ion-color-secondary-rgb: 76, 175, 80;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #439a46;
  --ion-color-secondary-tint: #5eb862;

  /** tertiary - Sage Green **/
  --ion-color-tertiary: #8bc34a;
  --ion-color-tertiary-rgb: 139, 195, 74;
  --ion-color-tertiary-contrast: #000000;
  --ion-color-tertiary-contrast-rgb: 0, 0, 0;
  --ion-color-tertiary-shade: #7aac41;
  --ion-color-tertiary-tint: #97c95c;

  /** success - Bright Green **/
  --ion-color-success: #66bb6a;
  --ion-color-success-rgb: 102, 187, 106;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #5aa45d;
  --ion-color-success-tint: #75c279;

  /** warning - Amber **/
  --ion-color-warning: #ffc107;
  --ion-color-warning-rgb: 255, 193, 7;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0aa06;
  --ion-color-warning-tint: #ffca20;

  /** danger - Soft Red **/
  --ion-color-danger: #e57373;
  --ion-color-danger-rgb: 229, 115, 115;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #ca6565;
  --ion-color-danger-tint: #e88181;

  /** dark - Deep Forest **/
  --ion-color-dark: #1b3b17;
  --ion-color-dark-rgb: 27, 59, 23;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #183315;
  --ion-color-dark-tint: #324f2e;

  /** medium - Muted Green **/
  --ion-color-medium: #81c784;
  --ion-color-medium-rgb: 129, 199, 132;
  --ion-color-medium-contrast: #000000;
  --ion-color-medium-contrast-rgb: 0, 0, 0;
  --ion-color-medium-shade: #72af74;
  --ion-color-medium-tint: #8ecc90;

  /** light - Soft White **/
  --ion-color-light: #f8fdf8;
  --ion-color-light-rgb: 248, 253, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #dadeda;
  --ion-color-light-tint: #f9fdf9;

  /** Custom Nature Colors **/
  --ion-color-leaf: #689f38;
  --ion-color-leaf-rgb: 104, 159, 56;
  --ion-color-leaf-contrast: #ffffff;
  --ion-color-leaf-contrast-rgb: 255, 255, 255;
  --ion-color-leaf-shade: #5c8c31;
  --ion-color-leaf-tint: #77a84c;

  --ion-color-mint: #a5d6a7;
  --ion-color-mint-rgb: 165, 214, 167;
  --ion-color-mint-contrast: #000000;
  --ion-color-mint-contrast-rgb: 0, 0, 0;
  --ion-color-mint-shade: #91bc93;
  --ion-color-mint-tint: #aedab0;

  --ion-color-forest: #2e7d32;
  --ion-color-forest-rgb: 46, 125, 50;
  --ion-color-forest-contrast: #ffffff;
  --ion-color-forest-contrast-rgb: 255, 255, 255;
  --ion-color-forest-shade: #286e2c;
  --ion-color-forest-tint: #438a47;

  /** Background Colors **/
  --ion-background-color: #fafffe;
  --ion-background-color-rgb: 250, 255, 254;

  --ion-text-color: #1b3b17;
  --ion-text-color-rgb: 27, 59, 23;

  --ion-card-background: #ffffff;
  --ion-item-background: #ffffff;

  --ion-toolbar-background: #ffffff;
  --ion-toolbar-border-color: #e8f5e8;

  --ion-tab-bar-background: #ffffff;
  --ion-tab-bar-border-color: #e8f5e8;
}

/** Custom CSS Classes for Nature Theme **/
.leaf {
  --color: var(--ion-color-leaf);
}

.mint {
  --color: var(--ion-color-mint);
}

.forest {
  --color: var(--ion-color-forest);
}

/** Enhanced Card Styling **/
ion-card {
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(45, 90, 39, 0.1);
  border: 1px solid #e8f5e8;
}

/** Enhanced Button Styling **/
ion-button {
  --border-radius: 12px;
  font-weight: 600;
}

ion-button.button-fill-solid {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
}

/** Tab Bar Styling **/
ion-tab-bar {
  --border: 1px solid var(--ion-tab-bar-border-color);
  border-radius: 20px 20px 0 0;
  margin: 0;
}

ion-tab-button {
  --color: var(--ion-color-medium);
  --color-selected: var(--ion-color-primary);
}

/** Toolbar Styling **/
ion-toolbar {
  --border-color: var(--ion-toolbar-border-color);
  --background: var(--ion-toolbar-background);
}

/** Input Styling **/
ion-input, ion-textarea, ion-select {
  --background: #f8fdf8;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
}

/** Item Styling **/
ion-item {
  --border-radius: 12px;
  --background: var(--ion-item-background);
  margin-bottom: 8px;
}

/** Chip Styling **/
ion-chip {
  --background: var(--ion-color-light);
  --color: var(--ion-color-primary);
  border-radius: 20px;
}
