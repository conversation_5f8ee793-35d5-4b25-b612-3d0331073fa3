import{a as le}from"./chunk-SPQUQXZ2.js";import{F as oe,J as re,K as ae,N as ce,R as se,a as ee,f as te,h as ne,y as ie}from"./chunk-AWIL3PGF.js";import{b as Z}from"./chunk-UUEAGYFQ.js";import"./chunk-N2M46O22.js";import{$a as q,B as M,Ba as z,C as a,D as f,E as x,F as C,G as m,Ha as R,I as e,Ia as V,J as t,K as d,Ka as D,L as h,La as F,M as g,Ma as G,N as p,Na as A,O as i,Oa as L,P as _,Pa as j,Q as P,Qa as N,R as y,Ta as $,Ua as U,Wa as B,X as w,Xa as Y,Z as I,_a as Q,ab as J,ba as S,ca as k,fa as T,ga as E,hb as K,ib as W,na as H,qb as X,v,w as u}from"./chunk-AZEIYKMX.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{g as O}from"./chunk-OLRFWS6T.js";function de(c,l){if(c&1&&(e(0,"ion-card",18)(1,"ion-card-header")(2,"ion-card-title"),i(3,"Your Progress"),t()(),e(4,"ion-card-content")(5,"ion-grid")(6,"ion-row")(7,"ion-col",19)(8,"div",20)(9,"div",21),i(10),t(),e(11,"div",22),i(12,"Observations"),t()()(),e(13,"ion-col",19)(14,"div",20)(15,"div",21),i(16),t(),e(17,"div",22),i(18,"Species"),t()()(),e(19,"ion-col",19)(20,"div",20)(21,"div",21),i(22),w(23,"number"),t(),e(24,"div",22),i(25,"Avg. Confidence"),t()()()()()()()),c&2){let o=p();a(10),_(o.stats.total||0),a(6),_(o.stats.speciesCount||0),a(6),P("",I(23,3,o.stats.averageConfidence||0,"1.0-0"),"%")}}function ge(c,l){if(c&1){let o=h();e(0,"ion-item",26),g("click",function(){let s=v(o).$implicit,r=p(2);return u(r.navigateToObservation(s))}),e(1,"ion-avatar",27),d(2,"img",28),t(),e(3,"ion-label")(4,"h3"),i(5),t(),e(6,"p"),i(7),t(),e(8,"ion-chip",29),i(9),t()(),d(10,"ion-icon",30),t()}if(c&2){let o=l.$implicit,n=p(2);a(2),m("src",o.image.thumbnailUrl||o.image.url,M)("alt",o.species.name),a(3),_(o.species.name),a(2),_(o.species.scientificName),a(),m("color",n.getConfidenceColor(o.species.confidence)),a(),P(" ",o.species.confidence,"% confidence ")}}function pe(c,l){if(c&1){let o=h();e(0,"ion-card",23)(1,"ion-card-header")(2,"ion-card-title"),i(3,"Recent Identifications"),t()(),e(4,"ion-card-content"),C(5,ge,11,6,"ion-item",24),e(6,"ion-button",25),g("click",function(){v(o);let s=p();return u(s.navigateToObservations())}),i(7," View All Observations "),t()()()}if(c&2){let o=p();a(5),m("ngForOf",o.recentObservations)}}function _e(c,l){if(c&1){let o=h();e(0,"ion-card",31)(1,"ion-card-content")(2,"div",32),d(3,"ion-icon",33),e(4,"h3"),i(5,"No observations yet"),t(),e(6,"p"),i(7,"Start by identifying your first plant!"),t(),e(8,"ion-button",34),g("click",function(){v(o);let s=p();return u(s.navigateToIdentify())}),d(9,"ion-icon",9),i(10," Take Your First Photo "),t()()()()}}var ye=(()=>{let l=class l{constructor(n,s,r){this.authService=n,this.observationService=s,this.router=r,this.user=null,this.recentObservations=[],this.stats=null,this.isLoading=!1,ee({camera:te,leaf:ie,map:oe,statsChart:se,person:re,settings:ce,chevronForward:ne,refresh:ae})}ngOnInit(){this.loadData(),this.authService.user$.subscribe(n=>{this.user=n}),this.observationService.userObservations$.subscribe(n=>{this.recentObservations=n.slice(0,3)})}loadData(){return O(this,null,function*(){this.isLoading=!0;try{yield this.observationService.loadUserObservations(3),this.stats=yield this.observationService.getObservationStats()}catch(n){console.error("Error loading home data:",n)}finally{this.isLoading=!1}})}doRefresh(n){return O(this,null,function*(){try{yield this.loadData()}finally{n.target.complete()}})}navigateToIdentify(){this.router.navigate(["/tabs/identify"])}navigateToObservations(){this.router.navigate(["/tabs/observations"])}navigateToMap(){this.router.navigate(["/tabs/map"])}navigateToProfile(){this.router.navigate(["/tabs/profile"])}navigateToObservation(n){this.router.navigate(["/observation",n.id])}getGreeting(){let n=new Date().getHours();return n<12?"Good morning":n<18?"Good afternoon":"Good evening"}getConfidenceColor(n){return n>=80?"success":n>=60?"warning":"danger"}};l.\u0275fac=function(s){return new(s||l)(f(Z),f(le),f(H))},l.\u0275cmp=x({type:l,selectors:[["app-home"]],decls:37,vars:7,consts:[[3,"translucent"],[3,"fullscreen"],["slot","fixed",3,"ionRefresh"],["pullingIcon","refresh","pullingText","Pull to refresh","refreshingSpinner","circles","refreshingText","Refreshing..."],[1,"home-container"],[1,"welcome-card"],[1,"welcome-content"],[1,"greeting"],["expand","block","size","large",1,"identify-button",3,"click"],["name","camera","slot","start"],["class","stats-card",4,"ngIf"],[1,"actions-card"],["size","6"],["expand","block","fill","outline",3,"click"],["name","leaf","slot","start"],["name","map","slot","start"],["class","recent-card",4,"ngIf"],["class","empty-state-card",4,"ngIf"],[1,"stats-card"],["size","4",1,"stat-col"],[1,"stat-item"],[1,"stat-number"],[1,"stat-label"],[1,"recent-card"],["button","","lines","none","class","observation-item",3,"click",4,"ngFor","ngForOf"],["expand","block","fill","clear",1,"view-all-button",3,"click"],["button","","lines","none",1,"observation-item",3,"click"],["slot","start"],["onerror","this.src='assets/images/plant-placeholder.png'",3,"src","alt"],["size","small",3,"color"],["name","chevron-forward","slot","end"],[1,"empty-state-card"],[1,"empty-state"],["name","leaf","size","large","color","medium"],["expand","block",3,"click"]],template:function(s,r){s&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),i(3,"PlantDetect"),t()()(),e(4,"ion-content",1)(5,"ion-refresher",2),g("ionRefresh",function(me){return r.doRefresh(me)}),d(6,"ion-refresher-content",3),t(),e(7,"div",4)(8,"ion-card",5)(9,"ion-card-content")(10,"div",6)(11,"div",7)(12,"h2"),i(13),t(),e(14,"p"),i(15,"Ready to discover some plants today?"),t()(),e(16,"ion-button",8),g("click",function(){return r.navigateToIdentify()}),d(17,"ion-icon",9),i(18," Identify a Plant "),t()()()(),C(19,de,26,6,"ion-card",10),e(20,"ion-card",11)(21,"ion-card-header")(22,"ion-card-title"),i(23,"Quick Actions"),t()(),e(24,"ion-card-content")(25,"ion-grid")(26,"ion-row")(27,"ion-col",12)(28,"ion-button",13),g("click",function(){return r.navigateToObservations()}),d(29,"ion-icon",14),i(30," My Plants "),t()(),e(31,"ion-col",12)(32,"ion-button",13),g("click",function(){return r.navigateToMap()}),d(33,"ion-icon",15),i(34," Explore Map "),t()()()()()(),C(35,pe,8,1,"ion-card",16)(36,_e,11,0,"ion-card",17),t()()),s&2&&(m("translucent",!0),a(4),m("fullscreen",!0),a(9),y("",r.getGreeting(),"",r.user!=null&&r.user.name?", "+(r.user==null?null:r.user.name):"","!"),a(6),m("ngIf",r.stats),a(16),m("ngIf",r.recentObservations.length>0),a(),m("ngIf",r.recentObservations.length===0&&!r.isLoading))},dependencies:[E,S,k,T,z,N,U,K,W,D,G,A,F,V,X,$,J,j,B,Y,R,L,Q,q],styles:[".home-container[_ngcontent-%COMP%]{padding:16px 16px 100px}.welcome-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--ion-color-primary),var(--ion-color-secondary));color:#fff;margin-bottom:16px}.welcome-card[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]{text-align:center;padding:20px 0}.welcome-card[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .greeting[_ngcontent-%COMP%]{margin-bottom:24px}.welcome-card[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .greeting[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 8px;font-size:1.5rem;font-weight:600}.welcome-card[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .greeting[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;opacity:.9;font-size:1rem}.welcome-card[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .identify-button[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .2);--color: white;--border-radius: 12px;font-weight:600;height:56px}.welcome-card[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .identify-button[_ngcontent-%COMP%]:hover{--background: rgba(255, 255, 255, .3)}.stats-card[_ngcontent-%COMP%]{margin-bottom:16px}.stats-card[_ngcontent-%COMP%]   .stat-col[_ngcontent-%COMP%]{text-align:center;padding:8px}.stats-card[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:var(--ion-color-primary);line-height:1}.stats-card[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.875rem;color:var(--ion-color-medium);margin-top:4px}.actions-card[_ngcontent-%COMP%]{margin-bottom:16px}.actions-card[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 12px;height:48px;font-weight:500}.recent-card[_ngcontent-%COMP%]{margin-bottom:16px}.recent-card[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]{--padding-start: 0;--padding-end: 0;margin-bottom:8px;border-radius:12px;background:var(--ion-color-light)}.recent-card[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]{width:56px;height:56px}.recent-card[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:8px;object-fit:cover}.recent-card[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-weight:600;margin-bottom:4px;color:var(--ion-color-dark)}.recent-card[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-style:italic;color:var(--ion-color-medium);margin-bottom:8px}.recent-card[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{font-size:.75rem;height:24px}.recent-card[_ngcontent-%COMP%]   .observation-item[_ngcontent-%COMP%]:hover{background:var(--ion-color-light-shade)}.recent-card[_ngcontent-%COMP%]   .view-all-button[_ngcontent-%COMP%]{margin-top:16px;--color: var(--ion-color-primary);font-weight:500}.empty-state-card[_ngcontent-%COMP%]{margin-top:40px}.empty-state-card[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.empty-state-card[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-bottom:16px;opacity:.6}.empty-state-card[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-dark);font-weight:600}.empty-state-card[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 24px;color:var(--ion-color-medium)}.empty-state-card[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 12px;height:48px;font-weight:500}@media (prefers-color-scheme: dark){.welcome-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--ion-color-primary-shade),var(--ion-color-secondary-shade))}.observation-item[_ngcontent-%COMP%]{background:var(--ion-color-dark)}.observation-item[_ngcontent-%COMP%]:hover{background:var(--ion-color-dark-shade)}}@media (max-width: 768px){.home-container[_ngcontent-%COMP%]{padding:12px}.welcome-content[_ngcontent-%COMP%]{padding:16px 0!important}.welcome-content[_ngcontent-%COMP%]   .greeting[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.25rem!important}.stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{font-size:1.5rem!important}}"]});let c=l;return c})();export{ye as HomePage};
