import{a as oe}from"./chunk-NTAPKKHP.js";import{B as ne,D as te,a as q,b as G,e as J,f as K,k as Q,m as W,o as X,t as Z,v as ee}from"./chunk-TV52TPWK.js";import"./chunk-ZBJDNBYI.js";import"./chunk-YUFJ6257.js";import{$a as Y,C as r,D as C,E as P,F as p,G as s,I as t,Ia as L,J as o,K as g,Ka as T,L as u,La as z,M as _,N as m,O as c,Oa as F,P as v,Q as b,Qa as D,R as M,Ra as R,Sa as j,Ua as $,Va as B,W as y,_a as N,ba as I,ca as S,cb as V,ga as w,gb as A,hb as U,oa as k,pb as H,sa as E,v as h,w as x}from"./chunk-DVLCYDDQ.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{g as O}from"./chunk-OLRFWS6T.js";var re=n=>["/observation",n];function ae(n,a){n&1&&(t(0,"div",13),g(1,"ion-spinner",14),t(2,"p"),c(3,"Loading your observations..."),o()())}function ce(n,a){if(n&1){let e=u();t(0,"div",15)(1,"ion-card",16)(2,"ion-card-content")(3,"div",17),g(4,"ion-icon",18),t(5,"h3"),c(6,"Error Loading Observations"),o(),t(7,"p"),c(8),o(),t(9,"ion-button",19),_("click",function(){h(e);let d=m();return x(d.loadObservations())}),g(10,"ion-icon",20),c(11," Try Again "),o()()()()()}if(n&2){let e=m();r(8),v(e.error)}}function se(n,a){n&1&&(t(0,"div",21)(1,"ion-card",22)(2,"ion-card-content")(3,"div",23),g(4,"ion-icon",24),t(5,"h2"),c(6,"No Observations Yet"),o(),t(7,"p"),c(8,"Start identifying plants to build your observation collection!"),o(),t(9,"ion-button",25),g(10,"ion-icon",26),c(11," Identify Your First Plant "),o()()()()())}function le(n,a){n&1&&(t(0,"div",41),g(1,"ion-icon",46),t(2,"span"),c(3,"Location recorded"),o()())}function ge(n,a){if(n&1&&(t(0,"ion-chip",50),c(1),o()),n&2){let e=a.$implicit;r(),b(" ",e," ")}}function de(n,a){if(n&1&&(t(0,"span",51),c(1),o()),n&2){let e=m(2).$implicit;r(),b(" +",e.tags.length-3," more ")}}function me(n,a){if(n&1&&(t(0,"div",47),p(1,ge,2,1,"ion-chip",48)(2,de,2,1,"span",49),o()),n&2){let e=m().$implicit;r(),s("ngForOf",e.tags.slice(0,3)),r(),s("ngIf",e.tags.length>3)}}function pe(n,a){if(n&1&&(t(0,"div",52)(1,"p"),c(2),o()()),n&2){let e=m().$implicit;r(2),v(e.notes.length>100?e.notes.substring(0,100)+"...":e.notes)}}function ve(n,a){if(n&1&&(t(0,"ion-card",31)(1,"div",32),g(2,"ion-img",33),t(3,"div",34)(4,"ion-chip",35),c(5),o()(),t(6,"div",36),g(7,"ion-icon",37),o()(),t(8,"ion-card-content")(9,"div",38)(10,"h3"),c(11),o(),t(12,"p",39),c(13),o()(),t(14,"div",40)(15,"div",41),g(16,"ion-icon",42),t(17,"span"),c(18),o()(),p(19,le,4,0,"div",43),o(),p(20,me,3,2,"div",44)(21,pe,3,1,"div",45),o()()),n&2){let e=a.$implicit,i=m(2);s("routerLink",y(12,re,e.id)),r(2),s("src",e.image&&e.image.url||e.image&&e.image.thumbnailUrl||"/assets/placeholder-plant.jpg")("alt",e.species.name),r(2),s("color",i.getConfidenceColor(e.species.confidence)),r(),b(" ",e.species.confidence,"% "),r(2),s("name",i.getPlantPartIcon(e.plantPart)),r(4),v(e.species.name),r(2),v(e.species.scientificName),r(5),v(i.formatDate(e.createdAt)),r(),s("ngIf",e.location),r(),s("ngIf",e.tags&&e.tags.length>0),r(),s("ngIf",e.notes)}}function _e(n,a){if(n&1&&(t(0,"div",27)(1,"div",28)(2,"h3"),c(3),o()(),t(4,"div",29),p(5,ve,22,14,"ion-card",30),o()()),n&2){let e=m();r(3),M("",e.observations.length," Observation",e.observations.length!==1?"s":"",""),r(2),s("ngForOf",e.observations)("ngForTrackBy",e.trackByObservationId)}}var ye=(()=>{let a=class a{constructor(i){this.observationService=i,this.observations=[],this.isLoading=!1,this.error=null,this.subscriptions=[],q({construct:Q,leaf:Z,location:ee,calendar:J,eye:W,add:G,refresh:ne,filter:X,search:te,camera:K})}ngOnInit(){this.loadObservations();let i=this.observationService.userObservations$.subscribe(l=>{this.observations=l}),d=this.observationService.loadingState$.subscribe(l=>{this.isLoading=l.loading,this.error=l.error});this.subscriptions.push(i,d)}ngOnDestroy(){this.subscriptions.forEach(i=>i.unsubscribe())}loadObservations(){return O(this,null,function*(){yield this.observationService.loadUserObservations()})}refreshObservations(i){return O(this,null,function*(){yield this.loadObservations(),i.target.complete()})}getConfidenceColor(i){return i>=80?"success":i>=60?"warning":"danger"}formatDate(i){return i?(i instanceof Date?i:i.toDate()).toLocaleDateString():""}getPlantPartIcon(i){return{leaf:"leaf",flower:"flower",fruit:"nutrition",bark:"grid",habit:"tree",other:"help"}[i]||"help"}trackByObservationId(i,d){return d.id}};a.\u0275fac=function(d){return new(d||a)(C(oe))},a.\u0275cmp=P({type:a,selectors:[["app-observations-list"]],decls:16,vars:6,consts:[[3,"translucent"],["slot","end","fill","clear",3,"click"],["name","refresh"],[3,"fullscreen"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","pullingText","Pull to refresh","refreshingSpinner","crescent","refreshingText","Refreshing..."],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","empty-container",4,"ngIf"],["class","observations-container",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed"],["routerLink","/tabs/identify"],["name","add"],[1,"loading-container"],["name","crescent","color","primary"],[1,"error-container"],[1,"error-card"],[1,"error-content"],["name","warning","color","danger"],["expand","block",3,"click"],["name","refresh","slot","start"],[1,"empty-container"],[1,"empty-card"],[1,"empty-content"],["name","leaf","size","large","color","medium"],["expand","block","routerLink","/tabs/identify"],["name","camera","slot","start"],[1,"observations-container"],[1,"observations-header"],[1,"observations-grid"],["class","observation-card",3,"routerLink",4,"ngFor","ngForOf","ngForTrackBy"],[1,"observation-card",3,"routerLink"],[1,"observation-image"],[3,"src","alt"],[1,"confidence-badge"],[3,"color"],[1,"plant-part-icon"],[3,"name"],[1,"observation-header"],[1,"scientific-name"],[1,"observation-meta"],[1,"meta-item"],["name","calendar"],["class","meta-item",4,"ngIf"],["class","observation-tags",4,"ngIf"],["class","observation-notes",4,"ngIf"],["name","location"],[1,"observation-tags"],["color","tertiary","size","small",4,"ngFor","ngForOf"],["class","more-tags",4,"ngIf"],["color","tertiary","size","small"],[1,"more-tags"],[1,"observation-notes"]],template:function(d,l){d&1&&(t(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),c(3,"My Observations"),o(),t(4,"ion-button",1),_("click",function(f){return l.refreshObservations(f)}),g(5,"ion-icon",2),o()()(),t(6,"ion-content",3)(7,"ion-refresher",4),_("ionRefresh",function(f){return l.refreshObservations(f)}),g(8,"ion-refresher-content",5),o(),p(9,ae,4,0,"div",6)(10,ce,12,1,"div",7)(11,se,12,0,"div",8)(12,_e,6,4,"div",9),t(13,"ion-fab",10)(14,"ion-fab-button",11),g(15,"ion-icon",12),o()()()),d&2&&(s("translucent",!0),r(6),s("fullscreen",!0),r(3),s("ngIf",l.isLoading&&l.observations.length===0),r(),s("ngIf",l.error&&l.observations.length===0),r(),s("ngIf",!l.isLoading&&!l.error&&l.observations.length===0),r(),s("ngIf",l.observations.length>0))},dependencies:[w,I,S,E,k,D,$,A,U,T,z,H,L,F,V,N,Y,R,j,B],styles:[".loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:60vh;padding:40px 20px}.loading-container[_ngcontent-%COMP%]{text-align:center}.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-bottom:20px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:1.1rem}.error-card[_ngcontent-%COMP%], .empty-card[_ngcontent-%COMP%]{border-radius:16px;box-shadow:0 4px 12px #0000001a;max-width:400px;width:100%}.error-content[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]{text-align:center;padding:20px}.error-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-bottom:20px}.error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 12px;font-weight:600;color:var(--ion-color-dark)}.error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 24px;color:var(--ion-color-medium);line-height:1.5}.error-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .empty-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 12px;height:48px;font-weight:500}.observations-container[_ngcontent-%COMP%]{padding:20px 20px 100px}.observations-header[_ngcontent-%COMP%]{margin-bottom:20px}.observations-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.3rem;font-weight:600;color:var(--ion-color-dark)}.observations-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:20px}.observation-card[_ngcontent-%COMP%]{border-radius:16px;overflow:hidden;box-shadow:0 4px 12px #0000001a;transition:all .3s ease;cursor:pointer;margin:0}.observation-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 20px #00000026}.observation-card[_ngcontent-%COMP%]:active{transform:translateY(-2px)}.observation-image[_ngcontent-%COMP%]{position:relative;height:200px;overflow:hidden}.observation-image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.observation-image[_ngcontent-%COMP%]   .confidence-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px}.observation-image[_ngcontent-%COMP%]   .confidence-badge[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{font-weight:600;font-size:.85rem}.observation-image[_ngcontent-%COMP%]   .plant-part-icon[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:#0009;border-radius:50%;width:36px;height:36px;display:flex;align-items:center;justify-content:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.observation-image[_ngcontent-%COMP%]   .plant-part-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff;font-size:1.2rem}.observation-header[_ngcontent-%COMP%]{margin-bottom:12px}.observation-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:1.2rem;font-weight:600;color:var(--ion-color-dark);line-height:1.3}.observation-header[_ngcontent-%COMP%]   .scientific-name[_ngcontent-%COMP%]{margin:0;font-style:italic;color:var(--ion-color-medium);font-size:.95rem}.observation-meta[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;margin-bottom:12px}.observation-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:.9rem;color:var(--ion-color-medium)}.observation-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1rem;color:var(--ion-color-medium)}.observation-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{flex:1}.observation-tags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:6px;align-items:center;margin-bottom:12px}.observation-tags[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{font-size:.8rem;height:24px}.observation-tags[_ngcontent-%COMP%]   .more-tags[_ngcontent-%COMP%]{font-size:.8rem;color:var(--ion-color-medium);font-weight:500}.observation-notes[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.9rem;color:var(--ion-color-dark);line-height:1.4}ion-fab[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), .3)}ion-fab[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]:hover{--background: var(--ion-color-primary-shade);transform:scale(1.05)}@media (prefers-color-scheme: dark){.error-card[_ngcontent-%COMP%], .empty-card[_ngcontent-%COMP%], .observation-card[_ngcontent-%COMP%]{background:var(--ion-color-dark)}.observation-card[_ngcontent-%COMP%]{box-shadow:0 4px 12px #0000004d}.observation-card[_ngcontent-%COMP%]:hover{box-shadow:0 8px 20px #0006}}@media (max-width: 768px){.observations-container[_ngcontent-%COMP%]{padding:16px 16px 80px}.observations-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.observation-image[_ngcontent-%COMP%]{height:180px}.observation-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]{font-size:.85rem}}@media (max-width: 480px){.loading-container[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]{padding:20px 16px}.observations-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem}.observation-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem}}"]});let n=a;return n})();export{ye as ObservationsListPage};
