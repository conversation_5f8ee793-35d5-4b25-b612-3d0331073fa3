<!DOCTYPE html>
<html lang="en" data-beasties-container>

<head>
  <meta charset="utf-8">
  <title>Ionic App</title>

  <base href="/">

  <meta name="color-scheme" content="light dark">
  <meta name="viewport" content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="format-detection" content="telephone=no">
  <meta name="msapplication-tap-highlight" content="no">

  <link rel="icon" type="image/png" href="assets/icon/favicon.png">

  <!-- add to homescreen for ios -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
<style>:root{--ion-color-primary:#0054e9;--ion-color-primary-rgb:0, 84, 233;--ion-color-primary-contrast:#fff;--ion-color-primary-contrast-rgb:255, 255, 255;--ion-color-primary-shade:#004acd;--ion-color-primary-tint:#1a65eb;--ion-color-secondary:#0163aa;--ion-color-secondary-rgb:1, 99, 170;--ion-color-secondary-contrast:#fff;--ion-color-secondary-contrast-rgb:255, 255, 255;--ion-color-secondary-shade:#015796;--ion-color-secondary-tint:#1a73b3;--ion-color-tertiary:#6030ff;--ion-color-tertiary-rgb:96, 48, 255;--ion-color-tertiary-contrast:#fff;--ion-color-tertiary-contrast-rgb:255, 255, 255;--ion-color-tertiary-shade:#542ae0;--ion-color-tertiary-tint:#7045ff;--ion-color-success:#2dd55b;--ion-color-success-rgb:45, 213, 91;--ion-color-success-contrast:#000;--ion-color-success-contrast-rgb:0, 0, 0;--ion-color-success-shade:#28bb50;--ion-color-success-tint:#42d96b;--ion-color-warning:#ffc409;--ion-color-warning-rgb:255, 196, 9;--ion-color-warning-contrast:#000;--ion-color-warning-contrast-rgb:0, 0, 0;--ion-color-warning-shade:#e0ac08;--ion-color-warning-tint:#ffca22;--ion-color-danger:#c5000f;--ion-color-danger-rgb:197, 0, 15;--ion-color-danger-contrast:#fff;--ion-color-danger-contrast-rgb:255, 255, 255;--ion-color-danger-shade:#ad000d;--ion-color-danger-tint:#cb1a27;--ion-color-light:#f4f5f8;--ion-color-light-rgb:244, 245, 248;--ion-color-light-contrast:#000;--ion-color-light-contrast-rgb:0, 0, 0;--ion-color-light-shade:#d7d8da;--ion-color-light-tint:#f5f6f9;--ion-color-medium:#636469;--ion-color-medium-rgb:99, 100, 105;--ion-color-medium-contrast:#fff;--ion-color-medium-contrast-rgb:255, 255, 255;--ion-color-medium-shade:#57585c;--ion-color-medium-tint:#737478;--ion-color-dark:#222428;--ion-color-dark-rgb:34, 36, 40;--ion-color-dark-contrast:#fff;--ion-color-dark-contrast-rgb:255, 255, 255;--ion-color-dark-shade:#1e2023;--ion-color-dark-tint:#383a3e}html{--ion-dynamic-font:-apple-system-body;--ion-font-family:var(--ion-default-font)}body{background:var(--ion-background-color);color:var(--ion-text-color)}@supports (padding-top: 20px){html{--ion-safe-area-top:var(--ion-statusbar-padding)}}@supports (padding-top: env(safe-area-inset-top)){html{--ion-safe-area-top:env(safe-area-inset-top);--ion-safe-area-bottom:env(safe-area-inset-bottom);--ion-safe-area-left:env(safe-area-inset-left);--ion-safe-area-right:env(safe-area-inset-right)}}*{box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none}html{width:100%;height:100%;-webkit-text-size-adjust:100%;text-size-adjust:100%}html:not(.hydrated) body{display:none}body{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin:0;padding:0;position:fixed;width:100%;max-width:100%;height:100%;max-height:100%;transform:translateZ(0);text-rendering:optimizeLegibility;overflow:hidden;touch-action:manipulation;-webkit-user-drag:none;-ms-content-zooming:none;word-wrap:break-word;overscroll-behavior-y:none;-webkit-text-size-adjust:none;text-size-adjust:none}html{font-family:var(--ion-font-family)}@supports (-webkit-touch-callout: none){html{font:var(--ion-dynamic-font, 16px var(--ion-font-family))}}@media (prefers-color-scheme: dark){:root{--ion-color-primary:#4d8dff;--ion-color-primary-rgb:77, 141, 255;--ion-color-primary-contrast:#000;--ion-color-primary-contrast-rgb:0, 0, 0;--ion-color-primary-shade:#447ce0;--ion-color-primary-tint:#5f98ff;--ion-color-secondary:#46b1ff;--ion-color-secondary-rgb:70, 177, 255;--ion-color-secondary-contrast:#000;--ion-color-secondary-contrast-rgb:0, 0, 0;--ion-color-secondary-shade:#3e9ce0;--ion-color-secondary-tint:#59b9ff;--ion-color-tertiary:#8482fb;--ion-color-tertiary-rgb:132, 130, 251;--ion-color-tertiary-contrast:#000;--ion-color-tertiary-contrast-rgb:0, 0, 0;--ion-color-tertiary-shade:#7472dd;--ion-color-tertiary-tint:#908ffb;--ion-color-success:#2dd55b;--ion-color-success-rgb:45, 213, 91;--ion-color-success-contrast:#000;--ion-color-success-contrast-rgb:0, 0, 0;--ion-color-success-shade:#28bb50;--ion-color-success-tint:#42d96b;--ion-color-warning:#ffce31;--ion-color-warning-rgb:255, 206, 49;--ion-color-warning-contrast:#000;--ion-color-warning-contrast-rgb:0, 0, 0;--ion-color-warning-shade:#e0b52b;--ion-color-warning-tint:#ffd346;--ion-color-danger:#f24c58;--ion-color-danger-rgb:242, 76, 88;--ion-color-danger-contrast:#000;--ion-color-danger-contrast-rgb:0, 0, 0;--ion-color-danger-shade:#d5434d;--ion-color-danger-tint:#f35e69;--ion-color-light:#222428;--ion-color-light-rgb:34, 36, 40;--ion-color-light-contrast:#fff;--ion-color-light-contrast-rgb:255, 255, 255;--ion-color-light-shade:#1e2023;--ion-color-light-tint:#383a3e;--ion-color-medium:#989aa2;--ion-color-medium-rgb:152, 154, 162;--ion-color-medium-contrast:#000;--ion-color-medium-contrast-rgb:0, 0, 0;--ion-color-medium-shade:#86888f;--ion-color-medium-tint:#a2a4ab;--ion-color-dark:#f4f5f8;--ion-color-dark-rgb:244, 245, 248;--ion-color-dark-contrast:#000;--ion-color-dark-contrast-rgb:0, 0, 0;--ion-color-dark-shade:#d7d8da;--ion-color-dark-tint:#f5f6f9}}:root{--ion-color-primary:#2d5a27;--ion-color-primary-rgb:45, 90, 39;--ion-color-primary-contrast:#ffffff;--ion-color-primary-contrast-rgb:255, 255, 255;--ion-color-primary-shade:#284f22;--ion-color-primary-tint:#42693d;--ion-color-secondary:#4caf50;--ion-color-secondary-rgb:76, 175, 80;--ion-color-secondary-contrast:#ffffff;--ion-color-secondary-contrast-rgb:255, 255, 255;--ion-color-secondary-shade:#439a46;--ion-color-secondary-tint:#5eb862;--ion-color-tertiary:#8bc34a;--ion-color-tertiary-rgb:139, 195, 74;--ion-color-tertiary-contrast:#000000;--ion-color-tertiary-contrast-rgb:0, 0, 0;--ion-color-tertiary-shade:#7aac41;--ion-color-tertiary-tint:#97c95c;--ion-color-success:#66bb6a;--ion-color-success-rgb:102, 187, 106;--ion-color-success-contrast:#ffffff;--ion-color-success-contrast-rgb:255, 255, 255;--ion-color-success-shade:#5aa45d;--ion-color-success-tint:#75c279;--ion-color-warning:#ffc107;--ion-color-warning-rgb:255, 193, 7;--ion-color-warning-contrast:#000000;--ion-color-warning-contrast-rgb:0, 0, 0;--ion-color-warning-shade:#e0aa06;--ion-color-warning-tint:#ffca20;--ion-color-danger:#e57373;--ion-color-danger-rgb:229, 115, 115;--ion-color-danger-contrast:#ffffff;--ion-color-danger-contrast-rgb:255, 255, 255;--ion-color-danger-shade:#ca6565;--ion-color-danger-tint:#e88181;--ion-color-dark:#1b3b17;--ion-color-dark-rgb:27, 59, 23;--ion-color-dark-contrast:#ffffff;--ion-color-dark-contrast-rgb:255, 255, 255;--ion-color-dark-shade:#183315;--ion-color-dark-tint:#324f2e;--ion-color-medium:#81c784;--ion-color-medium-rgb:129, 199, 132;--ion-color-medium-contrast:#000000;--ion-color-medium-contrast-rgb:0, 0, 0;--ion-color-medium-shade:#72af74;--ion-color-medium-tint:#8ecc90;--ion-color-light:#f8fdf8;--ion-color-light-rgb:248, 253, 248;--ion-color-light-contrast:#000000;--ion-color-light-contrast-rgb:0, 0, 0;--ion-color-light-shade:#dadeda;--ion-color-light-tint:#f9fdf9;--ion-color-leaf:#689f38;--ion-color-leaf-rgb:104, 159, 56;--ion-color-leaf-contrast:#ffffff;--ion-color-leaf-contrast-rgb:255, 255, 255;--ion-color-leaf-shade:#5c8c31;--ion-color-leaf-tint:#77a84c;--ion-color-mint:#a5d6a7;--ion-color-mint-rgb:165, 214, 167;--ion-color-mint-contrast:#000000;--ion-color-mint-contrast-rgb:0, 0, 0;--ion-color-mint-shade:#91bc93;--ion-color-mint-tint:#aedab0;--ion-color-forest:#2e7d32;--ion-color-forest-rgb:46, 125, 50;--ion-color-forest-contrast:#ffffff;--ion-color-forest-contrast-rgb:255, 255, 255;--ion-color-forest-shade:#286e2c;--ion-color-forest-tint:#438a47;--ion-background-color:#fafffe;--ion-background-color-rgb:250, 255, 254;--ion-text-color:#1a1a1a;--ion-text-color-rgb:26, 26, 26;--ion-card-background:#ffffff;--ion-item-background:#ffffff;--ion-toolbar-background:#ffffff;--ion-toolbar-border-color:#e8f5e8;--ion-tab-bar-background:#ffffff;--ion-tab-bar-border-color:#e8f5e8;--ion-color-step-50:#f8fdf8;--ion-color-step-100:#f1faf1;--ion-color-step-150:#e8f5e8;--ion-color-step-200:#d4edda;--ion-color-step-250:#c3e6cb;--ion-color-step-300:#b1dfbb;--ion-color-step-350:#9fd8ab;--ion-color-step-400:#8dd19b;--ion-color-step-450:#7bca8b;--ion-color-step-500:#69c37b;--ion-color-step-550:#57bc6b;--ion-color-step-600:#45b55b;--ion-color-step-650:#33ae4b;--ion-color-step-700:#21a73b;--ion-color-step-750:#0fa02b;--ion-color-step-800:#0d8a25;--ion-color-step-850:#0b741f;--ion-color-step-900:#095e19;--ion-color-step-950:#074813}</style><link rel="stylesheet" href="styles-JBYDEUBV.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="styles-JBYDEUBV.css"></noscript></head>

<body>
  <app-root></app-root>
<link rel="modulepreload" href="chunk-XFCVXJNA.js"><link rel="modulepreload" href="chunk-JBBAVVC3.js"><link rel="modulepreload" href="chunk-UUEAGYFQ.js"><link rel="modulepreload" href="chunk-N2M46O22.js"><link rel="modulepreload" href="chunk-AZEIYKMX.js"><link rel="modulepreload" href="chunk-MYOBYLQN.js"><link rel="modulepreload" href="chunk-64QR6YHT.js"><link rel="modulepreload" href="chunk-6DBQW3S3.js"><link rel="modulepreload" href="chunk-MMQ4D4FU.js"><link rel="modulepreload" href="chunk-JZIOZHPP.js"><script src="polyfills-RQ52UMDF.js" type="module"></script><script src="main-ALWVTITI.js" type="module"></script></body>

</html>
