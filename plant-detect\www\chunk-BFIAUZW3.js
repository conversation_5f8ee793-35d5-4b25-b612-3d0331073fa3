import{a as De}from"./chunk-NTAPKKHP.js";import{B as Le,C as Fe,F as Ne,a as Me,c as Oe,f as Se,g as Ie,i as Ee,p as ke,r as Te,t as Re,v as Ae}from"./chunk-TV52TPWK.js";import{a as U,b as j,c as Ue}from"./chunk-6S6VJZAU.js";import{a as be,d as we}from"./chunk-2DZQVLWT.js";import{a as k}from"./chunk-JBBAVVC3.js";import{b as D}from"./chunk-ZBJDNBYI.js";import"./chunk-YUFJ6257.js";import{Ba as Z,C as l,D as w,E as $,F as v,G as m,H as E,I as r,Ia as ee,J as s,K as h,Ka as te,L as S,La as ne,M as C,Ma as ie,N as u,Na as oe,O as c,Oa as ae,P as I,Q as b,Qa as re,S as L,T as F,U as N,Ua as se,Va as ce,Wa as le,Xa as de,Za as pe,b as V,ba as G,bb as ge,ca as Y,cb as me,ga as J,gb as ue,hb as fe,ia as q,kb as he,lb as _e,m as W,mb as Pe,na as K,nb as xe,o as A,ob as ve,pb as ye,rb as Ce,ua as Q,v as P,w as x,wa as X}from"./chunk-DVLCYDDQ.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{g}from"./chunk-OLRFWS6T.js";var M=function(i){return i.Prompt="PROMPT",i.Camera="CAMERA",i.Photos="PHOTOS",i}(M||{}),T=function(i){return i.Rear="REAR",i.Front="FRONT",i}(T||{}),B=function(i){return i.Uri="uri",i.Base64="base64",i.DataUrl="dataUrl",i}(B||{});var R=class extends Ue{getPhoto(a){return g(this,null,function*(){return new Promise((n,e)=>g(this,null,function*(){if(a.webUseInput||a.source===M.Photos)this.fileInputExperience(a,n,e);else if(a.source===M.Prompt){let t=document.querySelector("pwa-action-sheet");t||(t=document.createElement("pwa-action-sheet"),document.body.appendChild(t)),t.header=a.promptLabelHeader||"Photo",t.cancelable=!1,t.options=[{title:a.promptLabelPhoto||"From Photos"},{title:a.promptLabelPicture||"Take Picture"}],t.addEventListener("onSelection",o=>g(this,null,function*(){o.detail===0?this.fileInputExperience(a,n,e):this.cameraExperience(a,n,e)}))}else this.cameraExperience(a,n,e)}))})}pickImages(a){return g(this,null,function*(){return new Promise((n,e)=>g(this,null,function*(){this.multipleFileInputExperience(n,e)}))})}cameraExperience(a,n,e){return g(this,null,function*(){if(customElements.get("pwa-camera-modal")){let t=document.createElement("pwa-camera-modal");t.facingMode=a.direction===T.Front?"user":"environment",document.body.appendChild(t);try{yield t.componentOnReady(),t.addEventListener("onPhoto",o=>g(this,null,function*(){let d=o.detail;d===null?e(new U("User cancelled photos app")):d instanceof Error?e(d):n(yield this._getCameraPhoto(d,a)),t.dismiss(),document.body.removeChild(t)})),t.present()}catch{this.fileInputExperience(a,n,e)}}else console.error("Unable to load PWA Element 'pwa-camera-modal'. See the docs: https://capacitorjs.com/docs/web/pwa-elements."),this.fileInputExperience(a,n,e)})}fileInputExperience(a,n,e){let t=document.querySelector("#_capacitor-camera-input"),o=()=>{var d;(d=t.parentNode)===null||d===void 0||d.removeChild(t)};t||(t=document.createElement("input"),t.id="_capacitor-camera-input",t.type="file",t.hidden=!0,document.body.appendChild(t),t.addEventListener("change",d=>{let p=t.files[0],f="jpeg";if(p.type==="image/png"?f="png":p.type==="image/gif"&&(f="gif"),a.resultType==="dataUrl"||a.resultType==="base64"){let _=new FileReader;_.addEventListener("load",()=>{if(a.resultType==="dataUrl")n({dataUrl:_.result,format:f});else if(a.resultType==="base64"){let O=_.result.split(",")[1];n({base64String:O,format:f})}o()}),_.readAsDataURL(p)}else n({webPath:URL.createObjectURL(p),format:f}),o()}),t.addEventListener("cancel",d=>{e(new U("User cancelled photos app")),o()})),t.accept="image/*",t.capture=!0,a.source===M.Photos||a.source===M.Prompt?t.removeAttribute("capture"):a.direction===T.Front?t.capture="user":a.direction===T.Rear&&(t.capture="environment"),t.click()}multipleFileInputExperience(a,n){let e=document.querySelector("#_capacitor-camera-input-multiple"),t=()=>{var o;(o=e.parentNode)===null||o===void 0||o.removeChild(e)};e||(e=document.createElement("input"),e.id="_capacitor-camera-input-multiple",e.type="file",e.hidden=!0,e.multiple=!0,document.body.appendChild(e),e.addEventListener("change",o=>{let d=[];for(let p=0;p<e.files.length;p++){let f=e.files[p],_="jpeg";f.type==="image/png"?_="png":f.type==="image/gif"&&(_="gif"),d.push({webPath:URL.createObjectURL(f),format:_})}a({photos:d}),t()}),e.addEventListener("cancel",o=>{n(new U("User cancelled photos app")),t()})),e.accept="image/*",e.click()}_getCameraPhoto(a,n){return new Promise((e,t)=>{let o=new FileReader,d=a.type.split("/")[1];n.resultType==="uri"?e({webPath:URL.createObjectURL(a),format:d,saved:!1}):(o.readAsDataURL(a),o.onloadend=()=>{let p=o.result;n.resultType==="dataUrl"?e({dataUrl:p,format:d,saved:!1}):e({base64String:p.split(",")[1],format:d,saved:!1})},o.onerror=p=>{t(p)})})}checkPermissions(){return g(this,null,function*(){if(typeof navigator>"u"||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");try{return{camera:(yield window.navigator.permissions.query({name:"camera"})).state,photos:"granted"}}catch{throw this.unavailable("Camera permissions are not available in this browser")}})}requestPermissions(){return g(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}pickLimitedLibraryPhotos(){return g(this,null,function*(){throw this.unavailable("Not implemented on web.")})}getLimitedLibraryPhotos(){return g(this,null,function*(){throw this.unavailable("Not implemented on web.")})}},Pt=new R;var je=j("Camera",{web:()=>new R});function We(i){i.CapacitorUtils.Synapse=new Proxy({},{get(a,n){return new Proxy({},{get(e,t){return(o,d,p)=>{let f=i.Capacitor.Plugins[n];if(f===void 0){p(new Error(`Capacitor plugin ${n} not found`));return}if(typeof f[t]!="function"){p(new Error(`Method ${t} not found in Capacitor plugin ${n}`));return}g(null,null,function*(){try{let _=yield f[t](o);d(_)}catch(_){p(_)}})}}})}})}function $e(i){i.CapacitorUtils.Synapse=new Proxy({},{get(a,n){return i.cordova.plugins[n]}})}function Ve(i=!1){window.CapacitorUtils=window.CapacitorUtils||{},window.Capacitor!==void 0&&!i?We(window):window.cordova!==void 0&&$e(window)}var Be=j("Geolocation",{web:()=>import("./chunk-TMW6BMAF.js").then(i=>new i.GeolocationWeb)});Ve();var ze=[{id:"global",name:"Global Flora",description:"Worldwide plant species database",countries:[],regions:[],active:!0,createdAt:new Date,updatedAt:new Date},{id:"asia",name:"Asian Flora",description:"Plant species native to Asia",countries:["CN","IN","JP","KR","TH","VN","MY","ID","PH","LK"],regions:["East Asia","Southeast Asia","South Asia"],active:!0,characteristics:{climate:["tropical","subtropical","temperate"],biomes:["tropical rainforest","deciduous forest","grassland","mountain"]},createdAt:new Date,updatedAt:new Date},{id:"europe",name:"European Flora",description:"Plant species native to Europe",countries:["DE","FR","IT","ES","GB","NL","BE","AT","CH","SE","NO","DK"],regions:["Northern Europe","Western Europe","Southern Europe","Eastern Europe"],active:!0,characteristics:{climate:["temperate","mediterranean","continental"],biomes:["deciduous forest","coniferous forest","grassland","mediterranean scrub"]},createdAt:new Date,updatedAt:new Date},{id:"north-america",name:"North American Flora",description:"Plant species native to North America",countries:["US","CA","MX"],regions:["United States","Canada","Mexico"],active:!0,characteristics:{climate:["temperate","subtropical","tropical","arctic"],biomes:["deciduous forest","coniferous forest","grassland","desert","tundra"]},createdAt:new Date,updatedAt:new Date},{id:"tropical",name:"Tropical Flora",description:"Plant species from tropical regions worldwide",countries:["BR","CO","PE","EC","VE","GY","SR","GF","BO","PY"],regions:["Amazon Basin","Central America","Caribbean","Tropical Africa","Southeast Asia"],active:!0,characteristics:{climate:["tropical"],biomes:["tropical rainforest","tropical dry forest","mangrove"]},createdAt:new Date,updatedAt:new Date}];var He=(()=>{let a=class a{constructor(e,t,o){this.http=e,this.authService=t,this.functions=o,this.PLANTNET_API_URL=k.production?"https://my-api.plantnet.org/v1/identify":"/api/plantnet/v1/identify",this.SUPPORTED_PROJECTS=["k-world-flora","weurope","useful","australia"],this.loadingStateSubject=new V({loading:!1,error:null,lastUpdated:null}),this.loadingState$=this.loadingStateSubject.asObservable(),this.identificationHistorySubject=new V([]),this.identificationHistory$=this.identificationHistorySubject.asObservable(),this.loadIdentificationHistory()}identifyPlant(e){return g(this,null,function*(){this.setLoadingState(!0,null);try{this.validateIdentificationRequest(e);let t;return k.demoMode?t=yield this.generateDemoResult(e):t=yield this.callPlantNetAPI(e),yield this.saveToHistory(e,t),this.setLoadingState(!1,null),t}catch(t){let o=this.getErrorMessage(t);throw this.setLoadingState(!1,o),new Error(o)}})}callPlantNetAPI(e){return g(this,null,function*(){var t;try{let d={imageBase64:yield this.convertImageToBase64(e.imageFile),plantPart:e.plantPart,floraRegion:e.floraRegion,location:e.location&&((t=e.modifiers)!=null&&t.useLocationContext)?e.location:null},_=(yield we(this.functions,"identifyPlant")(d)).data;if(!_||!_.success)throw new Error("No response from identification service");let O=_.data;return yield this.processIdentificationResponse(O,e)}catch(o){throw console.error("Firebase Functions error:",o),new Error(`Identification failed: ${o.message||"Unknown error"}`)}})}generateDemoResult(e){return g(this,null,function*(){var p;yield new Promise(f=>setTimeout(f,2e3));let t=this.getDemoSpeciesByPlantPart(e.plantPart),o=yield this.analyzeImage(e.imageFile);return{id:this.generateId(),species:t,processingTime:2e3,imageAnalysis:o,suggestions:this.generateSuggestions(t,o),metadata:{apiProvider:"PlantNet (Demo Mode)",modelVersion:"demo-1.0",processingRegion:"demo",requestId:this.generateId(),cached:!1,floraRegionUsed:e.floraRegion,locationContextUsed:!!e.location&&!!((p=e.modifiers)!=null&&p.useLocationContext)},createdAt:new Date}})}validateIdentificationRequest(e){if(!e.imageFile)throw new Error("Image file is required");if(!e.plantPart)throw new Error("Plant part selection is required");if(!e.floraRegion)throw new Error("Flora region selection is required");let t=10*1024*1024;if(e.imageFile.size>t)throw new Error("Image file is too large. Maximum size is 10MB.");if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(e.imageFile.type))throw new Error("Invalid image format. Please use JPEG, PNG, or WebP.")}convertImageToBase64(e){return g(this,null,function*(){return new Promise((t,o)=>{let d=new FileReader;d.onload=()=>{let p=d.result;t(p.split(",")[1])},d.onerror=o,d.readAsDataURL(e)})})}getProjectFromFloraRegion(e){return{global:"k-world-flora",europe:"weurope","north-america":"useful",australia:"australia",asia:"k-world-flora",tropical:"k-world-flora"}[e]||"k-world-flora"}processIdentificationResponse(e,t){return g(this,null,function*(){var O;let o=Date.now(),d=e.results.map(y=>{var z,H;return{name:y.species.commonNames&&y.species.commonNames.length>0?y.species.commonNames[0]:y.species.scientificNameWithoutAuthor,scientificName:y.species.scientificNameWithoutAuthor,confidence:Math.round(y.score*100),taxonomy:{kingdom:"Plantae",phylum:"Tracheophyta",class:"Magnoliopsida",order:"Unknown",family:((z=y.species.family)==null?void 0:z.scientificNameWithoutAuthor)||"Unknown",genus:((H=y.species.genus)==null?void 0:H.scientificNameWithoutAuthor)||"Unknown",species:y.species.scientificNameWithoutAuthor},commonNames:y.species.commonNames||[],description:this.generateSpeciesDescription(y),habitat:this.generateHabitatInfo(y),conservationStatus:"Unknown"}}),p=yield this.analyzeImage(t.imageFile),f=Date.now()-o;return{id:this.generateId(),species:d,processingTime:f,imageAnalysis:p,suggestions:this.generateSuggestions(d,p),metadata:{apiProvider:"PlantNet",modelVersion:e.version||"1.0",processingRegion:"global",requestId:this.generateId(),cached:!1,floraRegionUsed:t.floraRegion,locationContextUsed:!!t.location&&!!((O=t.modifiers)!=null&&O.useLocationContext)},createdAt:new Date}})}generateSpeciesDescription(e){var d,p;let t=(d=e.species.family)==null?void 0:d.scientificNameWithoutAuthor,o=(p=e.species.genus)==null?void 0:p.scientificNameWithoutAuthor;return t&&o?`A plant species belonging to the ${t} family, genus ${o}.`:t?`A plant species from the ${t} family.`:"Plant species information available."}generateHabitatInfo(e){var d;let t=(d=e.species.family)==null?void 0:d.scientificNameWithoutAuthor;return{Rosaceae:"Temperate regions, gardens, woodlands",Asteraceae:"Diverse habitats worldwide",Fabaceae:"Various habitats, often nitrogen-fixing",Poaceae:"Grasslands, meadows, cultivated areas",Lamiaceae:"Mediterranean climates, herb gardens",Brassicaceae:"Temperate regions, disturbed soils"}[t||""]||"Various natural habitats"}getDemoSpeciesByPlantPart(e){let t={leaf:[{name:"Common Oak",scientificName:"Quercus robur",confidence:92,taxonomy:{kingdom:"Plantae",phylum:"Tracheophyta",class:"Magnoliopsida",order:"Fagales",family:"Fagaceae",genus:"Quercus",species:"Quercus robur"},commonNames:["English Oak","Pedunculate Oak"],description:"A large deciduous tree native to Europe, known for its distinctive lobed leaves and acorns.",habitat:"Temperate forests, parks, and woodlands",conservationStatus:"Least Concern"},{name:"Silver Maple",scientificName:"Acer saccharinum",confidence:78,taxonomy:{kingdom:"Plantae",phylum:"Tracheophyta",class:"Magnoliopsida",order:"Sapindales",family:"Sapindaceae",genus:"Acer",species:"Acer saccharinum"},commonNames:["Silver Maple","Soft Maple"],description:"A fast-growing deciduous tree with distinctive silver-backed leaves.",habitat:"Wetlands, floodplains, and urban areas",conservationStatus:"Least Concern"}],flower:[{name:"Common Daisy",scientificName:"Bellis perennis",confidence:89,taxonomy:{kingdom:"Plantae",phylum:"Tracheophyta",class:"Magnoliopsida",order:"Asterales",family:"Asteraceae",genus:"Bellis",species:"Bellis perennis"},commonNames:["English Daisy","Lawn Daisy"],description:"A small perennial flowering plant with white petals and yellow center.",habitat:"Grasslands, lawns, and meadows",conservationStatus:"Least Concern"},{name:"Wild Rose",scientificName:"Rosa canina",confidence:85,taxonomy:{kingdom:"Plantae",phylum:"Tracheophyta",class:"Magnoliopsida",order:"Rosales",family:"Rosaceae",genus:"Rosa",species:"Rosa canina"},commonNames:["Dog Rose","Wild Briar"],description:"A climbing wild rose with pink or white flowers and red hips.",habitat:"Hedgerows, woodland edges, and scrubland",conservationStatus:"Least Concern"}],fruit:[{name:"Apple",scientificName:"Malus domestica",confidence:94,taxonomy:{kingdom:"Plantae",phylum:"Tracheophyta",class:"Magnoliopsida",order:"Rosales",family:"Rosaceae",genus:"Malus",species:"Malus domestica"},commonNames:["Domestic Apple","Orchard Apple"],description:"The common apple fruit from cultivated apple trees.",habitat:"Orchards, gardens, and cultivated areas",conservationStatus:"Cultivated"}],bark:[{name:"White Birch",scientificName:"Betula papyrifera",confidence:87,taxonomy:{kingdom:"Plantae",phylum:"Tracheophyta",class:"Magnoliopsida",order:"Fagales",family:"Betulaceae",genus:"Betula",species:"Betula papyrifera"},commonNames:["Paper Birch","Canoe Birch"],description:"A deciduous tree known for its distinctive white, papery bark.",habitat:"Northern forests and woodlands",conservationStatus:"Least Concern"}],habit:[{name:"Common Lavender",scientificName:"Lavandula angustifolia",confidence:91,taxonomy:{kingdom:"Plantae",phylum:"Tracheophyta",class:"Magnoliopsida",order:"Lamiales",family:"Lamiaceae",genus:"Lavandula",species:"Lavandula angustifolia"},commonNames:["English Lavender","True Lavender"],description:"An aromatic shrub with purple flower spikes and silvery foliage.",habitat:"Mediterranean climates, gardens, and hillsides",conservationStatus:"Least Concern"}],other:[{name:"Unknown Plant",scientificName:"Plantae species",confidence:45,taxonomy:{kingdom:"Plantae",phylum:"Unknown",class:"Unknown",order:"Unknown",family:"Unknown",genus:"Unknown",species:"Unknown"},commonNames:["Unidentified Plant"],description:"Plant identification requires more specific features.",habitat:"Various habitats",conservationStatus:"Unknown"}]};return t[e]||t.other}analyzeImage(e){return g(this,null,function*(){return new Promise(t=>{let o=new Image;o.onload=()=>{let d={quality:{overall:85,sharpness:80,lighting:90,contrast:85,noise:10,resolution:o.width*o.height>1e6?95:70},plantPartDetected:["leaf"],features:[{type:"leaf_shape",confidence:.8,description:"Oval-shaped leaves detected"}],colors:{dominant:["#228B22","#32CD32","#006400"],palette:[{hex:"#228B22",rgb:[34,139,34],percentage:45},{hex:"#32CD32",rgb:[50,205,50],percentage:30},{hex:"#006400",rgb:[0,100,0],percentage:25}],distribution:{green:70,brown:15,red:5,yellow:5,purple:2,white:2,other:1}},composition:{plantCoverage:75,backgroundType:"natural",multipleSpecies:!1,partialView:!1}};t(d)},o.src=URL.createObjectURL(e)})})}generateSuggestions(e,t){let o=[];return t.quality.overall<70&&o.push({type:"image_quality",message:"Consider taking a clearer photo for better identification accuracy",priority:"high",actionable:!0}),e.length>0&&e[0].confidence<70&&o.push({type:"additional_photos",message:"Try taking photos of different plant parts (flowers, leaves, bark) for better accuracy",priority:"medium",actionable:!0}),o}saveToHistory(e,t){return g(this,null,function*(){let o=this.authService.getCurrentUser();if(!o)return;let d={id:this.generateId(),userId:o.uid,request:e,result:t,savedAsObservation:!1,createdAt:new Date},p=this.identificationHistorySubject.value,f=[d,...p].slice(0,50);this.identificationHistorySubject.next(f),localStorage.setItem("identification_history",JSON.stringify(f))})}loadIdentificationHistory(){try{let e=localStorage.getItem("identification_history");if(e){let t=JSON.parse(e);this.identificationHistorySubject.next(t)}}catch(e){console.error("Error loading identification history:",e)}}clearHistory(){this.identificationHistorySubject.next([]),localStorage.removeItem("identification_history")}getHistoryItem(e){return this.identificationHistorySubject.value.find(o=>o.id===e)||null}setLoadingState(e,t){this.loadingStateSubject.next({loading:e,error:t,lastUpdated:e?null:new Date})}getErrorMessage(e){var t;return console.error("PlantNet API Error:",e),e.status===429?"Rate limit exceeded. You have reached the daily limit for plant identifications. Please try again tomorrow.":e.status===401||e.status===403?"Invalid API key or access denied. Please check your PlantNet API configuration.":e.status===400?"Invalid request. Please check your image format and try again.":e.status===404?"PlantNet service not found. Please try again later.":e.status===500?"PlantNet server error. Please try again later.":e.status===0?"Network error. Please check your internet connection and try again.":(t=e.error)!=null&&t.message?`PlantNet API Error: ${e.error.message}`:e.message?e.message:"An unexpected error occurred during plant identification. Please try again."}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}};a.\u0275fac=function(t){return new(t||a)(A(q),A(D),A(be))},a.\u0275prov=W({token:a,factory:a.\u0275fac,providedIn:"root"});let i=a;return i})();function qe(i,a){if(i&1){let n=S();r(0,"ion-button",9),C("click",function(){P(n);let t=u();return x(t.resetIdentification())}),h(1,"ion-icon",10),s()}}function Ke(i,a){i&1&&(r(0,"div",11)(1,"ion-chip",12),h(2,"ion-icon",13),r(3,"ion-label"),c(4,"Demo Mode - Using sample data"),s()()())}function Qe(i,a){if(i&1){let n=S();r(0,"div",14)(1,"div",15)(2,"div",16),h(3,"ion-icon",17),r(4,"h2"),c(5,"Take a Photo"),s(),r(6,"p"),c(7,"Capture a clear image of the plant you want to identify"),s()(),r(8,"div",18)(9,"ion-card")(10,"ion-card-header")(11,"ion-card-title"),c(12,"\u{1F4F8} Photography Tips"),s()(),r(13,"ion-card-content")(14,"ul")(15,"li"),c(16,"Ensure good lighting"),s(),r(17,"li"),c(18,"Fill the frame with the plant part"),s(),r(19,"li"),c(20,"Keep the image sharp and in focus"),s(),r(21,"li"),c(22,"Avoid shadows and reflections"),s()()()()(),r(23,"ion-button",19),C("click",function(){P(n);let t=u();return x(t.presentImageSourceActionSheet())}),h(24,"ion-icon",20),c(25," Take Photo "),s()()()}}function Xe(i,a){if(i&1&&(r(0,"ion-select-option",36),c(1),s()),i&2){let n=a.$implicit;m("value",n.value),l(),b(" ",n.label," ")}}function Ze(i,a){if(i&1&&(r(0,"ion-select-option",36),c(1),s()),i&2){let n=a.$implicit;m("value",n.id),l(),b(" ",n.name," ")}}function et(i,a){if(i&1){let n=S();r(0,"div",21)(1,"ion-card",22),h(2,"ion-img",23),r(3,"div",24)(4,"ion-button",25),C("click",function(){P(n);let t=u();return x(t.retakePhoto())}),h(5,"ion-icon",20),c(6," Retake "),s()()(),r(7,"ion-card",26)(8,"ion-card-header")(9,"ion-card-title"),c(10,"Identification Settings"),s()(),r(11,"ion-card-content")(12,"ion-item",27)(13,"ion-label",28),c(14,"Plant Part"),s(),r(15,"ion-select",29),N("ngModelChange",function(t){P(n);let o=u();return F(o.selectedPlantPart,t)||(o.selectedPlantPart=t),x(t)}),v(16,Xe,2,2,"ion-select-option",30),s()(),r(17,"ion-item",27)(18,"ion-label",28),c(19,"Flora Region"),s(),r(20,"ion-select",31),N("ngModelChange",function(t){P(n);let o=u();return F(o.selectedFloraRegion,t)||(o.selectedFloraRegion=t),x(t)}),v(21,Ze,2,2,"ion-select-option",30),s()(),r(22,"ion-item",27),h(23,"ion-icon",32),r(24,"ion-label")(25,"h3"),c(26,"Use Location"),s(),r(27,"p"),c(28,"Improve accuracy with location context"),s()(),r(29,"ion-checkbox",33),N("ngModelChange",function(t){P(n);let o=u();return F(o.useLocation,t)||(o.useLocation=t),x(t)}),s()(),r(30,"ion-button",34),C("click",function(){P(n);let t=u();return x(t.startIdentification())}),h(31,"ion-icon",35),c(32," Identify Plant "),s()()()()}if(i&2){let n=u();l(2),m("src",n.capturedImage)("alt","Captured plant image"),l(13),L("ngModel",n.selectedPlantPart),l(),m("ngForOf",n.plantParts),l(4),L("ngModel",n.selectedFloraRegion),l(),m("ngForOf",n.floraRegions),l(8),L("ngModel",n.useLocation),l(),m("disabled",!n.selectedPlantPart||!n.selectedFloraRegion)}}function tt(i,a){if(i&1&&(r(0,"div",37)(1,"div",38)(2,"div",39),h(3,"ion-spinner",40),r(4,"h2"),c(5,"Analyzing Plant"),s(),r(6,"p"),c(7,"Our AI is identifying your plant..."),s()(),r(8,"ion-card",41)(9,"ion-card-content")(10,"div",42),h(11,"ion-progress-bar",43),r(12,"div",44),c(13),s()(),r(14,"div",45)(15,"div",46),h(16,"ion-icon",47),r(17,"span"),c(18,"Analyzing image"),s()(),r(19,"div",46),h(20,"ion-icon",48),r(21,"span"),c(22,"Identifying features"),s()(),r(23,"div",46),h(24,"ion-icon",49),r(25,"span"),c(26,"Matching species"),s()(),r(27,"div",46),h(28,"ion-icon",50),r(29,"span"),c(30,"Complete"),s()()()()()()()),i&2){let n=u();l(11),m("value",n.processingProgress/100),l(2),b("",n.processingProgress,"%"),l(2),E("active",n.processingProgress>=20),l(4),E("active",n.processingProgress>=50),l(4),E("active",n.processingProgress>=80),l(4),E("active",n.processingProgress>=100)}}function nt(i,a){if(i&1&&(r(0,"div",73)(1,"span",74),c(2),s()()),i&2){let n=u().$implicit;l(2),I(n.taxonomy.family)}}function it(i,a){if(i&1&&(r(0,"p"),c(1),s()),i&2){let n=u(2).$implicit;l(),I(n.description)}}function ot(i,a){if(i&1&&(r(0,"p")(1,"strong"),c(2,"Habitat:"),s(),c(3),s()),i&2){let n=u(2).$implicit;l(3),b(" ",n.habitat,"")}}function at(i,a){if(i&1&&(r(0,"div",75),v(1,it,2,1,"p",52)(2,ot,4,1,"p",52),s()),i&2){let n=u().$implicit;l(),m("ngIf",n.description),l(),m("ngIf",n.habitat)}}function rt(i,a){if(i&1&&(r(0,"ion-card",65)(1,"ion-card-content")(2,"div",66)(3,"div",67)(4,"h4"),c(5),s(),r(6,"p",68),c(7),s(),v(8,nt,3,1,"div",69),s(),r(9,"div",70)(10,"ion-chip",71),c(11),s(),r(12,"small"),c(13),s()()(),v(14,at,3,2,"div",72),s()()),i&2){let n=a.$implicit,e=a.index,t=u(3);E("top-result",e===0),l(5),I(n.name),l(2),I(n.scientificName),l(),m("ngIf",n.taxonomy),l(2),m("color",t.getConfidenceColor(n.confidence)),l(),b(" ",n.confidence,"% "),l(2),I(t.getConfidenceText(n.confidence)),l(),m("ngIf",n.description||n.habitat)}}function st(i,a){if(i&1&&(r(0,"ion-chip",84),c(1),s()),i&2){let n=a.$implicit;l(),b(" ",n.description," ")}}function ct(i,a){if(i&1&&(r(0,"div",81)(1,"h5"),c(2,"Detected Features"),s(),r(3,"div",82),v(4,st,2,1,"ion-chip",83),s()()),i&2){let n=u(4);l(4),m("ngForOf",n.identificationResult.imageAnalysis.features)}}function lt(i,a){if(i&1&&(r(0,"ion-card",76)(1,"ion-card-header")(2,"ion-card-title"),c(3,"Analysis Details"),s()(),r(4,"ion-card-content")(5,"div",77)(6,"div",78)(7,"h5"),c(8,"Image Quality"),s(),h(9,"ion-progress-bar",43),r(10,"span"),c(11),s()(),r(12,"div",78)(13,"h5"),c(14,"Plant Coverage"),s(),h(15,"ion-progress-bar",79),r(16,"span"),c(17),s()()(),v(18,ct,5,1,"div",80),s()()),i&2){let n=u(3);l(9),m("value",n.identificationResult.imageAnalysis.quality.overall/100),l(2),b("",n.identificationResult.imageAnalysis.quality.overall,"%"),l(4),m("value",n.identificationResult.imageAnalysis.composition.plantCoverage/100),l(2),b("",n.identificationResult.imageAnalysis.composition.plantCoverage,"%"),l(),m("ngIf",n.identificationResult.imageAnalysis.features.length>0)}}function dt(i,a){if(i&1){let n=S();r(0,"div")(1,"ion-card",54),h(2,"ion-img",23),r(3,"div",55)(4,"ion-button",56),C("click",function(){P(n);let t=u(2);return x(t.saveAsObservation())}),h(5,"ion-icon",57),c(6," Save "),s(),r(7,"ion-button",58),C("click",function(){P(n);let t=u(2);return x(t.shareResults())}),h(8,"ion-icon",59),c(9," Share "),s(),r(10,"ion-button",60),C("click",function(){P(n);let t=u(2);return x(t.resetIdentification())}),h(11,"ion-icon",61),c(12," New "),s()()(),r(13,"div",62)(14,"h3"),c(15,"Identification Results"),s(),v(16,rt,15,9,"ion-card",63),s(),v(17,lt,19,5,"ion-card",64),s()}if(i&2){let n=u(2);l(2),m("src",n.capturedImage)("alt","Identified plant"),l(14),m("ngForOf",n.identificationResult.species),l(),m("ngIf",n.identificationResult.imageAnalysis)}}function pt(i,a){if(i&1){let n=S();r(0,"div",85)(1,"ion-card")(2,"ion-card-content")(3,"div",86),h(4,"ion-icon",87),r(5,"h3"),c(6,"No Match Found"),s(),r(7,"p"),c(8,"We couldn't identify this plant. Try taking another photo with better lighting or a different angle."),s(),r(9,"ion-button",88),C("click",function(){P(n);let t=u(2);return x(t.resetIdentification())}),h(10,"ion-icon",89),c(11," Try Again "),s()()()()()}}function gt(i,a){if(i&1&&(r(0,"div",51),v(1,dt,18,4,"div",52)(2,pt,12,0,"div",53),s()),i&2){let n=u();l(),m("ngIf",n.identificationResult&&n.identificationResult.species.length>0),l(),m("ngIf",!n.identificationResult||n.identificationResult.species.length===0)}}function mt(i,a){if(i&1){let n=S();r(0,"ion-card",90)(1,"ion-card-content")(2,"div",91),h(3,"ion-icon",92),r(4,"p"),c(5),s(),r(6,"ion-button",93),C("click",function(){P(n);let t=u();return x(t.errorMessage=null)}),c(7,"Dismiss"),s()()()()}if(i&2){let n=u();l(5),I(n.errorMessage)}}var on=(()=>{let a=class a{constructor(e,t,o,d,p,f,_,O){this.identificationService=e,this.authService=t,this.observationService=o,this.router=d,this.alertController=p,this.toastController=f,this.loadingController=_,this.actionSheetController=O,this.isDemoMode=k.demoMode,this.currentStep="capture",this.capturedImage=null,this.imageFile=null,this.selectedPlantPart="leaf",this.selectedFloraRegion="global",this.useLocation=!0,this.currentLocation=null,this.identificationResult=null,this.isProcessing=!1,this.processingProgress=0,this.errorMessage=null,this.plantParts=[{value:"leaf",label:"Leaf",icon:"leaf"},{value:"flower",label:"Flower",icon:"flower"},{value:"fruit",label:"Fruit",icon:"nutrition"},{value:"bark",label:"Bark",icon:"grid"},{value:"habit",label:"Whole Plant",icon:"tree"},{value:"other",label:"Other",icon:"help"}],this.floraRegions=ze,this.subscriptions=[],Me({camera:Se,images:Te,location:Ae,leaf:Re,flower:ke,analytics:Oe,checkmarkCircle:Ie,closeCircle:Ee,refresh:Le,save:Fe,share:Ne})}ngOnInit(){this.getCurrentLocation();let e=this.identificationService.loadingState$.subscribe(t=>{this.isProcessing=t.loading,this.errorMessage=t.error});this.subscriptions.push(e)}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}presentImageSourceActionSheet(){return g(this,null,function*(){yield(yield this.actionSheetController.create({header:"Select Image Source",buttons:[{text:"Camera",icon:"camera",handler:()=>{this.captureImage(M.Camera)}},{text:"Photo Library",icon:"images",handler:()=>{this.captureImage(M.Photos)}},{text:"Cancel",icon:"close",role:"cancel"}]})).present()})}captureImage(e){return g(this,null,function*(){try{let t=yield je.getPhoto({quality:90,allowEditing:!1,resultType:B.DataUrl,source:e});if(t.dataUrl){this.capturedImage=t.dataUrl;let d=yield(yield fetch(t.dataUrl)).blob();this.imageFile=new File([d],"plant-image.jpg",{type:"image/jpeg"}),this.currentStep="configure"}}catch(t){console.error("Error capturing image:",t),this.showErrorToast("Failed to capture image. Please try again.")}})}getCurrentLocation(){return g(this,null,function*(){if(this.useLocation)try{let e=yield Be.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4});this.currentLocation={coordinates:{lat:e.coords.latitude,lng:e.coords.longitude},accuracy:e.coords.accuracy||void 0,altitude:e.coords.altitude||void 0}}catch(e){console.error("Error getting location:",e),this.useLocation=!1}})}startIdentification(){return g(this,null,function*(){var e;if(!this.imageFile){this.showErrorToast("Please capture an image first.");return}this.currentStep="processing",this.processingProgress=0;try{let t=setInterval(()=>{this.processingProgress<90&&(this.processingProgress+=10)},500),o={imageFile:this.imageFile,plantPart:this.selectedPlantPart,floraRegion:this.selectedFloraRegion,location:(e=this.currentLocation)==null?void 0:e.coordinates,modifiers:{includeRelatedSpecies:!0,maxResults:5,minConfidence:.1,useLocationContext:this.useLocation&&!!this.currentLocation}};this.identificationResult=yield this.identificationService.identifyPlant(o),clearInterval(t),this.processingProgress=100,setTimeout(()=>{this.currentStep="results"},500)}catch(t){this.showErrorToast(t.message||"Identification failed. Please try again."),this.currentStep="configure"}})}resetIdentification(){this.currentStep="capture",this.capturedImage=null,this.imageFile=null,this.identificationResult=null,this.errorMessage=null,this.processingProgress=0}retakePhoto(){this.capturedImage=null,this.imageFile=null,this.currentStep="capture"}goBackToConfiguration(){this.currentStep="configure"}saveAsObservation(){return g(this,null,function*(){if(!this.identificationResult||!this.identificationResult.species.length){this.showErrorToast("No identification results to save.");return}if(!this.imageFile||!this.currentLocation){this.showErrorToast("Missing required data for saving observation.");return}yield(yield this.alertController.create({header:"Save Observation",message:"Save this plant identification as an observation?",inputs:[{name:"notes",type:"textarea",placeholder:"Add notes about this observation (optional)"},{name:"isPublic",type:"checkbox",label:"Make this observation public",value:"public",checked:!0}],buttons:[{text:"Cancel",role:"cancel"},{text:"Save",handler:t=>g(this,null,function*(){yield this.performSaveObservation(t.notes,t.includes("public"))})}]})).present()})}performSaveObservation(e,t){return g(this,null,function*(){let o=yield this.loadingController.create({message:"Saving observation...",spinner:"crescent"});yield o.present();try{if(!this.identificationResult||!this.imageFile||!this.currentLocation)throw new Error("Missing required data");let d={species:this.identificationResult.species[0],image:this.imageFile,location:this.currentLocation,plantPart:this.selectedPlantPart,floraRegion:this.selectedFloraRegion,isPublic:t,notes:e||void 0,tags:[]},p=yield this.observationService.createObservation(d);yield o.dismiss(),yield(yield this.toastController.create({message:"Observation saved successfully!",duration:3e3,position:"bottom",color:"success",cssClass:"success-toast",buttons:[{text:"View",side:"end",handler:()=>{this.router.navigate(["/observation",p])}},{text:"Dismiss",role:"cancel",side:"end"}]})).present(),this.resetIdentification()}catch(d){yield o.dismiss(),this.showErrorToast("Failed to save observation: "+(d.message||"Unknown error"))}})}shareResults(){return g(this,null,function*(){if(!this.identificationResult){this.showErrorToast("No results to share.");return}this.showSuccessToast("Feature coming soon: Share results")})}getConfidenceColor(e){return e>=80?"success":e>=60?"warning":"danger"}getConfidenceText(e){return e>=80?"High confidence":e>=60?"Medium confidence":"Low confidence"}showErrorToast(e){return g(this,null,function*(){yield(yield this.toastController.create({message:e,duration:4e3,position:"bottom",color:"danger",cssClass:"error-toast",buttons:[{text:"Dismiss",role:"cancel",side:"end"}]})).present()})}showSuccessToast(e){return g(this,null,function*(){yield(yield this.toastController.create({message:e,duration:3e3,position:"bottom",color:"success",cssClass:"success-toast",buttons:[{text:"Dismiss",role:"cancel",side:"end"}]})).present()})}};a.\u0275fac=function(t){return new(t||a)(w(He),w(D),w(De),w(K),w(_e),w(xe),w(Pe),w(he))},a.\u0275cmp=$({type:a,selectors:[["app-identify"]],decls:12,vars:10,consts:[[3,"translucent"],["slot","start","fill","clear",3,"click",4,"ngIf"],[3,"fullscreen"],["class","demo-banner",4,"ngIf"],["class","capture-container",4,"ngIf"],["class","configure-container",4,"ngIf"],["class","processing-container","role","status","aria-live","polite",4,"ngIf"],["class","results-container",4,"ngIf"],["class","error-card","role","alert","aria-live","polite",4,"ngIf"],["slot","start","fill","clear",3,"click"],["name","arrow-back"],[1,"demo-banner"],["color","warning"],["name","construct"],[1,"capture-container"],[1,"capture-content"],[1,"capture-header"],["name","camera","size","large","color","primary"],[1,"capture-tips"],["expand","block","size","large",1,"capture-button",3,"click"],["name","camera","slot","start"],[1,"configure-container"],[1,"image-preview-card"],[3,"src","alt"],[1,"image-overlay"],["fill","clear",1,"retake-button",3,"click"],[1,"config-card"],["lines","none",1,"config-item"],["position","stacked"],["placeholder","Select plant part","interface","action-sheet",3,"ngModelChange","ngModel"],[3,"value",4,"ngFor","ngForOf"],["placeholder","Select region","interface","action-sheet",3,"ngModelChange","ngModel"],["name","location","slot","start","color","medium"],["slot","end",3,"ngModelChange","ngModel"],["expand","block","aria-label","Start plant identification process",1,"identify-button",3,"click","disabled"],["name","analytics","slot","start","aria-hidden","true"],[3,"value"],["role","status","aria-live","polite",1,"processing-container"],[1,"processing-content"],[1,"processing-header"],["name","crescent","color","primary","aria-label","Processing plant identification"],[1,"processing-card"],[1,"progress-container"],["color","primary",3,"value"],[1,"progress-text"],[1,"processing-steps"],[1,"step"],["name","camera"],["name","analytics"],["name","leaf"],["name","checkmark-circle"],[1,"results-container"],[4,"ngIf"],["class","no-results",4,"ngIf"],[1,"result-image-card"],[1,"result-actions"],["fill","clear","aria-label","Save identification as observation",3,"click"],["name","save","slot","start","aria-hidden","true"],["fill","clear","aria-label","Share identification results",3,"click"],["name","share","slot","start","aria-hidden","true"],["fill","clear","aria-label","Start new identification",3,"click"],["name","refresh","slot","start","aria-hidden","true"],[1,"species-results"],["class","species-card",3,"top-result",4,"ngFor","ngForOf"],["class","analysis-card",4,"ngIf"],[1,"species-card"],[1,"species-header"],[1,"species-info"],[1,"scientific-name"],["class","taxonomy",4,"ngIf"],[1,"confidence-badge"],[3,"color"],["class","species-details",4,"ngIf"],[1,"taxonomy"],[1,"family"],[1,"species-details"],[1,"analysis-card"],[1,"analysis-grid"],[1,"analysis-item"],["color","secondary",3,"value"],["class","detected-features",4,"ngIf"],[1,"detected-features"],[1,"features-list"],["color","tertiary",4,"ngFor","ngForOf"],["color","tertiary"],[1,"no-results"],[1,"no-results-content"],["name","close-circle","size","large","color","medium","aria-hidden","true"],["expand","block","aria-label","Try plant identification again",3,"click"],["name","camera","slot","start","aria-hidden","true"],["role","alert","aria-live","polite",1,"error-card"],[1,"error-content"],["name","close-circle","color","danger","aria-hidden","true"],["fill","clear","aria-label","Dismiss error message",3,"click"]],template:function(t,o){t&1&&(r(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),c(3),s(),v(4,qe,2,0,"ion-button",1),s()(),r(5,"ion-content",2),v(6,Ke,5,0,"div",3)(7,Qe,26,0,"div",4)(8,et,33,8,"div",5)(9,tt,31,10,"div",6)(10,gt,3,2,"div",7)(11,mt,8,1,"ion-card",8),s()),t&2&&(m("translucent",!0),l(3),b(" ",o.currentStep==="capture"?"Identify Plant":o.currentStep==="configure"?"Configure Identification":o.currentStep==="processing"?"Processing...":"Results"," "),l(),m("ngIf",o.currentStep!=="capture"),l(),m("fullscreen",!0),l(),m("ngIf",o.isDemoMode),l(),m("ngIf",o.currentStep==="capture"),l(),m("ngIf",o.currentStep==="configure"),l(),m("ngIf",o.currentStep==="processing"),l(),m("ngIf",o.currentStep==="results"),l(),m("ngIf",o.errorMessage))},dependencies:[J,G,Y,Z,Q,X,re,se,ue,fe,te,ne,ie,oe,ee,ye,le,de,Ce,ge,ce,me,ae,pe,ve],styles:[".demo-banner[_ngcontent-%COMP%]{position:sticky;top:0;z-index:100;background:var(--ion-color-warning-tint);padding:8px 16px;text-align:center;border-bottom:1px solid var(--ion-color-warning)}.demo-banner[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{margin:0;font-weight:500}.demo-banner[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}.capture-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:calc(100vh - 56px);padding:20px;justify-content:center}.capture-content[_ngcontent-%COMP%]{text-align:center}.capture-header[_ngcontent-%COMP%]{margin-bottom:40px}.capture-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-bottom:20px;filter:drop-shadow(0 4px 8px rgba(0,0,0,.1))}.capture-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 10px;font-size:2rem;font-weight:600;color:var(--ion-color-dark)}.capture-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:1.1rem}.capture-tips[_ngcontent-%COMP%]{margin-bottom:40px}.capture-tips[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{border-radius:16px;box-shadow:0 4px 12px #0000001a}.capture-tips[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px;text-align:left}.capture-tips[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px;color:var(--ion-color-dark)}.capture-button[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--border-radius: 16px;--box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), .3);height:60px;font-weight:600;font-size:1.1rem;margin-top:20px}.capture-button[_ngcontent-%COMP%]:hover{--background: var(--ion-color-primary-shade);transform:translateY(-2px);--box-shadow: 0 8px 24px rgba(var(--ion-color-primary-rgb), .4)}.configure-container[_ngcontent-%COMP%]{padding:20px 20px 100px}.image-preview-card[_ngcontent-%COMP%]{position:relative;border-radius:16px;overflow:hidden;margin-bottom:20px;box-shadow:0 6px 20px #00000026}.image-preview-card[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:100%;height:250px;object-fit:cover}.image-preview-card[_ngcontent-%COMP%]   .image-overlay[_ngcontent-%COMP%]{position:absolute;top:16px;right:16px}.image-preview-card[_ngcontent-%COMP%]   .image-overlay[_ngcontent-%COMP%]   .retake-button[_ngcontent-%COMP%]{--background: rgba(0, 0, 0, .6);--color: white;--border-radius: 20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.config-card[_ngcontent-%COMP%]{border-radius:16px;box-shadow:0 4px 12px #0000001a}.config-card[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]{--background: var(--ion-color-light);--border-radius: 12px;margin-bottom:16px;padding:8px 16px}.config-card[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-weight:500}.config-card[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-weight:600}.config-card[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0 0;font-size:.9rem;color:var(--ion-color-medium)}.config-card[_ngcontent-%COMP%]   .identify-button[_ngcontent-%COMP%]{--background: var(--ion-color-secondary);--border-radius: 12px;--box-shadow: 0 4px 12px rgba(var(--ion-color-secondary-rgb), .3);height:56px;font-weight:600;margin-top:24px}.config-card[_ngcontent-%COMP%]   .identify-button[_ngcontent-%COMP%]:hover:not([disabled]){--background: var(--ion-color-secondary-shade);transform:translateY(-2px)}.config-card[_ngcontent-%COMP%]   .identify-button[disabled][_ngcontent-%COMP%]{opacity:.5}.processing-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:calc(100vh - 56px);padding:20px;justify-content:center}.processing-content[_ngcontent-%COMP%]{text-align:center}.processing-header[_ngcontent-%COMP%]{margin-bottom:40px}.processing-header[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-bottom:20px}.processing-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 10px;font-size:1.8rem;font-weight:600;color:var(--ion-color-dark)}.processing-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:1.1rem}.processing-card[_ngcontent-%COMP%]{border-radius:16px;box-shadow:0 4px 12px #0000001a}.processing-card[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]{margin-bottom:30px;position:relative}.processing-card[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{text-align:center;margin-top:8px;font-weight:600;color:var(--ion-color-primary)}.processing-card[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.processing-card[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 16px;border-radius:12px;background:var(--ion-color-light);opacity:.5;transition:all .3s ease}.processing-card[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]{opacity:1;background:var(--ion-color-primary-tint);color:var(--ion-color-primary)}.processing-card[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-primary)}.processing-card[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:12px;font-size:1.2rem}.processing-card[_ngcontent-%COMP%]   .processing-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}.results-container[_ngcontent-%COMP%]{padding:20px 20px 100px}.result-image-card[_ngcontent-%COMP%]{position:relative;border-radius:16px;overflow:hidden;margin-bottom:20px;box-shadow:0 6px 20px #00000026}.result-image-card[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover}.result-image-card[_ngcontent-%COMP%]   .result-actions[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background:linear-gradient(transparent,#000000b3);padding:20px 16px 16px;display:flex;justify-content:space-around}.result-image-card[_ngcontent-%COMP%]   .result-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: white;--border-radius: 20px;font-size:.9rem}.species-results[_ngcontent-%COMP%]{margin-bottom:20px}.species-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 16px;font-size:1.3rem;font-weight:600;color:var(--ion-color-dark)}.species-card[_ngcontent-%COMP%]{border-radius:16px;margin-bottom:16px;box-shadow:0 4px 12px #0000001a;transition:all .3s ease}.species-card.top-result[_ngcontent-%COMP%]{border:2px solid var(--ion-color-primary);box-shadow:0 6px 20px rgba(var(--ion-color-primary-rgb),.2)}.species-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 16px #00000026}.species-card[_ngcontent-%COMP%]   .species-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:12px}.species-card[_ngcontent-%COMP%]   .species-header[_ngcontent-%COMP%]   .species-info[_ngcontent-%COMP%]{flex:1}.species-card[_ngcontent-%COMP%]   .species-header[_ngcontent-%COMP%]   .species-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:1.2rem;font-weight:600;color:var(--ion-color-dark)}.species-card[_ngcontent-%COMP%]   .species-header[_ngcontent-%COMP%]   .species-info[_ngcontent-%COMP%]   .scientific-name[_ngcontent-%COMP%]{margin:0 0 8px;font-style:italic;color:var(--ion-color-medium);font-size:.95rem}.species-card[_ngcontent-%COMP%]   .species-header[_ngcontent-%COMP%]   .species-info[_ngcontent-%COMP%]   .taxonomy[_ngcontent-%COMP%]   .family[_ngcontent-%COMP%]{background:var(--ion-color-light);padding:4px 8px;border-radius:12px;font-size:.8rem;color:var(--ion-color-dark)}.species-card[_ngcontent-%COMP%]   .species-header[_ngcontent-%COMP%]   .confidence-badge[_ngcontent-%COMP%]{text-align:center;min-width:80px}.species-card[_ngcontent-%COMP%]   .species-header[_ngcontent-%COMP%]   .confidence-badge[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{margin:0 0 4px;font-weight:600}.species-card[_ngcontent-%COMP%]   .species-header[_ngcontent-%COMP%]   .confidence-badge[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{display:block;color:var(--ion-color-medium);font-size:.75rem}.species-card[_ngcontent-%COMP%]   .species-details[_ngcontent-%COMP%]{padding-top:12px;border-top:1px solid var(--ion-color-light)}.species-card[_ngcontent-%COMP%]   .species-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-dark);line-height:1.5}.species-card[_ngcontent-%COMP%]   .species-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin-bottom:0}.analysis-card[_ngcontent-%COMP%]{border-radius:16px;margin-bottom:20px;box-shadow:0 4px 12px #0000001a}.analysis-card[_ngcontent-%COMP%]   .analysis-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:20px;margin-bottom:20px}.analysis-card[_ngcontent-%COMP%]   .analysis-grid[_ngcontent-%COMP%]   .analysis-item[_ngcontent-%COMP%]{text-align:center}.analysis-card[_ngcontent-%COMP%]   .analysis-grid[_ngcontent-%COMP%]   .analysis-item[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 8px;font-size:.9rem;font-weight:600;color:var(--ion-color-dark)}.analysis-card[_ngcontent-%COMP%]   .analysis-grid[_ngcontent-%COMP%]   .analysis-item[_ngcontent-%COMP%]   ion-progress-bar[_ngcontent-%COMP%]{margin-bottom:4px;height:8px;border-radius:4px}.analysis-card[_ngcontent-%COMP%]   .analysis-grid[_ngcontent-%COMP%]   .analysis-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.8rem;color:var(--ion-color-medium);font-weight:500}.analysis-card[_ngcontent-%COMP%]   .detected-features[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 12px;font-size:.9rem;font-weight:600;color:var(--ion-color-dark)}.analysis-card[_ngcontent-%COMP%]   .detected-features[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px}.analysis-card[_ngcontent-%COMP%]   .detected-features[_ngcontent-%COMP%]   .features-list[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{font-size:.8rem;height:28px}.no-results[_ngcontent-%COMP%]   .no-results-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.no-results[_ngcontent-%COMP%]   .no-results-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-bottom:20px}.no-results[_ngcontent-%COMP%]   .no-results-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 12px;font-weight:600;color:var(--ion-color-dark)}.no-results[_ngcontent-%COMP%]   .no-results-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 24px;color:var(--ion-color-medium);line-height:1.5}.no-results[_ngcontent-%COMP%]   .no-results-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 12px;height:48px;font-weight:500}.error-card[_ngcontent-%COMP%]{border-radius:16px;border:1px solid var(--ion-color-danger);background:var(--ion-color-danger-tint);margin:20px}.error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.5rem}.error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{flex:1;margin:0;color:var(--ion-color-danger-shade);font-weight:500}.error-card[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-danger);font-size:.9rem}@media (prefers-color-scheme: dark){.capture-tips[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%], .config-card[_ngcontent-%COMP%], .processing-card[_ngcontent-%COMP%], .species-card[_ngcontent-%COMP%], .analysis-card[_ngcontent-%COMP%]{background:var(--ion-color-dark)}.image-preview-card[_ngcontent-%COMP%], .result-image-card[_ngcontent-%COMP%]{box-shadow:0 6px 20px #0000004d}.config-item[_ngcontent-%COMP%]{--background: var(--ion-color-dark-tint)}.species-card.top-result[_ngcontent-%COMP%]{border-color:var(--ion-color-primary-tint)}}@media (max-width: 768px){.capture-container[_ngcontent-%COMP%], .processing-container[_ngcontent-%COMP%]{padding:16px}.configure-container[_ngcontent-%COMP%], .results-container[_ngcontent-%COMP%]{padding:16px 16px 80px}.capture-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.6rem}.analysis-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.result-actions[_ngcontent-%COMP%]{flex-direction:column;gap:8px}.result-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{font-size:.8rem}}@media (max-height: 700px){.capture-container[_ngcontent-%COMP%], .processing-container[_ngcontent-%COMP%]{justify-content:flex-start;padding-top:40px}.capture-header[_ngcontent-%COMP%], .processing-header[_ngcontent-%COMP%]{margin-bottom:30px}}"]});let i=a;return i})();export{on as IdentifyPage};
