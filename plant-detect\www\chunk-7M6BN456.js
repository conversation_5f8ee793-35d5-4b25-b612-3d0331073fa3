import{E as T,a as E,h as L,k as M}from"./chunk-TV52TPWK.js";import{C as c,E as m,G as r,I as e,J as t,K as a,Ka as f,La as g,O as o,Qa as I,Ua as v,V as s,Wa as x,Xa as S,Ya as C,ga as d,gb as b,hb as y,oa as p,pb as h,sa as u}from"./chunk-DVLCYDDQ.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import"./chunk-OLRFWS6T.js";var F=()=>["/test-config"],j=(()=>{let n=class n{constructor(){E({construct:M,settings:T,chevronForward:L})}};n.\u0275fac=function(i){return new(i||n)},n.\u0275cmp=m({type:n,selectors:[["app-settings"]],decls:25,vars:4,consts:[[3,"translucent"],[3,"fullscreen"],[2,"padding","20px"],["button","",3,"routerLink"],["name","settings","slot","start","color","primary"],["name","chevron-forward","slot","end","color","medium"],[2,"text-align","center","padding","40px 20px"],["name","construct","size","large","color","primary"]],template:function(i,k){i&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),o(3,"Settings"),t()()(),e(4,"ion-content",1)(5,"div",2)(6,"ion-card")(7,"ion-card-content")(8,"ion-list")(9,"ion-item",3),a(10,"ion-icon",4),e(11,"ion-label")(12,"h3"),o(13,"Test Configuration"),t(),e(14,"p"),o(15,"Test Firebase and PlantNet API connections"),t()(),a(16,"ion-icon",5),t()()()(),e(17,"ion-card")(18,"ion-card-content")(19,"div",6),a(20,"ion-icon",7),e(21,"h2"),o(22,"More Settings Coming Soon"),t(),e(23,"p"),o(24,"Additional settings will be available in future updates."),t()()()()()()),i&2&&(r("translucent",!0),c(4),r("fullscreen",!0),c(5),r("routerLink",s(3,F)))},dependencies:[d,u,p,I,v,b,y,f,g,h,C,x,S],encapsulation:2});let l=n;return l})();export{j as SettingsPage};
