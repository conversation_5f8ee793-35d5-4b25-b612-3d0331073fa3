import{$ as pd,A as dd,_ as fd,a as Nr,aa as oa,c as ad,d as cd,e as ji,f as ia,g as sa,m as ud,n as Mt,p as mt,q as _e,r as Ft,s as ld,t as zi,u as hd,x as Ye,y as Wi,z as Le}from"./chunk-AZEIYKMX.js";import{a as na,b as ra,e as Vt,f as Lt,g}from"./chunk-OLRFWS6T.js";var md=()=>{};var yd=function(n){let e=[],t=0;for(let r=0;r<n.length;r++){let i=n.charCodeAt(r);i<128?e[t++]=i:i<2048?(e[t++]=i>>6|192,e[t++]=i&63|128):(i&64512)===55296&&r+1<n.length&&(n.charCodeAt(r+1)&64512)===56320?(i=65536+((i&1023)<<10)+(n.charCodeAt(++r)&1023),e[t++]=i>>18|240,e[t++]=i>>12&63|128,e[t++]=i>>6&63|128,e[t++]=i&63|128):(e[t++]=i>>12|224,e[t++]=i>>6&63|128,e[t++]=i&63|128)}return e},M_=function(n){let e=[],t=0,r=0;for(;t<n.length;){let i=n[t++];if(i<128)e[r++]=String.fromCharCode(i);else if(i>191&&i<224){let s=n[t++];e[r++]=String.fromCharCode((i&31)<<6|s&63)}else if(i>239&&i<365){let s=n[t++],a=n[t++],c=n[t++],l=((i&7)<<18|(s&63)<<12|(a&63)<<6|c&63)-65536;e[r++]=String.fromCharCode(55296+(l>>10)),e[r++]=String.fromCharCode(56320+(l&1023))}else{let s=n[t++],a=n[t++];e[r++]=String.fromCharCode((i&15)<<12|(s&63)<<6|a&63)}}return e.join("")},Gi={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:typeof atob=="function",encodeByteArray(n,e){if(!Array.isArray(n))throw Error("encodeByteArray takes an array as a parameter");this.init_();let t=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let i=0;i<n.length;i+=3){let s=n[i],a=i+1<n.length,c=a?n[i+1]:0,l=i+2<n.length,d=l?n[i+2]:0,p=s>>2,_=(s&3)<<4|c>>4,I=(c&15)<<2|d>>6,R=d&63;l||(R=64,a||(I=64)),r.push(t[p],t[_],t[I],t[R])}return r.join("")},encodeString(n,e){return this.HAS_NATIVE_SUPPORT&&!e?btoa(n):this.encodeByteArray(yd(n),e)},decodeString(n,e){return this.HAS_NATIVE_SUPPORT&&!e?atob(n):M_(this.decodeStringToByteArray(n,e))},decodeStringToByteArray(n,e){this.init_();let t=e?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let i=0;i<n.length;){let s=t[n.charAt(i++)],c=i<n.length?t[n.charAt(i)]:0;++i;let d=i<n.length?t[n.charAt(i)]:64;++i;let _=i<n.length?t[n.charAt(i)]:64;if(++i,s==null||c==null||d==null||_==null)throw new ca;let I=s<<2|c>>4;if(r.push(I),d!==64){let R=c<<4&240|d>>2;if(r.push(R),_!==64){let k=d<<6&192|_;r.push(k)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let n=0;n<this.ENCODED_VALS.length;n++)this.byteToCharMap_[n]=this.ENCODED_VALS.charAt(n),this.charToByteMap_[this.byteToCharMap_[n]]=n,this.byteToCharMapWebSafe_[n]=this.ENCODED_VALS_WEBSAFE.charAt(n),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[n]]=n,n>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(n)]=n,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(n)]=n)}}},ca=class extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}},F_=function(n){let e=yd(n);return Gi.encodeByteArray(e,!0)},xr=function(n){return F_(n).replace(/\./g,"")},Hi=function(n){try{return Gi.decodeString(n,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};function la(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("Unable to locate global object.")}var U_=()=>la().__FIREBASE_DEFAULTS__,B_=()=>{if(typeof process>"u"||typeof process.env>"u")return;let n=process.env.__FIREBASE_DEFAULTS__;if(n)return JSON.parse(n)},q_=()=>{if(typeof document>"u")return;let n;try{n=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch{return}let e=n&&Hi(n[1]);return e&&JSON.parse(e)},Ki=()=>{try{return md()||U_()||B_()||q_()}catch(n){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${n}`);return}},ha=n=>{var e,t;return(t=(e=Ki())===null||e===void 0?void 0:e.emulatorHosts)===null||t===void 0?void 0:t[n]},Qi=n=>{let e=ha(n);if(!e)return;let t=e.lastIndexOf(":");if(t<=0||t+1===e.length)throw new Error(`Invalid host ${e} with no separate hostname and port!`);let r=parseInt(e.substring(t+1),10);return e[0]==="["?[e.substring(1,t-1),r]:[e.substring(0,t),r]},da=()=>{var n;return(n=Ki())===null||n===void 0?void 0:n.config},fa=n=>{var e;return(e=Ki())===null||e===void 0?void 0:e[`_${n}`]};var an=class{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}wrapCallback(e){return(t,r)=>{t?this.reject(t):this.resolve(r),typeof e=="function"&&(this.promise.catch(()=>{}),e.length===1?e(t):e(t,r))}}};function Je(n){return n.endsWith(".cloudworkstations.dev")}function kn(n){return g(this,null,function*(){return(yield fetch(n,{credentials:"include"})).ok})}function Yi(n,e){if(n.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');let t={alg:"none",type:"JWT"},r=e||"demo-project",i=n.iat||0,s=n.sub||n.user_id;if(!s)throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");let a=Object.assign({iss:`https://securetoken.google.com/${r}`,aud:r,iat:i,exp:i+3600,auth_time:i,sub:s,user_id:s,firebase:{sign_in_provider:"custom",identities:{}}},n);return[xr(JSON.stringify(t)),xr(JSON.stringify(a)),""].join(".")}var Or={};function $_(){let n={prod:[],emulator:[]};for(let e of Object.keys(Or))Or[e]?n.emulator.push(e):n.prod.push(e);return n}function j_(n){let e=document.getElementById(n),t=!1;return e||(e=document.createElement("div"),e.setAttribute("id",n),t=!0),{created:t,element:e}}var gd=!1;function Dn(n,e){if(typeof window>"u"||typeof document>"u"||!Je(window.location.host)||Or[n]===e||Or[n]||gd)return;Or[n]=e;function t(I){return`__firebase__banner__${I}`}let r="__firebase__banner",s=$_().prod.length>0;function a(){let I=document.getElementById(r);I&&I.remove()}function c(I){I.style.display="flex",I.style.background="#7faaf0",I.style.position="fixed",I.style.bottom="5px",I.style.left="5px",I.style.padding=".5em",I.style.borderRadius="5px",I.style.alignItems="center"}function l(I,R){I.setAttribute("width","24"),I.setAttribute("id",R),I.setAttribute("height","24"),I.setAttribute("viewBox","0 0 24 24"),I.setAttribute("fill","none"),I.style.marginLeft="-6px"}function d(){let I=document.createElement("span");return I.style.cursor="pointer",I.style.marginLeft="16px",I.style.fontSize="24px",I.innerHTML=" &times;",I.onclick=()=>{gd=!0,a()},I}function p(I,R){I.setAttribute("id",R),I.innerText="Learn more",I.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",I.setAttribute("target","__blank"),I.style.paddingLeft="5px",I.style.textDecoration="underline"}function _(){let I=j_(r),R=t("text"),k=document.getElementById(R)||document.createElement("span"),O=t("learnmore"),D=document.getElementById(O)||document.createElement("a"),q=t("preprendIcon"),L=document.getElementById(q)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(I.created){let F=I.element;c(F),p(D,O);let z=d();l(L,q),F.append(L,k,D,z),document.body.appendChild(F)}s?(k.innerText="Preview backend disconnected.",L.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(L.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,k.innerText="Preview backend running in this workspace."),k.setAttribute("id",R)}document.readyState==="loading"?window.addEventListener("DOMContentLoaded",_):_()}function fe(){return typeof navigator<"u"&&typeof navigator.userAgent=="string"?navigator.userAgent:""}function Id(){return typeof window<"u"&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(fe())}function z_(){var n;let e=(n=Ki())===null||n===void 0?void 0:n.forceEnvironment;if(e==="node")return!0;if(e==="browser")return!1;try{return Object.prototype.toString.call(global.process)==="[object process]"}catch{return!1}}function vd(){return typeof navigator<"u"&&navigator.userAgent==="Cloudflare-Workers"}function Ed(){let n=typeof chrome=="object"?chrome.runtime:typeof browser=="object"?browser.runtime:void 0;return typeof n=="object"&&n.id!==void 0}function Td(){return typeof navigator=="object"&&navigator.product==="ReactNative"}function wd(){let n=fe();return n.indexOf("MSIE ")>=0||n.indexOf("Trident/")>=0}function Ad(){return!z_()&&!!navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}function Vr(){try{return typeof indexedDB=="object"}catch{return!1}}function bd(){return new Promise((n,e)=>{try{let t=!0,r="validate-browser-context-for-indexeddb-analytics-module",i=self.indexedDB.open(r);i.onsuccess=()=>{i.result.close(),t||self.indexedDB.deleteDatabase(r),n(!0)},i.onupgradeneeded=()=>{t=!1},i.onerror=()=>{var s;e(((s=i.error)===null||s===void 0?void 0:s.message)||"")}}catch(t){e(t)}})}var W_="FirebaseError",ke=class n extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name=W_,Object.setPrototypeOf(this,n.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,qe.prototype.create)}},qe=class{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){let r=t[0]||{},i=`${this.service}/${e}`,s=this.errors[e],a=s?G_(s,r):"Error",c=`${this.serviceName}: ${a} (${i}).`;return new ke(i,c,r)}};function G_(n,e){return n.replace(H_,(t,r)=>{let i=e[r];return i!=null?String(i):`<${r}?>`})}var H_=/\{\$([^}]+)}/g;function Rd(n){for(let e in n)if(Object.prototype.hasOwnProperty.call(n,e))return!1;return!0}function Xe(n,e){if(n===e)return!0;let t=Object.keys(n),r=Object.keys(e);for(let i of t){if(!r.includes(i))return!1;let s=n[i],a=e[i];if(_d(s)&&_d(a)){if(!Xe(s,a))return!1}else if(s!==a)return!1}for(let i of r)if(!t.includes(i))return!1;return!0}function _d(n){return n!==null&&typeof n=="object"}function Nn(n){let e=[];for(let[t,r]of Object.entries(n))Array.isArray(r)?r.forEach(i=>{e.push(encodeURIComponent(t)+"="+encodeURIComponent(i))}):e.push(encodeURIComponent(t)+"="+encodeURIComponent(r));return e.length?"&"+e.join("&"):""}function On(n){let e={};return n.replace(/^\?/,"").split("&").forEach(r=>{if(r){let[i,s]=r.split("=");e[decodeURIComponent(i)]=decodeURIComponent(s)}}),e}function xn(n){let e=n.indexOf("?");if(!e)return"";let t=n.indexOf("#",e);return n.substring(e,t>0?t:void 0)}function Sd(n,e){let t=new ua(n,e);return t.subscribe.bind(t)}var ua=class{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(r=>{this.error(r)})}next(e){this.forEachObserver(t=>{t.next(e)})}error(e){this.forEachObserver(t=>{t.error(e)}),this.close(e)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,r){let i;if(e===void 0&&t===void 0&&r===void 0)throw new Error("Missing Observer.");K_(e,["next","error","complete"])?i=e:i={next:e,error:t,complete:r},i.next===void 0&&(i.next=aa),i.error===void 0&&(i.error=aa),i.complete===void 0&&(i.complete=aa);let s=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?i.error(this.finalError):i.complete()}catch{}}),this.observers.push(i),s}unsubscribeOne(e){this.observers===void 0||this.observers[e]===void 0||(delete this.observers[e],this.observerCount-=1,this.observerCount===0&&this.onNoObservers!==void 0&&this.onNoObservers(this))}forEachObserver(e){if(!this.finalized)for(let t=0;t<this.observers.length;t++)this.sendOne(t,e)}sendOne(e,t){this.task.then(()=>{if(this.observers!==void 0&&this.observers[e]!==void 0)try{t(this.observers[e])}catch(r){typeof console<"u"&&console.error&&console.error(r)}})}close(e){this.finalized||(this.finalized=!0,e!==void 0&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}};function K_(n,e){if(typeof n!="object"||n===null)return!1;for(let t of e)if(t in n&&typeof n[t]=="function")return!0;return!1}function aa(){}var Ob=4*60*60*1e3;function ee(n){return n&&n._delegate?n._delegate:n}var ye=class{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}};var cn="[DEFAULT]";var pa=class{constructor(e,t){this.name=e,this.container=t,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){let t=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(t)){let r=new an;if(this.instancesDeferred.set(t,r),this.isInitialized(t)||this.shouldAutoInitialize())try{let i=this.getOrInitializeService({instanceIdentifier:t});i&&r.resolve(i)}catch{}}return this.instancesDeferred.get(t).promise}getImmediate(e){var t;let r=this.normalizeInstanceIdentifier(e==null?void 0:e.identifier),i=(t=e==null?void 0:e.optional)!==null&&t!==void 0?t:!1;if(this.isInitialized(r)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:r})}catch(s){if(i)return null;throw s}else{if(i)return null;throw Error(`Service ${this.name} is not available`)}}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,!!this.shouldAutoInitialize()){if(Y_(e))try{this.getOrInitializeService({instanceIdentifier:cn})}catch{}for(let[t,r]of this.instancesDeferred.entries()){let i=this.normalizeInstanceIdentifier(t);try{let s=this.getOrInitializeService({instanceIdentifier:i});r.resolve(s)}catch{}}}}clearInstance(e=cn){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}delete(){return g(this,null,function*(){let e=Array.from(this.instances.values());yield Promise.all([...e.filter(t=>"INTERNAL"in t).map(t=>t.INTERNAL.delete()),...e.filter(t=>"_delete"in t).map(t=>t._delete())])})}isComponentSet(){return this.component!=null}isInitialized(e=cn){return this.instances.has(e)}getOptions(e=cn){return this.instancesOptions.get(e)||{}}initialize(e={}){let{options:t={}}=e,r=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(r))throw Error(`${this.name}(${r}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let i=this.getOrInitializeService({instanceIdentifier:r,options:t});for(let[s,a]of this.instancesDeferred.entries()){let c=this.normalizeInstanceIdentifier(s);r===c&&a.resolve(i)}return i}onInit(e,t){var r;let i=this.normalizeInstanceIdentifier(t),s=(r=this.onInitCallbacks.get(i))!==null&&r!==void 0?r:new Set;s.add(e),this.onInitCallbacks.set(i,s);let a=this.instances.get(i);return a&&e(a,i),()=>{s.delete(e)}}invokeOnInitCallbacks(e,t){let r=this.onInitCallbacks.get(t);if(r)for(let i of r)try{i(e,t)}catch{}}getOrInitializeService({instanceIdentifier:e,options:t={}}){let r=this.instances.get(e);if(!r&&this.component&&(r=this.component.instanceFactory(this.container,{instanceIdentifier:Q_(e),options:t}),this.instances.set(e,r),this.instancesOptions.set(e,t),this.invokeOnInitCallbacks(r,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,r)}catch{}return r||null}normalizeInstanceIdentifier(e=cn){return this.component?this.component.multipleInstances?e:cn:e}shouldAutoInitialize(){return!!this.component&&this.component.instantiationMode!=="EXPLICIT"}};function Q_(n){return n===cn?void 0:n}function Y_(n){return n.instantiationMode==="EAGER"}var Ji=class{constructor(e){this.name=e,this.providers=new Map}addComponent(e){let t=this.getProvider(e.name);if(t.isComponentSet())throw new Error(`Component ${e.name} has already been registered with ${this.name}`);t.setComponent(e)}addOrOverwriteComponent(e){this.getProvider(e.name).isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){if(this.providers.has(e))return this.providers.get(e);let t=new pa(e,this);return this.providers.set(e,t),t}getProviders(){return Array.from(this.providers.values())}};var J_=[],G=function(n){return n[n.DEBUG=0]="DEBUG",n[n.VERBOSE=1]="VERBOSE",n[n.INFO=2]="INFO",n[n.WARN=3]="WARN",n[n.ERROR=4]="ERROR",n[n.SILENT=5]="SILENT",n}(G||{}),X_={debug:G.DEBUG,verbose:G.VERBOSE,info:G.INFO,warn:G.WARN,error:G.ERROR,silent:G.SILENT},Z_=G.INFO,ey={[G.DEBUG]:"log",[G.VERBOSE]:"log",[G.INFO]:"info",[G.WARN]:"warn",[G.ERROR]:"error"},ty=(n,e,...t)=>{if(e<n.logLevel)return;let r=new Date().toISOString(),i=ey[e];if(i)console[i](`[${r}]  ${n.name}:`,...t);else throw new Error(`Attempted to log a message with an invalid logType (value: ${e})`)},Ze=class{constructor(e){this.name=e,this._logLevel=Z_,this._logHandler=ty,this._userLogHandler=null,J_.push(this)}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in G))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel=typeof e=="string"?X_[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if(typeof e!="function")throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,G.DEBUG,...e),this._logHandler(this,G.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,G.VERBOSE,...e),this._logHandler(this,G.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,G.INFO,...e),this._logHandler(this,G.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,G.WARN,...e),this._logHandler(this,G.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,G.ERROR,...e),this._logHandler(this,G.ERROR,...e)}};var ny=(n,e)=>e.some(t=>n instanceof t),Pd,Cd;function ry(){return Pd||(Pd=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function iy(){return Cd||(Cd=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}var kd=new WeakMap,ga=new WeakMap,Dd=new WeakMap,ma=new WeakMap,ya=new WeakMap;function sy(n){let e=new Promise((t,r)=>{let i=()=>{n.removeEventListener("success",s),n.removeEventListener("error",a)},s=()=>{t(et(n.result)),i()},a=()=>{r(n.error),i()};n.addEventListener("success",s),n.addEventListener("error",a)});return e.then(t=>{t instanceof IDBCursor&&kd.set(t,n)}).catch(()=>{}),ya.set(e,n),e}function oy(n){if(ga.has(n))return;let e=new Promise((t,r)=>{let i=()=>{n.removeEventListener("complete",s),n.removeEventListener("error",a),n.removeEventListener("abort",a)},s=()=>{t(),i()},a=()=>{r(n.error||new DOMException("AbortError","AbortError")),i()};n.addEventListener("complete",s),n.addEventListener("error",a),n.addEventListener("abort",a)});ga.set(n,e)}var _a={get(n,e,t){if(n instanceof IDBTransaction){if(e==="done")return ga.get(n);if(e==="objectStoreNames")return n.objectStoreNames||Dd.get(n);if(e==="store")return t.objectStoreNames[1]?void 0:t.objectStore(t.objectStoreNames[0])}return et(n[e])},set(n,e,t){return n[e]=t,!0},has(n,e){return n instanceof IDBTransaction&&(e==="done"||e==="store")?!0:e in n}};function Nd(n){_a=n(_a)}function ay(n){return n===IDBDatabase.prototype.transaction&&!("objectStoreNames"in IDBTransaction.prototype)?function(e,...t){let r=n.call(Xi(this),e,...t);return Dd.set(r,e.sort?e.sort():[e]),et(r)}:iy().includes(n)?function(...e){return n.apply(Xi(this),e),et(kd.get(this))}:function(...e){return et(n.apply(Xi(this),e))}}function cy(n){return typeof n=="function"?ay(n):(n instanceof IDBTransaction&&oy(n),ny(n,ry())?new Proxy(n,_a):n)}function et(n){if(n instanceof IDBRequest)return sy(n);if(ma.has(n))return ma.get(n);let e=cy(n);return e!==n&&(ma.set(n,e),ya.set(e,n)),e}var Xi=n=>ya.get(n);function xd(n,e,{blocked:t,upgrade:r,blocking:i,terminated:s}={}){let a=indexedDB.open(n,e),c=et(a);return r&&a.addEventListener("upgradeneeded",l=>{r(et(a.result),l.oldVersion,l.newVersion,et(a.transaction),l)}),t&&a.addEventListener("blocked",l=>t(l.oldVersion,l.newVersion,l)),c.then(l=>{s&&l.addEventListener("close",()=>s()),i&&l.addEventListener("versionchange",d=>i(d.oldVersion,d.newVersion,d))}).catch(()=>{}),c}var uy=["get","getKey","getAll","getAllKeys","count"],ly=["put","add","delete","clear"],Ia=new Map;function Od(n,e){if(!(n instanceof IDBDatabase&&!(e in n)&&typeof e=="string"))return;if(Ia.get(e))return Ia.get(e);let t=e.replace(/FromIndex$/,""),r=e!==t,i=ly.includes(t);if(!(t in(r?IDBIndex:IDBObjectStore).prototype)||!(i||uy.includes(t)))return;let s=function(a,...c){return g(this,null,function*(){let l=this.transaction(a,i?"readwrite":"readonly"),d=l.store;return r&&(d=d.index(c.shift())),(yield Promise.all([d[t](...c),i&&l.done]))[0]})};return Ia.set(e,s),s}Nd(n=>ra(na({},n),{get:(e,t,r)=>Od(e,t)||n.get(e,t,r),has:(e,t)=>!!Od(e,t)||n.has(e,t)}));var Ea=class{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map(t=>{if(hy(t)){let r=t.getImmediate();return`${r.library}/${r.version}`}else return null}).filter(t=>t).join(" ")}};function hy(n){let e=n.getComponent();return(e==null?void 0:e.type)==="VERSION"}var Ta="@firebase/app",Vd="0.13.0";var gt=new Ze("@firebase/app"),dy="@firebase/app-compat",fy="@firebase/analytics-compat",py="@firebase/analytics",my="@firebase/app-check-compat",gy="@firebase/app-check",_y="@firebase/auth",yy="@firebase/auth-compat",Iy="@firebase/database",vy="@firebase/data-connect",Ey="@firebase/database-compat",Ty="@firebase/functions",wy="@firebase/functions-compat",Ay="@firebase/installations",by="@firebase/installations-compat",Ry="@firebase/messaging",Sy="@firebase/messaging-compat",Py="@firebase/performance",Cy="@firebase/performance-compat",ky="@firebase/remote-config",Dy="@firebase/remote-config-compat",Ny="@firebase/storage",Oy="@firebase/storage-compat",xy="@firebase/firestore",Vy="@firebase/ai",Ly="@firebase/firestore-compat",My="firebase",Fy="11.8.0";var wa="[DEFAULT]",Uy={[Ta]:"fire-core",[dy]:"fire-core-compat",[py]:"fire-analytics",[fy]:"fire-analytics-compat",[gy]:"fire-app-check",[my]:"fire-app-check-compat",[_y]:"fire-auth",[yy]:"fire-auth-compat",[Iy]:"fire-rtdb",[vy]:"fire-data-connect",[Ey]:"fire-rtdb-compat",[Ty]:"fire-fn",[wy]:"fire-fn-compat",[Ay]:"fire-iid",[by]:"fire-iid-compat",[Ry]:"fire-fcm",[Sy]:"fire-fcm-compat",[Py]:"fire-perf",[Cy]:"fire-perf-compat",[ky]:"fire-rc",[Dy]:"fire-rc-compat",[Ny]:"fire-gcs",[Oy]:"fire-gcs-compat",[xy]:"fire-fst",[Ly]:"fire-fst-compat",[Vy]:"fire-vertex","fire-js":"fire-js",[My]:"fire-js-all"};var Lr=new Map,By=new Map,Aa=new Map;function Ld(n,e){try{n.container.addComponent(e)}catch(t){gt.debug(`Component ${e.name} failed to register with FirebaseApp ${n.name}`,t)}}function Oe(n){let e=n.name;if(Aa.has(e))return gt.debug(`There were multiple attempts to register component ${e}.`),!1;Aa.set(e,n);for(let t of Lr.values())Ld(t,n);for(let t of By.values())Ld(t,n);return!0}function Bt(n,e){let t=n.container.getProvider("heartbeat").getImmediate({optional:!0});return t&&t.triggerHeartbeat(),n.container.getProvider(e)}function Ie(n){return n==null?!1:n.settings!==void 0}var qy={"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."},Ut=new qe("app","Firebase",qy);var ba=class{constructor(e,t,r){this._isDeleted=!1,this._options=Object.assign({},e),this._config=Object.assign({},t),this._name=t.name,this._automaticDataCollectionEnabled=t.automaticDataCollectionEnabled,this._container=r,this.container.addComponent(new ye("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw Ut.create("app-deleted",{appName:this._name})}};var tt=Fy;function Pa(n,e={}){let t=n;typeof e!="object"&&(e={name:e});let r=Object.assign({name:wa,automaticDataCollectionEnabled:!0},e),i=r.name;if(typeof i!="string"||!i)throw Ut.create("bad-app-name",{appName:String(i)});if(t||(t=da()),!t)throw Ut.create("no-options");let s=Lr.get(i);if(s){if(Xe(t,s.options)&&Xe(r,s.config))return s;throw Ut.create("duplicate-app",{appName:i})}let a=new Ji(i);for(let l of Aa.values())a.addComponent(l);let c=new ba(t,r,a);return Lr.set(i,c),c}function _t(n=wa){let e=Lr.get(n);if(!e&&n===wa&&da())return Pa();if(!e)throw Ut.create("no-app",{appName:n});return e}function Zi(){return Array.from(Lr.values())}function te(n,e,t){var r;let i=(r=Uy[n])!==null&&r!==void 0?r:n;t&&(i+=`-${t}`);let s=i.match(/\s|\//),a=e.match(/\s|\//);if(s||a){let c=[`Unable to register library "${i}" with version "${e}":`];s&&c.push(`library name "${i}" contains illegal characters (whitespace or "/")`),s&&a&&c.push("and"),a&&c.push(`version name "${e}" contains illegal characters (whitespace or "/")`),gt.warn(c.join(" "));return}Oe(new ye(`${i}-version`,()=>({library:i,version:e}),"VERSION"))}var $y="firebase-heartbeat-database",jy=1,Mr="firebase-heartbeat-store",va=null;function Bd(){return va||(va=xd($y,jy,{upgrade:(n,e)=>{switch(e){case 0:try{n.createObjectStore(Mr)}catch(t){console.warn(t)}}}}).catch(n=>{throw Ut.create("idb-open",{originalErrorMessage:n.message})})),va}function zy(n){return g(this,null,function*(){try{let t=(yield Bd()).transaction(Mr),r=yield t.objectStore(Mr).get(qd(n));return yield t.done,r}catch(e){if(e instanceof ke)gt.warn(e.message);else{let t=Ut.create("idb-get",{originalErrorMessage:e==null?void 0:e.message});gt.warn(t.message)}}})}function Md(n,e){return g(this,null,function*(){try{let r=(yield Bd()).transaction(Mr,"readwrite");yield r.objectStore(Mr).put(e,qd(n)),yield r.done}catch(t){if(t instanceof ke)gt.warn(t.message);else{let r=Ut.create("idb-set",{originalErrorMessage:t==null?void 0:t.message});gt.warn(r.message)}}})}function qd(n){return`${n.name}!${n.options.appId}`}var Wy=1024,Gy=30,Ra=class{constructor(e){this.container=e,this._heartbeatsCache=null;let t=this.container.getProvider("app").getImmediate();this._storage=new Sa(t),this._heartbeatsCachePromise=this._storage.read().then(r=>(this._heartbeatsCache=r,r))}triggerHeartbeat(){return g(this,null,function*(){var e,t;try{let i=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),s=Fd();if(((e=this._heartbeatsCache)===null||e===void 0?void 0:e.heartbeats)==null&&(this._heartbeatsCache=yield this._heartbeatsCachePromise,((t=this._heartbeatsCache)===null||t===void 0?void 0:t.heartbeats)==null)||this._heartbeatsCache.lastSentHeartbeatDate===s||this._heartbeatsCache.heartbeats.some(a=>a.date===s))return;if(this._heartbeatsCache.heartbeats.push({date:s,agent:i}),this._heartbeatsCache.heartbeats.length>Gy){let a=Ky(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(a,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(r){gt.warn(r)}})}getHeartbeatsHeader(){return g(this,null,function*(){var e;try{if(this._heartbeatsCache===null&&(yield this._heartbeatsCachePromise),((e=this._heartbeatsCache)===null||e===void 0?void 0:e.heartbeats)==null||this._heartbeatsCache.heartbeats.length===0)return"";let t=Fd(),{heartbeatsToSend:r,unsentEntries:i}=Hy(this._heartbeatsCache.heartbeats),s=xr(JSON.stringify({version:2,heartbeats:r}));return this._heartbeatsCache.lastSentHeartbeatDate=t,i.length>0?(this._heartbeatsCache.heartbeats=i,yield this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),s}catch(t){return gt.warn(t),""}})}};function Fd(){return new Date().toISOString().substring(0,10)}function Hy(n,e=Wy){let t=[],r=n.slice();for(let i of n){let s=t.find(a=>a.agent===i.agent);if(s){if(s.dates.push(i.date),Ud(t)>e){s.dates.pop();break}}else if(t.push({agent:i.agent,dates:[i.date]}),Ud(t)>e){t.pop();break}r=r.slice(1)}return{heartbeatsToSend:t,unsentEntries:r}}var Sa=class{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}runIndexedDBEnvironmentCheck(){return g(this,null,function*(){return Vr()?bd().then(()=>!0).catch(()=>!1):!1})}read(){return g(this,null,function*(){if(yield this._canUseIndexedDBPromise){let t=yield zy(this.app);return t!=null&&t.heartbeats?t:{heartbeats:[]}}else return{heartbeats:[]}})}overwrite(e){return g(this,null,function*(){var t;if(yield this._canUseIndexedDBPromise){let i=yield this.read();return Md(this.app,{lastSentHeartbeatDate:(t=e.lastSentHeartbeatDate)!==null&&t!==void 0?t:i.lastSentHeartbeatDate,heartbeats:e.heartbeats})}else return})}add(e){return g(this,null,function*(){var t;if(yield this._canUseIndexedDBPromise){let i=yield this.read();return Md(this.app,{lastSentHeartbeatDate:(t=e.lastSentHeartbeatDate)!==null&&t!==void 0?t:i.lastSentHeartbeatDate,heartbeats:[...i.heartbeats,...e.heartbeats]})}else return})}};function Ud(n){return xr(JSON.stringify({version:2,heartbeats:n})).length}function Ky(n){if(n.length===0)return-1;let e=0,t=n[0].date;for(let r=1;r<n.length;r++)n[r].date<t&&(t=n[r].date,e=r);return e}function Qy(n){Oe(new ye("platform-logger",e=>new Ea(e),"PRIVATE")),Oe(new ye("heartbeat",e=>new Ra(e),"PRIVATE")),te(Ta,Vd,n),te(Ta,Vd,"esm2017"),te("fire-js","")}Qy("");var Yy="firebase",Jy="11.8.1";te(Yy,Jy,"app");function Ca(n){n===void 0&&(hd(Ca),n=mt(Ye));let e=n.get(Wi);return t=>new Nr(r=>{let i=e.add(),s=!1;function a(){s||(i(),s=!0)}let c=t.subscribe({next:l=>{r.next(l),a()},complete:()=>{r.complete(),a()},error:l=>{r.error(l),a()}});return c.add(()=>{r.unsubscribe(),a()}),c})}var nt=new fd("ANGULARFIRE2_VERSION");function ln(n,e,t){if(e){if(e.length===1)return e[0];let s=e.filter(a=>a.app===t);if(s.length===1)return s[0]}return t.container.getProvider(n).getImmediate({optional:!0})}var qt=(n,e)=>{let t=e?[e]:Zi(),r=[];return t.forEach(i=>{i.container.getProvider(n).instances.forEach(a=>{r.includes(a)||r.push(a)})}),r},Vn=function(n){return n[n.SILENT=0]="SILENT",n[n.WARN=1]="WARN",n[n.VERBOSE=2]="VERBOSE",n}(Vn||{}),$d=oa()&&typeof Zone<"u"?Vn.WARN:Vn.SILENT;var es=class{zone;delegate;constructor(e,t=cd){this.zone=e,this.delegate=t}now(){return this.delegate.now()}schedule(e,t,r){let i=this.zone,s=function(a){i?i.runGuarded(()=>{e.apply(this,[a])}):e.apply(this,[a])};return this.delegate.schedule(s,t,r)}},rt=(()=>{let e=class e{outsideAngular;insideAngular;constructor(){let r=mt(Le);this.outsideAngular=r.runOutsideAngular(()=>new es(typeof Zone>"u"?void 0:Zone.current)),this.insideAngular=r.run(()=>new es(typeof Zone>"u"?void 0:Zone.current,ad))}};Vt(e,"\u0275fac",function(i){return new(i||e)}),Vt(e,"\u0275prov",ud({token:e,factory:e.\u0275fac,providedIn:"root"}));let n=e;return n})(),jd=!1;function Xy(n,e){!jd&&($d>Vn.SILENT||oa())&&(jd=!0,console.warn("Calling Firebase APIs outside of an Injection context may destabilize your application leading to subtle change-detection and hydration bugs. Find more at https://github.com/angular/angularfire/blob/main/docs/zones.md")),$d>=e&&console.warn(`Firebase API called outside injection context: ${n.name}`)}function Zy(n){let e=mt(Le,{optional:!0});return e?e.runOutsideAngular(()=>n()):n()}function un(n){let e=mt(Le,{optional:!0});return e?e.run(()=>n()):n()}var eI=(n,e,t)=>(...r)=>(e&&setTimeout(e,0),zi(t,()=>un(()=>n.apply(void 0,r)))),J=(n,e,t)=>(t||(t=e?Vn.WARN:Vn.VERBOSE),function(){let r,i=arguments,s,a,c;try{s=mt(rt),a=mt(Wi),c=mt(ld)}catch{return Xy(n,t),n.apply(this,i)}for(let d=0;d<arguments.length;d++)typeof i[d]=="function"&&(e&&(r||(r=un(()=>a.add()))),i[d]=eI(i[d],r,c));let l=Zy(()=>n.apply(this,i));return e?l instanceof Nr?l.pipe(sa(s.outsideAngular),ia(s.insideAngular),Ca(c)):l instanceof Promise?un(()=>new Promise((d,p)=>{a.run(()=>l).then(_=>zi(c,()=>un(()=>d(_))),_=>zi(c,()=>un(()=>p(_))))})):typeof l=="function"&&r?function(){return setTimeout(r,0),l.apply(this,arguments)}:un(()=>l):l instanceof Nr?l.pipe(sa(s.outsideAngular),ia(s.insideAngular)):un(()=>l)});var $e=class{constructor(e){return e}},yt=class{constructor(){return Zi()}};function tI(n){return n&&n.length===1?n[0]:new $e(_t())}var ka=new Mt("angularfire2._apps"),nI={provide:$e,useFactory:tI,deps:[[new _e,ka]]},rI={provide:yt,deps:[[new _e,ka]]};function iI(n){return(e,t)=>{let r=t.get(dd);te("angularfire",nt.full,"core"),te("angularfire",nt.full,"app"),te("angular",pd.full,r.toString());let i=e.runOutsideAngular(()=>n(t));return new $e(i)}}function F0(n,...e){return Ft([nI,rI,{provide:ka,useFactory:iI(n),multi:!0,deps:[Le,Ye,rt,...e]}])}var U0=J(Pa,!0);var sI=new Map,oI={activated:!1,tokenObservers:[]},aI={initialized:!1,enabled:!1};function je(n){return sI.get(n)||Object.assign({},oI)}function Hd(){return aI}var cI="https://content-firebaseappcheck.googleapis.com/v1";var uI="exchangeDebugToken",zd={OFFSET_DURATION:5*60*1e3,RETRIAL_MIN_WAIT:30*1e3,RETRIAL_MAX_WAIT:16*60*1e3},Q0=24*60*60*1e3;var Oa=class{constructor(e,t,r,i,s){if(this.operation=e,this.retryPolicy=t,this.getWaitDuration=r,this.lowerBound=i,this.upperBound=s,this.pending=null,this.nextErrorWaitInterval=i,i>s)throw new Error("Proactive refresh lower bound greater than upper bound!")}start(){this.nextErrorWaitInterval=this.lowerBound,this.process(!0).catch(()=>{})}stop(){this.pending&&(this.pending.reject("cancelled"),this.pending=null)}isRunning(){return!!this.pending}process(e){return g(this,null,function*(){this.stop();try{this.pending=new an,this.pending.promise.catch(t=>{}),yield lI(this.getNextRun(e)),this.pending.resolve(),yield this.pending.promise,this.pending=new an,this.pending.promise.catch(t=>{}),yield this.operation(),this.pending.resolve(),yield this.pending.promise,this.process(!0).catch(()=>{})}catch(t){this.retryPolicy(t)?this.process(!1).catch(()=>{}):this.stop()}})}getNextRun(e){if(e)return this.nextErrorWaitInterval=this.lowerBound,this.getWaitDuration();{let t=this.nextErrorWaitInterval;return this.nextErrorWaitInterval*=2,this.nextErrorWaitInterval>this.upperBound&&(this.nextErrorWaitInterval=this.upperBound),t}}};function lI(n){return new Promise(e=>{setTimeout(e,n)})}var hI={"already-initialized":"You have already called initializeAppCheck() for FirebaseApp {$appName} with different options. To avoid this error, call initializeAppCheck() with the same options as when it was originally called. This will return the already initialized instance.","use-before-activation":"App Check is being used before initializeAppCheck() is called for FirebaseApp {$appName}. Call initializeAppCheck() before instantiating other Firebase services.","fetch-network-error":"Fetch failed to connect to a network. Check Internet connection. Original error: {$originalErrorMessage}.","fetch-parse-error":"Fetch client could not parse response. Original error: {$originalErrorMessage}.","fetch-status-error":"Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.","storage-open":"Error thrown when opening storage. Original error: {$originalErrorMessage}.","storage-get":"Error thrown when reading from storage. Original error: {$originalErrorMessage}.","storage-set":"Error thrown when writing to storage. Original error: {$originalErrorMessage}.","recaptcha-error":"ReCAPTCHA error.","initial-throttle":"{$httpStatus} error. Attempts allowed again after {$time}",throttled:"Requests throttled due to previous {$httpStatus} error. Attempts allowed again after {$time}"},$t=new qe("appCheck","AppCheck",hI);function Kd(n){if(!je(n).activated)throw $t.create("use-before-activation",{appName:n.name})}function Qd(r,i){return g(this,arguments,function*({url:n,body:e},t){let s={"Content-Type":"application/json"},a=t.getImmediate({optional:!0});if(a){let R=yield a.getHeartbeatsHeader();R&&(s["X-Firebase-Client"]=R)}let c={method:"POST",body:JSON.stringify(e),headers:s},l;try{l=yield fetch(n,c)}catch(R){throw $t.create("fetch-network-error",{originalErrorMessage:R==null?void 0:R.message})}if(l.status!==200)throw $t.create("fetch-status-error",{httpStatus:l.status});let d;try{d=yield l.json()}catch(R){throw $t.create("fetch-parse-error",{originalErrorMessage:R==null?void 0:R.message})}let p=d.ttl.match(/^([\d.]+)(s)$/);if(!p||!p[2]||isNaN(Number(p[1])))throw $t.create("fetch-parse-error",{originalErrorMessage:`ttl field (timeToLive) is not in standard Protobuf Duration format: ${d.ttl}`});let _=Number(p[1])*1e3,I=Date.now();return{token:d.token,expireTimeMillis:I+_,issuedAtTimeMillis:I}})}function Yd(n,e){let{projectId:t,appId:r,apiKey:i}=n.options;return{url:`${cI}/projects/${t}/apps/${r}:${uI}?key=${i}`,body:{debug_token:e}}}var dI="firebase-app-check-database",fI=1,xa="firebase-app-check-store";var ts=null;function pI(){return ts||(ts=new Promise((n,e)=>{try{let t=indexedDB.open(dI,fI);t.onsuccess=r=>{n(r.target.result)},t.onerror=r=>{var i;e($t.create("storage-open",{originalErrorMessage:(i=r.target.error)===null||i===void 0?void 0:i.message}))},t.onupgradeneeded=r=>{let i=r.target.result;switch(r.oldVersion){case 0:i.createObjectStore(xa,{keyPath:"compositeKey"})}}}catch(t){e($t.create("storage-open",{originalErrorMessage:t==null?void 0:t.message}))}}),ts)}function mI(n,e){return gI(_I(n),e)}function gI(n,e){return g(this,null,function*(){let r=(yield pI()).transaction(xa,"readwrite"),s=r.objectStore(xa).put({compositeKey:n,value:e});return new Promise((a,c)=>{s.onsuccess=l=>{a()},r.onerror=l=>{var d;c($t.create("storage-set",{originalErrorMessage:(d=l.target.error)===null||d===void 0?void 0:d.message}))}})})}function _I(n){return`${n.options.appId}-${n.name}`}var Fr=new Ze("@firebase/app-check");function Da(n,e){return Vr()?mI(n,e).catch(t=>{Fr.warn(`Failed to write token to IndexedDB. Error: ${t}`)}):Promise.resolve()}function Jd(){return Hd().enabled}function Xd(){return g(this,null,function*(){let n=Hd();if(n.enabled&&n.token)return n.token.promise;throw Error(`
            Can't get debug token in production mode.
        `)})}var yI={error:"UNKNOWN_ERROR"};function II(n){return Gi.encodeString(JSON.stringify(n),!1)}function Va(n,e=!1,t=!1){return g(this,null,function*(){let r=n.app;Kd(r);let i=je(r),s=i.token,a;if(s&&!Ur(s)&&(i.token=void 0,s=void 0),!s){let d=yield i.cachedTokenPromise;d&&(Ur(d)?s=d:yield Da(r,void 0))}if(!e&&s&&Ur(s))return{token:s.token};let c=!1;if(Jd())try{i.exchangeTokenPromise||(i.exchangeTokenPromise=Qd(Yd(r,yield Xd()),n.heartbeatServiceProvider).finally(()=>{i.exchangeTokenPromise=void 0}),c=!0);let d=yield i.exchangeTokenPromise;return yield Da(r,d),i.token=d,{token:d.token}}catch(d){return d.code==="appCheck/throttled"||d.code==="appCheck/initial-throttle"?Fr.warn(d.message):t&&Fr.error(d),Na(d)}try{i.exchangeTokenPromise||(i.exchangeTokenPromise=i.provider.getToken().finally(()=>{i.exchangeTokenPromise=void 0}),c=!0),s=yield je(r).exchangeTokenPromise}catch(d){d.code==="appCheck/throttled"||d.code==="appCheck/initial-throttle"?Fr.warn(d.message):t&&Fr.error(d),a=d}let l;return s?a?Ur(s)?l={token:s.token,internalError:a}:l=Na(a):(l={token:s.token},i.token=s,yield Da(r,s)):l=Na(a),c&&wI(r,l),l})}function vI(n){return g(this,null,function*(){let e=n.app;Kd(e);let{provider:t}=je(e);if(Jd()){let r=yield Xd(),{token:i}=yield Qd(Yd(e,r),n.heartbeatServiceProvider);return{token:i}}else{let{token:r}=yield t.getToken();return{token:r}}})}function EI(n,e,t,r){let{app:i}=n,s=je(i),a={next:t,error:r,type:e};if(s.tokenObservers=[...s.tokenObservers,a],s.token&&Ur(s.token)){let c=s.token;Promise.resolve().then(()=>{t({token:c.token}),Wd(n)}).catch(()=>{})}s.cachedTokenPromise.then(()=>Wd(n))}function Zd(n,e){let t=je(n),r=t.tokenObservers.filter(i=>i.next!==e);r.length===0&&t.tokenRefresher&&t.tokenRefresher.isRunning()&&t.tokenRefresher.stop(),t.tokenObservers=r}function Wd(n){let{app:e}=n,t=je(e),r=t.tokenRefresher;r||(r=TI(n),t.tokenRefresher=r),!r.isRunning()&&t.isTokenAutoRefreshEnabled&&r.start()}function TI(n){let{app:e}=n;return new Oa(()=>g(null,null,function*(){let t=je(e),r;if(t.token?r=yield Va(n,!0):r=yield Va(n),r.error)throw r.error;if(r.internalError)throw r.internalError}),()=>!0,()=>{let t=je(e);if(t.token){let r=t.token.issuedAtTimeMillis+(t.token.expireTimeMillis-t.token.issuedAtTimeMillis)*.5+3e5,i=t.token.expireTimeMillis-5*60*1e3;return r=Math.min(r,i),Math.max(0,r-Date.now())}else return 0},zd.RETRIAL_MIN_WAIT,zd.RETRIAL_MAX_WAIT)}function wI(n,e){let t=je(n).tokenObservers;for(let r of t)try{r.type==="EXTERNAL"&&e.error!=null?r.error(e.error):r.next(e)}catch{}}function Ur(n){return n.expireTimeMillis-Date.now()>0}function Na(n){return{token:II(yI),error:n}}var La=class{constructor(e,t){this.app=e,this.heartbeatServiceProvider=t}_delete(){let{tokenObservers:e}=je(this.app);for(let t of e)Zd(this.app,t.next);return Promise.resolve()}};function AI(n,e){return new La(n,e)}function bI(n){return{getToken:e=>Va(n,e),getLimitedUseToken:()=>vI(n),addTokenListener:e=>EI(n,"INTERNAL",e),removeTokenListener:e=>Zd(n.app,e)}}var RI="@firebase/app-check",SI="0.10.0";var PI="app-check",Gd="app-check-internal";function CI(){Oe(new ye(PI,n=>{let e=n.getProvider("app").getImmediate(),t=n.getProvider("heartbeat");return AI(e,t)},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((n,e,t)=>{n.getProvider(Gd).initialize()})),Oe(new ye(Gd,n=>{let e=n.getProvider("app-check").getImmediate();return bI(e)},"PUBLIC").setInstantiationMode("EXPLICIT")),te(RI,SI)}CI();var kI="app-check";var jt=class{constructor(){return qt(kI)}};var DI=["localhost","0.0.0.0","127.0.0.1"],cR=typeof window<"u"&&DI.includes(window.location.hostname);function gf(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}var _f=gf,yf=new qe("auth","Firebase",gf());var as=new Ze("@firebase/auth");function NI(n,...e){as.logLevel<=G.WARN&&as.warn(`Auth (${tt}): ${n}`,...e)}function rs(n,...e){as.logLevel<=G.ERROR&&as.error(`Auth (${tt}): ${n}`,...e)}function Me(n,...e){throw sc(n,...e)}function ze(n,...e){return sc(n,...e)}function ic(n,e,t){let r=Object.assign(Object.assign({},_f()),{[e]:t});return new qe("auth","Firebase",r).create(e,{appName:n.name})}function Et(n){return ic(n,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function OI(n,e,t){let r=t;if(!(e instanceof r))throw r.name!==e.constructor.name&&Me(n,"argument-error"),ic(n,"argument-error",`Type of ${e.constructor.name} does not match expected instance.Did you pass a reference from a different Auth SDK?`)}function sc(n,...e){if(typeof n!="string"){let t=e[0],r=[...e.slice(1)];return r[0]&&(r[0].appName=n.name),n._errorFactory.create(t,...r)}return yf.create(n,...e)}function B(n,e,...t){if(!n)throw sc(e,...t)}function It(n){let e="INTERNAL ASSERTION FAILED: "+n;throw rs(e),new Error(e)}function Tt(n,e){n||It(e)}function Ua(){var n;return typeof self<"u"&&((n=self.location)===null||n===void 0?void 0:n.href)||""}function xI(){return ef()==="http:"||ef()==="https:"}function ef(){var n;return typeof self<"u"&&((n=self.location)===null||n===void 0?void 0:n.protocol)||null}function VI(){return typeof navigator<"u"&&navigator&&"onLine"in navigator&&typeof navigator.onLine=="boolean"&&(xI()||Ed()||"connection"in navigator)?navigator.onLine:!0}function LI(){if(typeof navigator>"u")return null;let n=navigator;return n.languages&&n.languages[0]||n.language||null}var hn=class{constructor(e,t){this.shortDelay=e,this.longDelay=t,Tt(t>e,"Short delay should be less than long delay!"),this.isMobile=Id()||Td()}get(){return VI()?this.isMobile?this.longDelay:this.shortDelay:Math.min(5e3,this.shortDelay)}};function oc(n,e){Tt(n.emulator,"Emulator should always be set here");let{url:t}=n.emulator;return e?`${t}${e.startsWith("/")?e.slice(1):e}`:t}var cs=class{static initialize(e,t,r){this.fetchImpl=e,t&&(this.headersImpl=t),r&&(this.responseImpl=r)}static fetch(){if(this.fetchImpl)return this.fetchImpl;if(typeof self<"u"&&"fetch"in self)return self.fetch;if(typeof globalThis<"u"&&globalThis.fetch)return globalThis.fetch;if(typeof fetch<"u")return fetch;It("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}static headers(){if(this.headersImpl)return this.headersImpl;if(typeof self<"u"&&"Headers"in self)return self.Headers;if(typeof globalThis<"u"&&globalThis.Headers)return globalThis.Headers;if(typeof Headers<"u")return Headers;It("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}static response(){if(this.responseImpl)return this.responseImpl;if(typeof self<"u"&&"Response"in self)return self.Response;if(typeof globalThis<"u"&&globalThis.Response)return globalThis.Response;if(typeof Response<"u")return Response;It("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}};var MI={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"};var FI=["/v1/accounts:signInWithCustomToken","/v1/accounts:signInWithEmailLink","/v1/accounts:signInWithIdp","/v1/accounts:signInWithPassword","/v1/accounts:signInWithPhoneNumber","/v1/token"],UI=new hn(3e4,6e4);function Gt(n,e){return n.tenantId&&!e.tenantId?Object.assign(Object.assign({},e),{tenantId:n.tenantId}):e}function Ht(s,a,c,l){return g(this,arguments,function*(n,e,t,r,i={}){return If(n,i,()=>g(null,null,function*(){let d={},p={};r&&(e==="GET"?p=r:d={body:JSON.stringify(r)});let _=Nn(Object.assign({key:n.config.apiKey},p)).slice(1),I=yield n._getAdditionalHeaders();I["Content-Type"]="application/json",n.languageCode&&(I["X-Firebase-Locale"]=n.languageCode);let R=Object.assign({method:e,headers:I},d);return vd()||(R.referrerPolicy="no-referrer"),n.emulatorConfig&&Je(n.emulatorConfig.host)&&(R.credentials="include"),cs.fetch()(yield vf(n,n.config.apiHost,t,_),R)}))})}function If(n,e,t){return g(this,null,function*(){n._canInitEmulator=!1;let r=Object.assign(Object.assign({},MI),e);try{let i=new Ba(n),s=yield Promise.race([t(),i.promise]);i.clearNetworkTimeout();let a=yield s.json();if("needConfirmation"in a)throw ns(n,"account-exists-with-different-credential",a);if(s.ok&&!("errorMessage"in a))return a;{let c=s.ok?a.errorMessage:a.error.message,[l,d]=c.split(" : ");if(l==="FEDERATED_USER_ID_ALREADY_LINKED")throw ns(n,"credential-already-in-use",a);if(l==="EMAIL_EXISTS")throw ns(n,"email-already-in-use",a);if(l==="USER_DISABLED")throw ns(n,"user-disabled",a);let p=r[l]||l.toLowerCase().replace(/[_\s]+/g,"-");if(d)throw ic(n,p,d);Me(n,p)}}catch(i){if(i instanceof ke)throw i;Me(n,"network-request-failed",{message:String(i)})}})}function Kr(s,a,c,l){return g(this,arguments,function*(n,e,t,r,i={}){let d=yield Ht(n,e,t,r,i);return"mfaPendingCredential"in d&&Me(n,"multi-factor-auth-required",{_serverResponse:d}),d})}function vf(n,e,t,r){return g(this,null,function*(){let i=`${e}${t}?${r}`,s=n,a=s.config.emulator?oc(n.config,i):`${n.config.apiScheme}://${i}`;return FI.includes(t)&&(yield s._persistenceManagerAvailable,s._getPersistenceType()==="COOKIE")?s._getPersistence()._getFinalTarget(a).toString():a})}function BI(n){switch(n){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}}var Ba=class{clearNetworkTimeout(){clearTimeout(this.timer)}constructor(e){this.auth=e,this.timer=null,this.promise=new Promise((t,r)=>{this.timer=setTimeout(()=>r(ze(this.auth,"network-request-failed")),UI.get())})}};function ns(n,e,t){let r={appName:n.name};t.email&&(r.email=t.email),t.phoneNumber&&(r.phoneNumber=t.phoneNumber);let i=ze(n,e,r);return i.customData._tokenResponse=t,i}function tf(n){return n!==void 0&&n.enterprise!==void 0}var qa=class{constructor(e){if(this.siteKey="",this.recaptchaEnforcementState=[],e.recaptchaKey===void 0)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}getProviderEnforcementState(e){if(!this.recaptchaEnforcementState||this.recaptchaEnforcementState.length===0)return null;for(let t of this.recaptchaEnforcementState)if(t.provider&&t.provider===e)return BI(t.enforcementState);return null}isProviderEnabled(e){return this.getProviderEnforcementState(e)==="ENFORCE"||this.getProviderEnforcementState(e)==="AUDIT"}isAnyProviderEnabled(){return this.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")||this.isProviderEnabled("PHONE_PROVIDER")}};function qI(n,e){return g(this,null,function*(){return Ht(n,"GET","/v2/recaptchaConfig",Gt(n,e))})}function $I(n,e){return g(this,null,function*(){return Ht(n,"POST","/v1/accounts:delete",e)})}function us(n,e){return g(this,null,function*(){return Ht(n,"POST","/v1/accounts:lookup",e)})}function qr(n){if(n)try{let e=new Date(Number(n));if(!isNaN(e.getTime()))return e.toUTCString()}catch{}}function ac(n,e=!1){return g(this,null,function*(){let t=ee(n),r=yield t.getIdToken(e),i=cc(r);B(i&&i.exp&&i.auth_time&&i.iat,t.auth,"internal-error");let s=typeof i.firebase=="object"?i.firebase:void 0,a=s==null?void 0:s.sign_in_provider;return{claims:i,token:r,authTime:qr(Ma(i.auth_time)),issuedAtTime:qr(Ma(i.iat)),expirationTime:qr(Ma(i.exp)),signInProvider:a||null,signInSecondFactor:(s==null?void 0:s.sign_in_second_factor)||null}})}function Ma(n){return Number(n)*1e3}function cc(n){let[e,t,r]=n.split(".");if(e===void 0||t===void 0||r===void 0)return rs("JWT malformed, contained fewer than 3 sections"),null;try{let i=Hi(t);return i?JSON.parse(i):(rs("Failed to decode base64 JWT payload"),null)}catch(i){return rs("Caught error parsing JWT payload as JSON",i==null?void 0:i.toString()),null}}function nf(n){let e=cc(n);return B(e,"internal-error"),B(typeof e.exp<"u","internal-error"),B(typeof e.iat<"u","internal-error"),Number(e.exp)-Number(e.iat)}function jr(n,e,t=!1){return g(this,null,function*(){if(t)return e;try{return yield e}catch(r){throw r instanceof ke&&jI(r)&&n.auth.currentUser===n&&(yield n.auth.signOut()),r}})}function jI({code:n}){return n==="auth/user-disabled"||n==="auth/user-token-expired"}var $a=class{constructor(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,this.timerId!==null&&clearTimeout(this.timerId))}getInterval(e){var t;if(e){let r=this.errorBackoff;return this.errorBackoff=Math.min(this.errorBackoff*2,96e4),r}else{this.errorBackoff=3e4;let i=((t=this.user.stsTokenManager.expirationTime)!==null&&t!==void 0?t:0)-Date.now()-3e5;return Math.max(0,i)}}schedule(e=!1){if(!this.isRunning)return;let t=this.getInterval(e);this.timerId=setTimeout(()=>g(this,null,function*(){yield this.iteration()}),t)}iteration(){return g(this,null,function*(){try{yield this.user.getIdToken(!0)}catch(e){(e==null?void 0:e.code)==="auth/network-request-failed"&&this.schedule(!0);return}this.schedule()})}};var zr=class{constructor(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}_initializeTime(){this.lastSignInTime=qr(this.lastLoginAt),this.creationTime=qr(this.createdAt)}_copy(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}};function ls(n){return g(this,null,function*(){var e;let t=n.auth,r=yield n.getIdToken(),i=yield jr(n,us(t,{idToken:r}));B(i==null?void 0:i.users.length,t,"internal-error");let s=i.users[0];n._notifyReloadListener(s);let a=!((e=s.providerUserInfo)===null||e===void 0)&&e.length?Ef(s.providerUserInfo):[],c=zI(n.providerData,a),l=n.isAnonymous,d=!(n.email&&s.passwordHash)&&!(c!=null&&c.length),p=l?d:!1,_={uid:s.localId,displayName:s.displayName||null,photoURL:s.photoUrl||null,email:s.email||null,emailVerified:s.emailVerified||!1,phoneNumber:s.phoneNumber||null,tenantId:s.tenantId||null,providerData:c,metadata:new zr(s.createdAt,s.lastLoginAt),isAnonymous:p};Object.assign(n,_)})}function uc(n){return g(this,null,function*(){let e=ee(n);yield ls(e),yield e.auth._persistUserIfCurrent(e),e.auth._notifyListenersIfCurrent(e)})}function zI(n,e){return[...n.filter(r=>!e.some(i=>i.providerId===r.providerId)),...e]}function Ef(n){return n.map(e=>{var{providerId:t}=e,r=ji(e,["providerId"]);return{providerId:t,uid:r.rawId||"",displayName:r.displayName||null,email:r.email||null,phoneNumber:r.phoneNumber||null,photoURL:r.photoUrl||null}})}function WI(n,e){return g(this,null,function*(){let t=yield If(n,{},()=>g(null,null,function*(){let r=Nn({grant_type:"refresh_token",refresh_token:e}).slice(1),{tokenApiHost:i,apiKey:s}=n.config,a=yield vf(n,i,"/v1/token",`key=${s}`),c=yield n._getAdditionalHeaders();return c["Content-Type"]="application/x-www-form-urlencoded",cs.fetch()(a,{method:"POST",headers:c,body:r})}));return{accessToken:t.access_token,expiresIn:t.expires_in,refreshToken:t.refresh_token}})}function GI(n,e){return g(this,null,function*(){return Ht(n,"POST","/v2/accounts:revokeToken",Gt(n,e))})}var $r=class n{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(e){B(e.idToken,"internal-error"),B(typeof e.idToken<"u","internal-error"),B(typeof e.refreshToken<"u","internal-error");let t="expiresIn"in e&&typeof e.expiresIn<"u"?Number(e.expiresIn):nf(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)}updateFromIdToken(e){B(e.length!==0,"internal-error");let t=nf(e);this.updateTokensAndExpiration(e,null,t)}getToken(e,t=!1){return g(this,null,function*(){return!t&&this.accessToken&&!this.isExpired?this.accessToken:(B(this.refreshToken,e,"user-token-expired"),this.refreshToken?(yield this.refresh(e,this.refreshToken),this.accessToken):null)})}clearRefreshToken(){this.refreshToken=null}refresh(e,t){return g(this,null,function*(){let{accessToken:r,refreshToken:i,expiresIn:s}=yield WI(e,t);this.updateTokensAndExpiration(r,i,Number(s))})}updateTokensAndExpiration(e,t,r){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+r*1e3}static fromJSON(e,t){let{refreshToken:r,accessToken:i,expirationTime:s}=t,a=new n;return r&&(B(typeof r=="string","internal-error",{appName:e}),a.refreshToken=r),i&&(B(typeof i=="string","internal-error",{appName:e}),a.accessToken=i),s&&(B(typeof s=="number","internal-error",{appName:e}),a.expirationTime=s),a}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime}_clone(){return Object.assign(new n,this.toJSON())}_performRefresh(){return It("not implemented")}};function zt(n,e){B(typeof n=="string"||typeof n>"u","internal-error",{appName:e})}var Wt=class n{constructor(e){var{uid:t,auth:r,stsTokenManager:i}=e,s=ji(e,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new $a(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=t,this.auth=r,this.stsTokenManager=i,this.accessToken=i.accessToken,this.displayName=s.displayName||null,this.email=s.email||null,this.emailVerified=s.emailVerified||!1,this.phoneNumber=s.phoneNumber||null,this.photoURL=s.photoURL||null,this.isAnonymous=s.isAnonymous||!1,this.tenantId=s.tenantId||null,this.providerData=s.providerData?[...s.providerData]:[],this.metadata=new zr(s.createdAt||void 0,s.lastLoginAt||void 0)}getIdToken(e){return g(this,null,function*(){let t=yield jr(this,this.stsTokenManager.getToken(this.auth,e));return B(t,this.auth,"internal-error"),this.accessToken!==t&&(this.accessToken=t,yield this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),t})}getIdTokenResult(e){return ac(this,e)}reload(){return uc(this)}_assign(e){this!==e&&(B(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map(t=>Object.assign({},t)),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))}_clone(e){let t=new n(Object.assign(Object.assign({},this),{auth:e,stsTokenManager:this.stsTokenManager._clone()}));return t.metadata._copy(this.metadata),t}_onReload(e){B(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}_updateTokensIfNecessary(e,t=!1){return g(this,null,function*(){let r=!1;e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),r=!0),t&&(yield ls(this)),yield this.auth._persistUserIfCurrent(this),r&&this.auth._notifyListenersIfCurrent(this)})}delete(){return g(this,null,function*(){if(Ie(this.auth.app))return Promise.reject(Et(this.auth));let e=yield this.getIdToken();return yield jr(this,$I(this.auth,{idToken:e})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut()})}toJSON(){return Object.assign(Object.assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(e=>Object.assign({},e)),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(e,t){var r,i,s,a,c,l,d,p;let _=(r=t.displayName)!==null&&r!==void 0?r:void 0,I=(i=t.email)!==null&&i!==void 0?i:void 0,R=(s=t.phoneNumber)!==null&&s!==void 0?s:void 0,k=(a=t.photoURL)!==null&&a!==void 0?a:void 0,O=(c=t.tenantId)!==null&&c!==void 0?c:void 0,D=(l=t._redirectEventId)!==null&&l!==void 0?l:void 0,q=(d=t.createdAt)!==null&&d!==void 0?d:void 0,L=(p=t.lastLoginAt)!==null&&p!==void 0?p:void 0,{uid:F,emailVerified:z,isAnonymous:ue,providerData:Q,stsTokenManager:T}=t;B(F&&T,e,"internal-error");let m=$r.fromJSON(this.name,T);B(typeof F=="string",e,"internal-error"),zt(_,e.name),zt(I,e.name),B(typeof z=="boolean",e,"internal-error"),B(typeof ue=="boolean",e,"internal-error"),zt(R,e.name),zt(k,e.name),zt(O,e.name),zt(D,e.name),zt(q,e.name),zt(L,e.name);let v=new n({uid:F,auth:e,email:I,emailVerified:z,displayName:_,isAnonymous:ue,photoURL:k,phoneNumber:R,tenantId:O,stsTokenManager:m,createdAt:q,lastLoginAt:L});return Q&&Array.isArray(Q)&&(v.providerData=Q.map(E=>Object.assign({},E))),D&&(v._redirectEventId=D),v}static _fromIdTokenResponse(e,t,r=!1){return g(this,null,function*(){let i=new $r;i.updateFromServerResponse(t);let s=new n({uid:t.localId,auth:e,stsTokenManager:i,isAnonymous:r});return yield ls(s),s})}static _fromGetAccountInfoResponse(e,t,r){return g(this,null,function*(){let i=t.users[0];B(i.localId!==void 0,"internal-error");let s=i.providerUserInfo!==void 0?Ef(i.providerUserInfo):[],a=!(i.email&&i.passwordHash)&&!(s!=null&&s.length),c=new $r;c.updateFromIdToken(r);let l=new n({uid:i.localId,auth:e,stsTokenManager:c,isAnonymous:a}),d={uid:i.localId,displayName:i.displayName||null,photoURL:i.photoUrl||null,email:i.email||null,emailVerified:i.emailVerified||!1,phoneNumber:i.phoneNumber||null,tenantId:i.tenantId||null,providerData:s,metadata:new zr(i.createdAt,i.lastLoginAt),isAnonymous:!(i.email&&i.passwordHash)&&!(s!=null&&s.length)};return Object.assign(l,d),l})}};var rf=new Map;function vt(n){Tt(n instanceof Function,"Expected a class definition");let e=rf.get(n);return e?(Tt(e instanceof n,"Instance stored in cache mismatched with class"),e):(e=new n,rf.set(n,e),e)}var HI=(()=>{class n{constructor(){this.type="NONE",this.storage={}}_isAvailable(){return g(this,null,function*(){return!0})}_set(t,r){return g(this,null,function*(){this.storage[t]=r})}_get(t){return g(this,null,function*(){let r=this.storage[t];return r===void 0?null:r})}_remove(t){return g(this,null,function*(){delete this.storage[t]})}_addListener(t,r){}_removeListener(t,r){}}return n.type="NONE",n})(),ja=HI;function is(n,e,t){return`firebase:${n}:${e}:${t}`}var hs=class n{constructor(e,t,r){this.persistence=e,this.auth=t,this.userKey=r;let{config:i,name:s}=this.auth;this.fullUserKey=is(this.userKey,i.apiKey,s),this.fullPersistenceKey=is("persistence",i.apiKey,s),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(e){return this.persistence._set(this.fullUserKey,e.toJSON())}getCurrentUser(){return g(this,null,function*(){let e=yield this.persistence._get(this.fullUserKey);if(!e)return null;if(typeof e=="string"){let t=yield us(this.auth,{idToken:e}).catch(()=>{});return t?Wt._fromGetAccountInfoResponse(this.auth,t,e):null}return Wt._fromJSON(this.auth,e)})}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}setPersistence(e){return g(this,null,function*(){if(this.persistence===e)return;let t=yield this.getCurrentUser();if(yield this.removeCurrentUser(),this.persistence=e,t)return this.setCurrentUser(t)})}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static create(e,t,r="authUser"){return g(this,null,function*(){if(!t.length)return new n(vt(ja),e,r);let i=(yield Promise.all(t.map(d=>g(null,null,function*(){if(yield d._isAvailable())return d})))).filter(d=>d),s=i[0]||vt(ja),a=is(r,e.config.apiKey,e.name),c=null;for(let d of t)try{let p=yield d._get(a);if(p){let _;if(typeof p=="string"){let I=yield us(e,{idToken:p}).catch(()=>{});if(!I)break;_=yield Wt._fromGetAccountInfoResponse(e,I,p)}else _=Wt._fromJSON(e,p);d!==s&&(c=_),s=d;break}}catch{}let l=i.filter(d=>d._shouldAllowMigration);return!s._shouldAllowMigration||!l.length?new n(s,e,r):(s=l[0],c&&(yield s._set(a,c.toJSON())),yield Promise.all(t.map(d=>g(null,null,function*(){if(d!==s)try{yield d._remove(a)}catch{}}))),new n(s,e,r))})}};function sf(n){let e=n.toLowerCase();if(e.includes("opera/")||e.includes("opr/")||e.includes("opios/"))return"Opera";if(bf(e))return"IEMobile";if(e.includes("msie")||e.includes("trident/"))return"IE";if(e.includes("edge/"))return"Edge";if(Tf(e))return"Firefox";if(e.includes("silk/"))return"Silk";if(Sf(e))return"Blackberry";if(Pf(e))return"Webos";if(wf(e))return"Safari";if((e.includes("chrome/")||Af(e))&&!e.includes("edge/"))return"Chrome";if(Rf(e))return"Android";{let t=/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/,r=n.match(t);if((r==null?void 0:r.length)===2)return r[1]}return"Other"}function Tf(n=fe()){return/firefox\//i.test(n)}function wf(n=fe()){let e=n.toLowerCase();return e.includes("safari/")&&!e.includes("chrome/")&&!e.includes("crios/")&&!e.includes("android")}function Af(n=fe()){return/crios\//i.test(n)}function bf(n=fe()){return/iemobile/i.test(n)}function Rf(n=fe()){return/android/i.test(n)}function Sf(n=fe()){return/blackberry/i.test(n)}function Pf(n=fe()){return/webos/i.test(n)}function lc(n=fe()){return/iphone|ipad|ipod/i.test(n)||/macintosh/i.test(n)&&/mobile/i.test(n)}function KI(n=fe()){var e;return lc(n)&&!!(!((e=window.navigator)===null||e===void 0)&&e.standalone)}function QI(){return wd()&&document.documentMode===10}function Cf(n=fe()){return lc(n)||Rf(n)||Pf(n)||Sf(n)||/windows phone/i.test(n)||bf(n)}function kf(n,e=[]){let t;switch(n){case"Browser":t=sf(fe());break;case"Worker":t=`${sf(fe())}-${n}`;break;default:t=n}let r=e.length?e.join(","):"FirebaseCore-web";return`${t}/JsCore/${tt}/${r}`}var za=class{constructor(e){this.auth=e,this.queue=[]}pushCallback(e,t){let r=s=>new Promise((a,c)=>{try{let l=e(s);a(l)}catch(l){c(l)}});r.onAbort=t,this.queue.push(r);let i=this.queue.length-1;return()=>{this.queue[i]=()=>Promise.resolve()}}runMiddleware(e){return g(this,null,function*(){if(this.auth.currentUser===e)return;let t=[];try{for(let r of this.queue)yield r(e),r.onAbort&&t.push(r.onAbort)}catch(r){t.reverse();for(let i of t)try{i()}catch{}throw this.auth._errorFactory.create("login-blocked",{originalMessage:r==null?void 0:r.message})}})}};function YI(t){return g(this,arguments,function*(n,e={}){return Ht(n,"GET","/v2/passwordPolicy",Gt(n,e))})}var JI=6,Wa=class{constructor(e){var t,r,i,s;let a=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=(t=a.minPasswordLength)!==null&&t!==void 0?t:JI,a.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=a.maxPasswordLength),a.containsLowercaseCharacter!==void 0&&(this.customStrengthOptions.containsLowercaseLetter=a.containsLowercaseCharacter),a.containsUppercaseCharacter!==void 0&&(this.customStrengthOptions.containsUppercaseLetter=a.containsUppercaseCharacter),a.containsNumericCharacter!==void 0&&(this.customStrengthOptions.containsNumericCharacter=a.containsNumericCharacter),a.containsNonAlphanumericCharacter!==void 0&&(this.customStrengthOptions.containsNonAlphanumericCharacter=a.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,this.enforcementState==="ENFORCEMENT_STATE_UNSPECIFIED"&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=(i=(r=e.allowedNonAlphanumericCharacters)===null||r===void 0?void 0:r.join(""))!==null&&i!==void 0?i:"",this.forceUpgradeOnSignin=(s=e.forceUpgradeOnSignin)!==null&&s!==void 0?s:!1,this.schemaVersion=e.schemaVersion}validatePassword(e){var t,r,i,s,a,c;let l={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,l),this.validatePasswordCharacterOptions(e,l),l.isValid&&(l.isValid=(t=l.meetsMinPasswordLength)!==null&&t!==void 0?t:!0),l.isValid&&(l.isValid=(r=l.meetsMaxPasswordLength)!==null&&r!==void 0?r:!0),l.isValid&&(l.isValid=(i=l.containsLowercaseLetter)!==null&&i!==void 0?i:!0),l.isValid&&(l.isValid=(s=l.containsUppercaseLetter)!==null&&s!==void 0?s:!0),l.isValid&&(l.isValid=(a=l.containsNumericCharacter)!==null&&a!==void 0?a:!0),l.isValid&&(l.isValid=(c=l.containsNonAlphanumericCharacter)!==null&&c!==void 0?c:!0),l}validatePasswordLengthOptions(e,t){let r=this.customStrengthOptions.minPasswordLength,i=this.customStrengthOptions.maxPasswordLength;r&&(t.meetsMinPasswordLength=e.length>=r),i&&(t.meetsMaxPasswordLength=e.length<=i)}validatePasswordCharacterOptions(e,t){this.updatePasswordCharacterOptionsStatuses(t,!1,!1,!1,!1);let r;for(let i=0;i<e.length;i++)r=e.charAt(i),this.updatePasswordCharacterOptionsStatuses(t,r>="a"&&r<="z",r>="A"&&r<="Z",r>="0"&&r<="9",this.allowedNonAlphanumericCharacters.includes(r))}updatePasswordCharacterOptionsStatuses(e,t,r,i,s){this.customStrengthOptions.containsLowercaseLetter&&(e.containsLowercaseLetter||(e.containsLowercaseLetter=t)),this.customStrengthOptions.containsUppercaseLetter&&(e.containsUppercaseLetter||(e.containsUppercaseLetter=r)),this.customStrengthOptions.containsNumericCharacter&&(e.containsNumericCharacter||(e.containsNumericCharacter=i)),this.customStrengthOptions.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter||(e.containsNonAlphanumericCharacter=s))}};var Ga=class{constructor(e,t,r,i){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=r,this.config=i,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new ds(this),this.idTokenSubscription=new ds(this),this.beforeStateQueue=new za(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=yf,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this._resolvePersistenceManagerAvailable=void 0,this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=i.sdkClientVersion,this._persistenceManagerAvailable=new Promise(s=>this._resolvePersistenceManagerAvailable=s)}_initializeWithPersistence(e,t){return t&&(this._popupRedirectResolver=vt(t)),this._initializationPromise=this.queue(()=>g(this,null,function*(){var r,i,s;if(!this._deleted&&(this.persistenceManager=yield hs.create(this,e),(r=this._resolvePersistenceManagerAvailable)===null||r===void 0||r.call(this),!this._deleted)){if(!((i=this._popupRedirectResolver)===null||i===void 0)&&i._shouldInitProactively)try{yield this._popupRedirectResolver._initialize(this)}catch{}yield this.initializeCurrentUser(t),this.lastNotifiedUid=((s=this.currentUser)===null||s===void 0?void 0:s.uid)||null,!this._deleted&&(this._isInitialized=!0)}})),this._initializationPromise}_onStorageEvent(){return g(this,null,function*(){if(this._deleted)return;let e=yield this.assertedPersistence.getCurrentUser();if(!(!this.currentUser&&!e)){if(this.currentUser&&e&&this.currentUser.uid===e.uid){this._currentUser._assign(e),yield this.currentUser.getIdToken();return}yield this._updateCurrentUser(e,!0)}})}initializeCurrentUserFromIdToken(e){return g(this,null,function*(){try{let t=yield us(this,{idToken:e}),r=yield Wt._fromGetAccountInfoResponse(this,t,e);yield this.directlySetCurrentUser(r)}catch(t){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",t),yield this.directlySetCurrentUser(null)}})}initializeCurrentUser(e){return g(this,null,function*(){var t;if(Ie(this.app)){let a=this.app.settings.authIdToken;return a?new Promise(c=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(a).then(c,c))}):this.directlySetCurrentUser(null)}let r=yield this.assertedPersistence.getCurrentUser(),i=r,s=!1;if(e&&this.config.authDomain){yield this.getOrInitRedirectPersistenceManager();let a=(t=this.redirectUser)===null||t===void 0?void 0:t._redirectEventId,c=i==null?void 0:i._redirectEventId,l=yield this.tryRedirectSignIn(e);(!a||a===c)&&(l!=null&&l.user)&&(i=l.user,s=!0)}if(!i)return this.directlySetCurrentUser(null);if(!i._redirectEventId){if(s)try{yield this.beforeStateQueue.runMiddleware(i)}catch(a){i=r,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(a))}return i?this.reloadAndSetCurrentUserOrClear(i):this.directlySetCurrentUser(null)}return B(this._popupRedirectResolver,this,"argument-error"),yield this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===i._redirectEventId?this.directlySetCurrentUser(i):this.reloadAndSetCurrentUserOrClear(i)})}tryRedirectSignIn(e){return g(this,null,function*(){let t=null;try{t=yield this._popupRedirectResolver._completeRedirectFn(this,e,!0)}catch{yield this._setRedirectUser(null)}return t})}reloadAndSetCurrentUserOrClear(e){return g(this,null,function*(){try{yield ls(e)}catch(t){if((t==null?void 0:t.code)!=="auth/network-request-failed")return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(e)})}useDeviceLanguage(){this.languageCode=LI()}_delete(){return g(this,null,function*(){this._deleted=!0})}updateCurrentUser(e){return g(this,null,function*(){if(Ie(this.app))return Promise.reject(Et(this));let t=e?ee(e):null;return t&&B(t.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(t&&t._clone(this))})}_updateCurrentUser(e,t=!1){return g(this,null,function*(){if(!this._deleted)return e&&B(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t||(yield this.beforeStateQueue.runMiddleware(e)),this.queue(()=>g(this,null,function*(){yield this.directlySetCurrentUser(e),this.notifyAuthListeners()}))})}signOut(){return g(this,null,function*(){return Ie(this.app)?Promise.reject(Et(this)):(yield this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&(yield this._setRedirectUser(null)),this._updateCurrentUser(null,!0))})}setPersistence(e){return Ie(this.app)?Promise.reject(Et(this)):this.queue(()=>g(this,null,function*(){yield this.assertedPersistence.setPersistence(vt(e))}))}_getRecaptchaConfig(){return this.tenantId==null?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}validatePassword(e){return g(this,null,function*(){this._getPasswordPolicyInternal()||(yield this._updatePasswordPolicy());let t=this._getPasswordPolicyInternal();return t.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):t.validatePassword(e)})}_getPasswordPolicyInternal(){return this.tenantId===null?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}_updatePasswordPolicy(){return g(this,null,function*(){let e=yield YI(this),t=new Wa(e);this.tenantId===null?this._projectPasswordPolicy=t:this._tenantPasswordPolicies[this.tenantId]=t})}_getPersistenceType(){return this.assertedPersistence.persistence.type}_getPersistence(){return this.assertedPersistence.persistence}_updateErrorMap(e){this._errorFactory=new qe("auth","Firebase",e())}onAuthStateChanged(e,t,r){return this.registerStateListener(this.authStateSubscription,e,t,r)}beforeAuthStateChanged(e,t){return this.beforeStateQueue.pushCallback(e,t)}onIdTokenChanged(e,t,r){return this.registerStateListener(this.idTokenSubscription,e,t,r)}authStateReady(){return new Promise((e,t)=>{if(this.currentUser)e();else{let r=this.onAuthStateChanged(()=>{r(),e()},t)}})}revokeAccessToken(e){return g(this,null,function*(){if(this.currentUser){let t=yield this.currentUser.getIdToken(),r={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:t};this.tenantId!=null&&(r.tenantId=this.tenantId),yield GI(this,r)}})}toJSON(){var e;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:(e=this._currentUser)===null||e===void 0?void 0:e.toJSON()}}_setRedirectUser(e,t){return g(this,null,function*(){let r=yield this.getOrInitRedirectPersistenceManager(t);return e===null?r.removeCurrentUser():r.setCurrentUser(e)})}getOrInitRedirectPersistenceManager(e){return g(this,null,function*(){if(!this.redirectPersistenceManager){let t=e&&vt(e)||this._popupRedirectResolver;B(t,this,"argument-error"),this.redirectPersistenceManager=yield hs.create(this,[vt(t._redirectPersistence)],"redirectUser"),this.redirectUser=yield this.redirectPersistenceManager.getCurrentUser()}return this.redirectPersistenceManager})}_redirectUserForId(e){return g(this,null,function*(){var t,r;return this._isInitialized&&(yield this.queue(()=>g(this,null,function*(){}))),((t=this._currentUser)===null||t===void 0?void 0:t._redirectEventId)===e?this._currentUser:((r=this.redirectUser)===null||r===void 0?void 0:r._redirectEventId)===e?this.redirectUser:null})}_persistUserIfCurrent(e){return g(this,null,function*(){if(e===this.currentUser)return this.queue(()=>g(this,null,function*(){return this.directlySetCurrentUser(e)}))})}_notifyListenersIfCurrent(e){e===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:${this.name}`}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var e,t;if(!this._isInitialized)return;this.idTokenSubscription.next(this.currentUser);let r=(t=(e=this.currentUser)===null||e===void 0?void 0:e.uid)!==null&&t!==void 0?t:null;this.lastNotifiedUid!==r&&(this.lastNotifiedUid=r,this.authStateSubscription.next(this.currentUser))}registerStateListener(e,t,r,i){if(this._deleted)return()=>{};let s=typeof t=="function"?t:t.next.bind(t),a=!1,c=this._isInitialized?Promise.resolve():this._initializationPromise;if(B(c,this,"internal-error"),c.then(()=>{a||s(this.currentUser)}),typeof t=="function"){let l=e.addObserver(t,r,i);return()=>{a=!0,l()}}else{let l=e.addObserver(t);return()=>{a=!0,l()}}}directlySetCurrentUser(e){return g(this,null,function*(){this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),this.currentUser=e,e?yield this.assertedPersistence.setCurrentUser(e):yield this.assertedPersistence.removeCurrentUser()})}queue(e){return this.operations=this.operations.then(e,e),this.operations}get assertedPersistence(){return B(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(e){!e||this.frameworks.includes(e)||(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=kf(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}_getAdditionalHeaders(){return g(this,null,function*(){var e;let t={"X-Client-Version":this.clientVersion};this.app.options.appId&&(t["X-Firebase-gmpid"]=this.app.options.appId);let r=yield(e=this.heartbeatServiceProvider.getImmediate({optional:!0}))===null||e===void 0?void 0:e.getHeartbeatsHeader();r&&(t["X-Firebase-Client"]=r);let i=yield this._getAppCheckToken();return i&&(t["X-Firebase-AppCheck"]=i),t})}_getAppCheckToken(){return g(this,null,function*(){var e;if(Ie(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let t=yield(e=this.appCheckServiceProvider.getImmediate({optional:!0}))===null||e===void 0?void 0:e.getToken();return t!=null&&t.error&&NI(`Error while retrieving App Check token: ${t.error}`),t==null?void 0:t.token})}};function Kt(n){return ee(n)}var ds=class{constructor(e){this.auth=e,this.observer=null,this.addObserver=Sd(t=>this.observer=t)}get next(){return B(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}};var Es={loadJS(){return g(this,null,function*(){throw new Error("Unable to load external scripts")})},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function XI(n){Es=n}function Df(n){return Es.loadJS(n)}function ZI(){return Es.recaptchaEnterpriseScript}function ev(){return Es.gapiScript}function Nf(n){return`__${n}${Math.floor(Math.random()*1e6)}`}var Ha=class{constructor(){this.enterprise=new Ka}ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}},Ka=class{ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}};var tv="recaptcha-enterprise",Of="NO_RECAPTCHA",Qa=class{constructor(e){this.type=tv,this.auth=Kt(e)}verify(e="verify",t=!1){return g(this,null,function*(){function r(s){return g(this,null,function*(){if(!t){if(s.tenantId==null&&s._agentRecaptchaConfig!=null)return s._agentRecaptchaConfig.siteKey;if(s.tenantId!=null&&s._tenantRecaptchaConfigs[s.tenantId]!==void 0)return s._tenantRecaptchaConfigs[s.tenantId].siteKey}return new Promise((a,c)=>g(null,null,function*(){qI(s,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(l=>{if(l.recaptchaKey===void 0)c(new Error("recaptcha Enterprise site key undefined"));else{let d=new qa(l);return s.tenantId==null?s._agentRecaptchaConfig=d:s._tenantRecaptchaConfigs[s.tenantId]=d,a(d.siteKey)}}).catch(l=>{c(l)})}))})}function i(s,a,c){let l=window.grecaptcha;tf(l)?l.enterprise.ready(()=>{l.enterprise.execute(s,{action:e}).then(d=>{a(d)}).catch(()=>{a(Of)})}):c(Error("No reCAPTCHA enterprise script loaded."))}return this.auth.settings.appVerificationDisabledForTesting?new Ha().execute("siteKey",{action:"verify"}):new Promise((s,a)=>{r(this.auth).then(c=>{if(!t&&tf(window.grecaptcha))i(c,s,a);else{if(typeof window>"u"){a(new Error("RecaptchaVerifier is only supported in browser"));return}let l=ZI();l.length!==0&&(l+=c),Df(l).then(()=>{i(c,s,a)}).catch(d=>{a(d)})}}).catch(c=>{a(c)})})})}};function Br(n,e,t,r=!1,i=!1){return g(this,null,function*(){let s=new Qa(n),a;if(i)a=Of;else try{a=yield s.verify(t)}catch{a=yield s.verify(t,!0)}let c=Object.assign({},e);if(t==="mfaSmsEnrollment"||t==="mfaSmsSignIn"){if("phoneEnrollmentInfo"in c){let l=c.phoneEnrollmentInfo.phoneNumber,d=c.phoneEnrollmentInfo.recaptchaToken;Object.assign(c,{phoneEnrollmentInfo:{phoneNumber:l,recaptchaToken:d,captchaResponse:a,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})}else if("phoneSignInInfo"in c){let l=c.phoneSignInInfo.recaptchaToken;Object.assign(c,{phoneSignInInfo:{recaptchaToken:l,captchaResponse:a,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})}return c}return r?Object.assign(c,{captchaResp:a}):Object.assign(c,{captchaResponse:a}),Object.assign(c,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(c,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"}),c})}function Ya(n,e,t,r,i){return g(this,null,function*(){var s,a;if(i==="EMAIL_PASSWORD_PROVIDER")if(!((s=n._getRecaptchaConfig())===null||s===void 0)&&s.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")){let c=yield Br(n,e,t,t==="getOobCode");return r(n,c)}else return r(n,e).catch(c=>g(null,null,function*(){if(c.code==="auth/missing-recaptcha-token"){console.log(`${t} is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow.`);let l=yield Br(n,e,t,t==="getOobCode");return r(n,l)}else return Promise.reject(c)}));else if(i==="PHONE_PROVIDER")if(!((a=n._getRecaptchaConfig())===null||a===void 0)&&a.isProviderEnabled("PHONE_PROVIDER")){let c=yield Br(n,e,t);return r(n,c).catch(l=>g(null,null,function*(){var d;if(((d=n._getRecaptchaConfig())===null||d===void 0?void 0:d.getProviderEnforcementState("PHONE_PROVIDER"))==="AUDIT"&&(l.code==="auth/missing-recaptcha-token"||l.code==="auth/invalid-app-credential")){console.log(`Failed to verify with reCAPTCHA Enterprise. Automatically triggering the reCAPTCHA v2 flow to complete the ${t} flow.`);let p=yield Br(n,e,t,!1,!0);return r(n,p)}return Promise.reject(l)}))}else{let c=yield Br(n,e,t,!1,!0);return r(n,c)}else return Promise.reject(i+" provider is not supported.")})}function hc(n,e){let t=Bt(n,"auth");if(t.isInitialized()){let i=t.getImmediate(),s=t.getOptions();if(Xe(s,e!=null?e:{}))return i;Me(i,"already-initialized")}return t.initialize({options:e})}function nv(n,e){let t=(e==null?void 0:e.persistence)||[],r=(Array.isArray(t)?t:[t]).map(vt);e!=null&&e.errorMap&&n._updateErrorMap(e.errorMap),n._initializeWithPersistence(r,e==null?void 0:e.popupRedirectResolver)}function dc(n,e,t){let r=Kt(n);B(/^https?:\/\//.test(e),r,"invalid-emulator-scheme");let i=!!(t!=null&&t.disableWarnings),s=xf(e),{host:a,port:c}=rv(e),l=c===null?"":`:${c}`,d={url:`${s}//${a}${l}/`},p=Object.freeze({host:a,port:c,protocol:s.replace(":",""),options:Object.freeze({disableWarnings:i})});if(!r._canInitEmulator){B(r.config.emulator&&r.emulatorConfig,r,"emulator-config-failed"),B(Xe(d,r.config.emulator)&&Xe(p,r.emulatorConfig),r,"emulator-config-failed");return}r.config.emulator=d,r.emulatorConfig=p,r.settings.appVerificationDisabledForTesting=!0,Je(a)?(kn(`${s}//${a}${l}`),Dn("Auth",!0)):i||iv()}function xf(n){let e=n.indexOf(":");return e<0?"":n.substr(0,e+1)}function rv(n){let e=xf(n),t=/(\/\/)?([^?#/]+)/.exec(n.substr(e.length));if(!t)return{host:"",port:null};let r=t[2].split("@").pop()||"",i=/^(\[[^\]]+\])(:|$)/.exec(r);if(i){let s=i[1];return{host:s,port:of(r.substr(s.length+1))}}else{let[s,a]=r.split(":");return{host:s,port:of(a)}}}function of(n){if(!n)return null;let e=Number(n);return isNaN(e)?null:e}function iv(){function n(){let e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}typeof console<"u"&&typeof console.info=="function"&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials."),typeof window<"u"&&typeof document<"u"&&(document.readyState==="loading"?window.addEventListener("DOMContentLoaded",n):n())}var Mn=class{constructor(e,t){this.providerId=e,this.signInMethod=t}toJSON(){return It("not implemented")}_getIdTokenResponse(e){return It("not implemented")}_linkToIdToken(e,t){return It("not implemented")}_getReauthenticationResolver(e){return It("not implemented")}};function sv(n,e){return g(this,null,function*(){return Ht(n,"POST","/v1/accounts:signUp",e)})}function ov(n,e){return g(this,null,function*(){return Kr(n,"POST","/v1/accounts:signInWithPassword",Gt(n,e))})}function av(n,e){return g(this,null,function*(){return Kr(n,"POST","/v1/accounts:signInWithEmailLink",Gt(n,e))})}function cv(n,e){return g(this,null,function*(){return Kr(n,"POST","/v1/accounts:signInWithEmailLink",Gt(n,e))})}var Wr=class n extends Mn{constructor(e,t,r,i=null){super("password",r),this._email=e,this._password=t,this._tenantId=i}static _fromEmailAndPassword(e,t){return new n(e,t,"password")}static _fromEmailAndCode(e,t,r=null){return new n(e,t,"emailLink",r)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(e){let t=typeof e=="string"?JSON.parse(e):e;if(t!=null&&t.email&&(t!=null&&t.password)){if(t.signInMethod==="password")return this._fromEmailAndPassword(t.email,t.password);if(t.signInMethod==="emailLink")return this._fromEmailAndCode(t.email,t.password,t.tenantId)}return null}_getIdTokenResponse(e){return g(this,null,function*(){switch(this.signInMethod){case"password":let t={returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"};return Ya(e,t,"signInWithPassword",ov,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return av(e,{email:this._email,oobCode:this._password});default:Me(e,"internal-error")}})}_linkToIdToken(e,t){return g(this,null,function*(){switch(this.signInMethod){case"password":let r={idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"};return Ya(e,r,"signUpPassword",sv,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return cv(e,{idToken:t,email:this._email,oobCode:this._password});default:Me(e,"internal-error")}})}_getReauthenticationResolver(e){return this._getIdTokenResponse(e)}};function Ln(n,e){return g(this,null,function*(){return Kr(n,"POST","/v1/accounts:signInWithIdp",Gt(n,e))})}var uv="http://localhost",fs=class n extends Mn{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(e){let t=new n(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):Me("argument-error"),t}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(e){let t=typeof e=="string"?JSON.parse(e):e,{providerId:r,signInMethod:i}=t,s=ji(t,["providerId","signInMethod"]);if(!r||!i)return null;let a=new n(r,i);return a.idToken=s.idToken||void 0,a.accessToken=s.accessToken||void 0,a.secret=s.secret,a.nonce=s.nonce,a.pendingToken=s.pendingToken||null,a}_getIdTokenResponse(e){let t=this.buildRequest();return Ln(e,t)}_linkToIdToken(e,t){let r=this.buildRequest();return r.idToken=t,Ln(e,r)}_getReauthenticationResolver(e){let t=this.buildRequest();return t.autoCreate=!1,Ln(e,t)}buildRequest(){let e={requestUri:uv,returnSecureToken:!0};if(this.pendingToken)e.pendingToken=this.pendingToken;else{let t={};this.idToken&&(t.id_token=this.idToken),this.accessToken&&(t.access_token=this.accessToken),this.secret&&(t.oauth_token_secret=this.secret),t.providerId=this.providerId,this.nonce&&!this.pendingToken&&(t.nonce=this.nonce),e.postBody=Nn(t)}return e}};function lv(n){switch(n){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}}function hv(n){let e=On(xn(n)).link,t=e?On(xn(e)).deep_link_id:null,r=On(xn(n)).deep_link_id;return(r?On(xn(r)).link:null)||r||t||e||n}var ps=class n{constructor(e){var t,r,i,s,a,c;let l=On(xn(e)),d=(t=l.apiKey)!==null&&t!==void 0?t:null,p=(r=l.oobCode)!==null&&r!==void 0?r:null,_=lv((i=l.mode)!==null&&i!==void 0?i:null);B(d&&p&&_,"argument-error"),this.apiKey=d,this.operation=_,this.code=p,this.continueUrl=(s=l.continueUrl)!==null&&s!==void 0?s:null,this.languageCode=(a=l.lang)!==null&&a!==void 0?a:null,this.tenantId=(c=l.tenantId)!==null&&c!==void 0?c:null}static parseLink(e){let t=hv(e);try{return new n(t)}catch{return null}}};var Vf=(()=>{class n{constructor(){this.providerId=n.PROVIDER_ID}static credential(t,r){return Wr._fromEmailAndPassword(t,r)}static credentialWithLink(t,r){let i=ps.parseLink(r);return B(i,"argument-error"),Wr._fromEmailAndCode(t,i.code,i.tenantId)}}n.PROVIDER_ID="password",n.EMAIL_PASSWORD_SIGN_IN_METHOD="password",n.EMAIL_LINK_SIGN_IN_METHOD="emailLink";return n})(),Gr=class{constructor(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(e){this.defaultLanguageCode=e}setCustomParameters(e){return this.customParameters=e,this}getCustomParameters(){return this.customParameters}};var ms=class extends Gr{constructor(){super(...arguments),this.scopes=[]}addScope(e){return this.scopes.includes(e)||this.scopes.push(e),this}getScopes(){return[...this.scopes]}};var dv=(()=>{class n extends ms{constructor(){super("google.com"),this.addScope("profile")}static credential(t,r){return fs._fromParams({providerId:n.PROVIDER_ID,signInMethod:n.GOOGLE_SIGN_IN_METHOD,idToken:t,accessToken:r})}static credentialFromResult(t){return n.credentialFromTaggedObject(t)}static credentialFromError(t){return n.credentialFromTaggedObject(t.customData||{})}static credentialFromTaggedObject({_tokenResponse:t}){if(!t)return null;let{oauthIdToken:r,oauthAccessToken:i}=t;if(!r&&!i)return null;try{return n.credential(r,i)}catch{return null}}}n.GOOGLE_SIGN_IN_METHOD="google.com",n.PROVIDER_ID="google.com";return n})();function fv(n,e){return g(this,null,function*(){return Kr(n,"POST","/v1/accounts:signUp",Gt(n,e))})}var Fn=class n{constructor(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}static _fromIdTokenResponse(e,t,r,i=!1){return g(this,null,function*(){let s=yield Wt._fromIdTokenResponse(e,r,i),a=af(r);return new n({user:s,providerId:a,_tokenResponse:r,operationType:t})})}static _forOperation(e,t,r){return g(this,null,function*(){yield e._updateTokensIfNecessary(r,!0);let i=af(r);return new n({user:e,providerId:i,_tokenResponse:r,operationType:t})})}};function af(n){return n.providerId?n.providerId:"phoneNumber"in n?"phone":null}var Ja=class n extends ke{constructor(e,t,r,i){var s;super(t.code,t.message),this.operationType=r,this.user=i,Object.setPrototypeOf(this,n.prototype),this.customData={appName:e.name,tenantId:(s=e.tenantId)!==null&&s!==void 0?s:void 0,_serverResponse:t.customData._serverResponse,operationType:r}}static _fromErrorAndOperation(e,t,r,i){return new n(e,t,r,i)}};function Lf(n,e,t,r){return(e==="reauthenticate"?t._getReauthenticationResolver(n):t._getIdTokenResponse(n)).catch(s=>{throw s.code==="auth/multi-factor-auth-required"?Ja._fromErrorAndOperation(n,s,e,r):s})}function pv(n,e,t=!1){return g(this,null,function*(){let r=yield jr(n,e._linkToIdToken(n.auth,yield n.getIdToken()),t);return Fn._forOperation(n,"link",r)})}function mv(n,e,t=!1){return g(this,null,function*(){let{auth:r}=n;if(Ie(r.app))return Promise.reject(Et(r));let i="reauthenticate";try{let s=yield jr(n,Lf(r,i,e,n),t);B(s.idToken,r,"internal-error");let a=cc(s.idToken);B(a,r,"internal-error");let{sub:c}=a;return B(n.uid===c,r,"user-mismatch"),Fn._forOperation(n,i,s)}catch(s){throw(s==null?void 0:s.code)==="auth/user-not-found"&&Me(r,"user-mismatch"),s}})}function Mf(n,e,t=!1){return g(this,null,function*(){if(Ie(n.app))return Promise.reject(Et(n));let r="signIn",i=yield Lf(n,r,e),s=yield Fn._fromIdTokenResponse(n,r,i);return t||(yield n._updateCurrentUser(s.user)),s})}function fc(n,e){return g(this,null,function*(){return Mf(Kt(n),e)})}function Ff(n){return g(this,null,function*(){let e=Kt(n);e._getPasswordPolicyInternal()&&(yield e._updatePasswordPolicy())})}function pc(n,e,t){return g(this,null,function*(){if(Ie(n.app))return Promise.reject(Et(n));let r=Kt(n),a=yield Ya(r,{returnSecureToken:!0,email:e,password:t,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",fv,"EMAIL_PASSWORD_PROVIDER").catch(l=>{throw l.code==="auth/password-does-not-meet-requirements"&&Ff(n),l}),c=yield Fn._fromIdTokenResponse(r,"signIn",a);return yield r._updateCurrentUser(c.user),c})}function mc(n,e,t){return Ie(n.app)?Promise.reject(Et(n)):fc(ee(n),Vf.credential(e,t)).catch(r=>g(null,null,function*(){throw r.code==="auth/password-does-not-meet-requirements"&&Ff(n),r}))}function gc(n,e,t,r){return ee(n).onIdTokenChanged(e,t,r)}function _c(n,e,t){return ee(n).beforeAuthStateChanged(e,t)}function yc(n,e,t,r){return ee(n).onAuthStateChanged(e,t,r)}function Ic(n){return ee(n).signOut()}var gs="__sak";var _s=class{constructor(e,t){this.storageRetriever=e,this.type=t}_isAvailable(){try{return this.storage?(this.storage.setItem(gs,"1"),this.storage.removeItem(gs),Promise.resolve(!0)):Promise.resolve(!1)}catch{return Promise.resolve(!1)}}_set(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()}_get(e){let t=this.storage.getItem(e);return Promise.resolve(t?JSON.parse(t):null)}_remove(e){return this.storage.removeItem(e),Promise.resolve()}get storage(){return this.storageRetriever()}};var gv=1e3,_v=10,yv=(()=>{class n extends _s{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(t,r)=>this.onStorageEvent(t,r),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=Cf(),this._shouldAllowMigration=!0}forAllChangedKeys(t){for(let r of Object.keys(this.listeners)){let i=this.storage.getItem(r),s=this.localCache[r];i!==s&&t(r,s,i)}}onStorageEvent(t,r=!1){if(!t.key){this.forAllChangedKeys((c,l,d)=>{this.notifyListeners(c,d)});return}let i=t.key;r?this.detachListener():this.stopPolling();let s=()=>{let c=this.storage.getItem(i);!r&&this.localCache[i]===c||this.notifyListeners(i,c)},a=this.storage.getItem(i);QI()&&a!==t.newValue&&t.newValue!==t.oldValue?setTimeout(s,_v):s()}notifyListeners(t,r){this.localCache[t]=r;let i=this.listeners[t];if(i)for(let s of Array.from(i))s(r&&JSON.parse(r))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((t,r,i)=>{this.onStorageEvent(new StorageEvent("storage",{key:t,oldValue:r,newValue:i}),!0)})},gv)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(t,r){Object.keys(this.listeners).length===0&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[t]||(this.listeners[t]=new Set,this.localCache[t]=this.storage.getItem(t)),this.listeners[t].add(r)}_removeListener(t,r){this.listeners[t]&&(this.listeners[t].delete(r),this.listeners[t].size===0&&delete this.listeners[t]),Object.keys(this.listeners).length===0&&(this.detachListener(),this.stopPolling())}_set(t,r){return g(this,null,function*(){yield Lt(n.prototype,this,"_set").call(this,t,r),this.localCache[t]=JSON.stringify(r)})}_get(t){return g(this,null,function*(){let r=yield Lt(n.prototype,this,"_get").call(this,t);return this.localCache[t]=JSON.stringify(r),r})}_remove(t){return g(this,null,function*(){yield Lt(n.prototype,this,"_remove").call(this,t),delete this.localCache[t]})}}return n.type="LOCAL",n})(),Uf=yv;var Iv=(()=>{class n extends _s{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(t,r){}_removeListener(t,r){}}return n.type="SESSION",n})(),vc=Iv;function vv(n){return Promise.all(n.map(e=>g(null,null,function*(){try{return{fulfilled:!0,value:yield e}}catch(t){return{fulfilled:!1,reason:t}}})))}var Ev=(()=>{class n{constructor(t){this.eventTarget=t,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(t){let r=this.receivers.find(s=>s.isListeningto(t));if(r)return r;let i=new n(t);return this.receivers.push(i),i}isListeningto(t){return this.eventTarget===t}handleEvent(t){return g(this,null,function*(){let r=t,{eventId:i,eventType:s,data:a}=r.data,c=this.handlersMap[s];if(!(c!=null&&c.size))return;r.ports[0].postMessage({status:"ack",eventId:i,eventType:s});let l=Array.from(c).map(p=>g(this,null,function*(){return p(r.origin,a)})),d=yield vv(l);r.ports[0].postMessage({status:"done",eventId:i,eventType:s,response:d})})}_subscribe(t,r){Object.keys(this.handlersMap).length===0&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[t]||(this.handlersMap[t]=new Set),this.handlersMap[t].add(r)}_unsubscribe(t,r){this.handlersMap[t]&&r&&this.handlersMap[t].delete(r),(!r||this.handlersMap[t].size===0)&&delete this.handlersMap[t],Object.keys(this.handlersMap).length===0&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}n.receivers=[];return n})();function Ec(n="",e=10){let t="";for(let r=0;r<e;r++)t+=Math.floor(Math.random()*10);return n+t}var Xa=class{constructor(e){this.target=e,this.handlers=new Set}removeMessageHandler(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)}_send(e,t,r=50){return g(this,null,function*(){let i=typeof MessageChannel<"u"?new MessageChannel:null;if(!i)throw new Error("connection_unavailable");let s,a;return new Promise((c,l)=>{let d=Ec("",20);i.port1.start();let p=setTimeout(()=>{l(new Error("unsupported_event"))},r);a={messageChannel:i,onMessage(_){let I=_;if(I.data.eventId===d)switch(I.data.status){case"ack":clearTimeout(p),s=setTimeout(()=>{l(new Error("timeout"))},3e3);break;case"done":clearTimeout(s),c(I.data.response);break;default:clearTimeout(p),clearTimeout(s),l(new Error("invalid_response"));break}}},this.handlers.add(a),i.port1.addEventListener("message",a.onMessage),this.target.postMessage({eventType:e,eventId:d,data:t},[i.port2])}).finally(()=>{a&&this.removeMessageHandler(a)})})}};function it(){return window}function Tv(n){it().location.href=n}function Bf(){return typeof it().WorkerGlobalScope<"u"&&typeof it().importScripts=="function"}function wv(){return g(this,null,function*(){if(!(navigator!=null&&navigator.serviceWorker))return null;try{return(yield navigator.serviceWorker.ready).active}catch{return null}})}function Av(){var n;return((n=navigator==null?void 0:navigator.serviceWorker)===null||n===void 0?void 0:n.controller)||null}function bv(){return Bf()?self:null}var qf="firebaseLocalStorageDb",Rv=1,ys="firebaseLocalStorage",$f="fbase_key",dn=class{constructor(e){this.request=e}toPromise(){return new Promise((e,t)=>{this.request.addEventListener("success",()=>{e(this.request.result)}),this.request.addEventListener("error",()=>{t(this.request.error)})})}};function Ts(n,e){return n.transaction([ys],e?"readwrite":"readonly").objectStore(ys)}function Sv(){let n=indexedDB.deleteDatabase(qf);return new dn(n).toPromise()}function Za(){let n=indexedDB.open(qf,Rv);return new Promise((e,t)=>{n.addEventListener("error",()=>{t(n.error)}),n.addEventListener("upgradeneeded",()=>{let r=n.result;try{r.createObjectStore(ys,{keyPath:$f})}catch(i){t(i)}}),n.addEventListener("success",()=>g(null,null,function*(){let r=n.result;r.objectStoreNames.contains(ys)?e(r):(r.close(),yield Sv(),e(yield Za()))}))})}function cf(n,e,t){return g(this,null,function*(){let r=Ts(n,!0).put({[$f]:e,value:t});return new dn(r).toPromise()})}function Pv(n,e){return g(this,null,function*(){let t=Ts(n,!1).get(e),r=yield new dn(t).toPromise();return r===void 0?null:r.value})}function uf(n,e){let t=Ts(n,!0).delete(e);return new dn(t).toPromise()}var Cv=800,kv=3,Dv=(()=>{class n{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}_openDb(){return g(this,null,function*(){return this.db?this.db:(this.db=yield Za(),this.db)})}_withRetries(t){return g(this,null,function*(){let r=0;for(;;)try{let i=yield this._openDb();return yield t(i)}catch(i){if(r++>kv)throw i;this.db&&(this.db.close(),this.db=void 0)}})}initializeServiceWorkerMessaging(){return g(this,null,function*(){return Bf()?this.initializeReceiver():this.initializeSender()})}initializeReceiver(){return g(this,null,function*(){this.receiver=Ev._getInstance(bv()),this.receiver._subscribe("keyChanged",(t,r)=>g(this,null,function*(){return{keyProcessed:(yield this._poll()).includes(r.key)}})),this.receiver._subscribe("ping",(t,r)=>g(this,null,function*(){return["keyChanged"]}))})}initializeSender(){return g(this,null,function*(){var t,r;if(this.activeServiceWorker=yield wv(),!this.activeServiceWorker)return;this.sender=new Xa(this.activeServiceWorker);let i=yield this.sender._send("ping",{},800);i&&!((t=i[0])===null||t===void 0)&&t.fulfilled&&!((r=i[0])===null||r===void 0)&&r.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0)})}notifyServiceWorker(t){return g(this,null,function*(){if(!(!this.sender||!this.activeServiceWorker||Av()!==this.activeServiceWorker))try{yield this.sender._send("keyChanged",{key:t},this.serviceWorkerReceiverAvailable?800:50)}catch{}})}_isAvailable(){return g(this,null,function*(){try{if(!indexedDB)return!1;let t=yield Za();return yield cf(t,gs,"1"),yield uf(t,gs),!0}catch{}return!1})}_withPendingWrite(t){return g(this,null,function*(){this.pendingWrites++;try{yield t()}finally{this.pendingWrites--}})}_set(t,r){return g(this,null,function*(){return this._withPendingWrite(()=>g(this,null,function*(){return yield this._withRetries(i=>cf(i,t,r)),this.localCache[t]=r,this.notifyServiceWorker(t)}))})}_get(t){return g(this,null,function*(){let r=yield this._withRetries(i=>Pv(i,t));return this.localCache[t]=r,r})}_remove(t){return g(this,null,function*(){return this._withPendingWrite(()=>g(this,null,function*(){return yield this._withRetries(r=>uf(r,t)),delete this.localCache[t],this.notifyServiceWorker(t)}))})}_poll(){return g(this,null,function*(){let t=yield this._withRetries(s=>{let a=Ts(s,!1).getAll();return new dn(a).toPromise()});if(!t)return[];if(this.pendingWrites!==0)return[];let r=[],i=new Set;if(t.length!==0)for(let{fbase_key:s,value:a}of t)i.add(s),JSON.stringify(this.localCache[s])!==JSON.stringify(a)&&(this.notifyListeners(s,a),r.push(s));for(let s of Object.keys(this.localCache))this.localCache[s]&&!i.has(s)&&(this.notifyListeners(s,null),r.push(s));return r})}notifyListeners(t,r){this.localCache[t]=r;let i=this.listeners[t];if(i)for(let s of Array.from(i))s(r)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>g(this,null,function*(){return this._poll()}),Cv)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(t,r){Object.keys(this.listeners).length===0&&this.startPolling(),this.listeners[t]||(this.listeners[t]=new Set,this._get(t)),this.listeners[t].add(r)}_removeListener(t,r){this.listeners[t]&&(this.listeners[t].delete(r),this.listeners[t].size===0&&delete this.listeners[t]),Object.keys(this.listeners).length===0&&this.stopPolling()}}return n.type="LOCAL",n})(),jf=Dv;var _R=Nf("rcb"),yR=new hn(3e4,6e4);function zf(n,e){return e?vt(e):(B(n._popupRedirectResolver,n,"argument-error"),n._popupRedirectResolver)}var Hr=class extends Mn{constructor(e){super("custom","custom"),this.params=e}_getIdTokenResponse(e){return Ln(e,this._buildIdpRequest())}_linkToIdToken(e,t){return Ln(e,this._buildIdpRequest(t))}_getReauthenticationResolver(e){return Ln(e,this._buildIdpRequest())}_buildIdpRequest(e){let t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t}};function Nv(n){return Mf(n.auth,new Hr(n),n.bypassAuthState)}function Ov(n){let{auth:e,user:t}=n;return B(t,e,"internal-error"),mv(t,new Hr(n),n.bypassAuthState)}function xv(n){return g(this,null,function*(){let{auth:e,user:t}=n;return B(t,e,"internal-error"),pv(t,new Hr(n),n.bypassAuthState)})}var Is=class{constructor(e,t,r,i,s=!1){this.auth=e,this.resolver=r,this.user=i,this.bypassAuthState=s,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}execute(){return new Promise((e,t)=>g(this,null,function*(){this.pendingPromise={resolve:e,reject:t};try{this.eventManager=yield this.resolver._initialize(this.auth),yield this.onExecution(),this.eventManager.registerConsumer(this)}catch(r){this.reject(r)}}))}onAuthEvent(e){return g(this,null,function*(){let{urlResponse:t,sessionId:r,postBody:i,tenantId:s,error:a,type:c}=e;if(a){this.reject(a);return}let l={auth:this.auth,requestUri:t,sessionId:r,tenantId:s||void 0,postBody:i||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(yield this.getIdpTask(c)(l))}catch(d){this.reject(d)}})}onError(e){this.reject(e)}getIdpTask(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return Nv;case"linkViaPopup":case"linkViaRedirect":return xv;case"reauthViaPopup":case"reauthViaRedirect":return Ov;default:Me(this.auth,"internal-error")}}resolve(e){Tt(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()}reject(e){Tt(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}};var Vv=new hn(2e3,1e4);function Tc(n,e,t){return g(this,null,function*(){if(Ie(n.app))return Promise.reject(ze(n,"operation-not-supported-in-this-environment"));let r=Kt(n);OI(n,e,Gr);let i=zf(r,t);return new Lv(r,"signInViaPopup",e,i).executeNotNull()})}var Lv=(()=>{class n extends Is{constructor(t,r,i,s,a){super(t,r,s,a),this.provider=i,this.authWindow=null,this.pollId=null,n.currentPopupAction&&n.currentPopupAction.cancel(),n.currentPopupAction=this}executeNotNull(){return g(this,null,function*(){let t=yield this.execute();return B(t,this.auth,"internal-error"),t})}onExecution(){return g(this,null,function*(){Tt(this.filter.length===1,"Popup operations only handle one event");let t=Ec();this.authWindow=yield this.resolver._openPopup(this.auth,this.provider,this.filter[0],t),this.authWindow.associatedEvent=t,this.resolver._originValidation(this.auth).catch(r=>{this.reject(r)}),this.resolver._isIframeWebStorageSupported(this.auth,r=>{r||this.reject(ze(this.auth,"web-storage-unsupported"))}),this.pollUserCancellation()})}get eventId(){var t;return((t=this.authWindow)===null||t===void 0?void 0:t.associatedEvent)||null}cancel(){this.reject(ze(this.auth,"cancelled-popup-request"))}cleanUp(){this.authWindow&&this.authWindow.close(),this.pollId&&window.clearTimeout(this.pollId),this.authWindow=null,this.pollId=null,n.currentPopupAction=null}pollUserCancellation(){let t=()=>{var r,i;if(!((i=(r=this.authWindow)===null||r===void 0?void 0:r.window)===null||i===void 0)&&i.closed){this.pollId=window.setTimeout(()=>{this.pollId=null,this.reject(ze(this.auth,"popup-closed-by-user"))},8e3);return}this.pollId=window.setTimeout(t,Vv.get())};t()}}n.currentPopupAction=null;return n})(),Mv="pendingRedirect",ss=new Map,ec=class n extends Is{constructor(e,t,r=!1){super(e,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],t,void 0,r),this.eventId=null}execute(){return g(this,null,function*(){let e=ss.get(this.auth._key());if(!e){try{let r=(yield Fv(this.resolver,this.auth))?yield Lt(n.prototype,this,"execute").call(this):null;e=()=>Promise.resolve(r)}catch(t){e=()=>Promise.reject(t)}ss.set(this.auth._key(),e)}return this.bypassAuthState||ss.set(this.auth._key(),()=>Promise.resolve(null)),e()})}onAuthEvent(e){return g(this,null,function*(){if(e.type==="signInViaRedirect")return Lt(n.prototype,this,"onAuthEvent").call(this,e);if(e.type==="unknown"){this.resolve(null);return}if(e.eventId){let t=yield this.auth._redirectUserForId(e.eventId);if(t)return this.user=t,Lt(n.prototype,this,"onAuthEvent").call(this,e);this.resolve(null)}})}onExecution(){return g(this,null,function*(){})}cleanUp(){}};function Fv(n,e){return g(this,null,function*(){let t=qv(e),r=Bv(n);if(!(yield r._isAvailable()))return!1;let i=(yield r._get(t))==="true";return yield r._remove(t),i})}function Uv(n,e){ss.set(n._key(),e)}function Bv(n){return vt(n._redirectPersistence)}function qv(n){return is(Mv,n.config.apiKey,n.name)}function $v(n,e,t=!1){return g(this,null,function*(){if(Ie(n.app))return Promise.reject(Et(n));let r=Kt(n),i=zf(r,e),a=yield new ec(r,i,t).execute();return a&&!t&&(delete a.user._redirectEventId,yield r._persistUserIfCurrent(a.user),yield r._setRedirectUser(null,e)),a})}var jv=10*60*1e3,tc=class{constructor(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(e){this.consumers.delete(e)}onEvent(e){if(this.hasEventBeenHandled(e))return!1;let t=!1;return this.consumers.forEach(r=>{this.isEventForConsumer(e,r)&&(t=!0,this.sendToConsumer(e,r),this.saveEventToCache(e))}),this.hasHandledPotentialRedirect||!zv(e)||(this.hasHandledPotentialRedirect=!0,t||(this.queuedRedirectEvent=e,t=!0)),t}sendToConsumer(e,t){var r;if(e.error&&!Wf(e)){let i=((r=e.error.code)===null||r===void 0?void 0:r.split("auth/")[1])||"internal-error";t.onError(ze(this.auth,i))}else t.onAuthEvent(e)}isEventForConsumer(e,t){let r=t.eventId===null||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&r}hasEventBeenHandled(e){return Date.now()-this.lastProcessedEventTime>=jv&&this.cachedEventUids.clear(),this.cachedEventUids.has(lf(e))}saveEventToCache(e){this.cachedEventUids.add(lf(e)),this.lastProcessedEventTime=Date.now()}};function lf(n){return[n.type,n.eventId,n.sessionId,n.tenantId].filter(e=>e).join("-")}function Wf({type:n,error:e}){return n==="unknown"&&(e==null?void 0:e.code)==="auth/no-auth-event"}function zv(n){switch(n.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return!0;case"unknown":return Wf(n);default:return!1}}function Wv(t){return g(this,arguments,function*(n,e={}){return Ht(n,"GET","/v1/projects",e)})}var Gv=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,Hv=/^https?/;function Kv(n){return g(this,null,function*(){if(n.config.emulator)return;let{authorizedDomains:e}=yield Wv(n);for(let t of e)try{if(Qv(t))return}catch{}Me(n,"unauthorized-domain")})}function Qv(n){let e=Ua(),{protocol:t,hostname:r}=new URL(e);if(n.startsWith("chrome-extension://")){let a=new URL(n);return a.hostname===""&&r===""?t==="chrome-extension:"&&n.replace("chrome-extension://","")===e.replace("chrome-extension://",""):t==="chrome-extension:"&&a.hostname===r}if(!Hv.test(t))return!1;if(Gv.test(n))return r===n;let i=n.replace(/\./g,"\\.");return new RegExp("^(.+\\."+i+"|"+i+")$","i").test(r)}var Yv=new hn(3e4,6e4);function hf(){let n=it().___jsl;if(n!=null&&n.H){for(let e of Object.keys(n.H))if(n.H[e].r=n.H[e].r||[],n.H[e].L=n.H[e].L||[],n.H[e].r=[...n.H[e].L],n.CP)for(let t=0;t<n.CP.length;t++)n.CP[t]=null}}function Jv(n){return new Promise((e,t)=>{var r,i,s;function a(){hf(),gapi.load("gapi.iframes",{callback:()=>{e(gapi.iframes.getContext())},ontimeout:()=>{hf(),t(ze(n,"network-request-failed"))},timeout:Yv.get()})}if(!((i=(r=it().gapi)===null||r===void 0?void 0:r.iframes)===null||i===void 0)&&i.Iframe)e(gapi.iframes.getContext());else if(!((s=it().gapi)===null||s===void 0)&&s.load)a();else{let c=Nf("iframefcb");return it()[c]=()=>{gapi.load?a():t(ze(n,"network-request-failed"))},Df(`${ev()}?onload=${c}`).catch(l=>t(l))}}).catch(e=>{throw os=null,e})}var os=null;function Xv(n){return os=os||Jv(n),os}var Zv=new hn(5e3,15e3),eE="__/auth/iframe",tE="emulator/auth/iframe",nE={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},rE=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);function iE(n){let e=n.config;B(e.authDomain,n,"auth-domain-config-required");let t=e.emulator?oc(e,tE):`https://${n.config.authDomain}/${eE}`,r={apiKey:e.apiKey,appName:n.name,v:tt},i=rE.get(n.config.apiHost);i&&(r.eid=i);let s=n._getFrameworks();return s.length&&(r.fw=s.join(",")),`${t}?${Nn(r).slice(1)}`}function sE(n){return g(this,null,function*(){let e=yield Xv(n),t=it().gapi;return B(t,n,"internal-error"),e.open({where:document.body,url:iE(n),messageHandlersFilter:t.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:nE,dontclear:!0},r=>new Promise((i,s)=>g(null,null,function*(){yield r.restyle({setHideOnLeave:!1});let a=ze(n,"network-request-failed"),c=it().setTimeout(()=>{s(a)},Zv.get());function l(){it().clearTimeout(c),i(r)}r.ping(l).then(l,()=>{s(a)})})))})}var oE={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"},aE=500,cE=600,uE="_blank",lE="http://localhost",vs=class{constructor(e){this.window=e,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch{}}};function hE(n,e,t,r=aE,i=cE){let s=Math.max((window.screen.availHeight-i)/2,0).toString(),a=Math.max((window.screen.availWidth-r)/2,0).toString(),c="",l=Object.assign(Object.assign({},oE),{width:r.toString(),height:i.toString(),top:s,left:a}),d=fe().toLowerCase();t&&(c=Af(d)?uE:t),Tf(d)&&(e=e||lE,l.scrollbars="yes");let p=Object.entries(l).reduce((I,[R,k])=>`${I}${R}=${k},`,"");if(KI(d)&&c!=="_self")return dE(e||"",c),new vs(null);let _=window.open(e||"",c,p);B(_,n,"popup-blocked");try{_.focus()}catch{}return new vs(_)}function dE(n,e){let t=document.createElement("a");t.href=n,t.target=e;let r=document.createEvent("MouseEvent");r.initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),t.dispatchEvent(r)}var fE="__/auth/handler",pE="emulator/auth/handler",mE=encodeURIComponent("fac");function df(n,e,t,r,i,s){return g(this,null,function*(){B(n.config.authDomain,n,"auth-domain-config-required"),B(n.config.apiKey,n,"invalid-api-key");let a={apiKey:n.config.apiKey,appName:n.name,authType:t,redirectUrl:r,v:tt,eventId:i};if(e instanceof Gr){e.setDefaultLanguage(n.languageCode),a.providerId=e.providerId||"",Rd(e.getCustomParameters())||(a.customParameters=JSON.stringify(e.getCustomParameters()));for(let[p,_]of Object.entries(s||{}))a[p]=_}if(e instanceof ms){let p=e.getScopes().filter(_=>_!=="");p.length>0&&(a.scopes=p.join(","))}n.tenantId&&(a.tid=n.tenantId);let c=a;for(let p of Object.keys(c))c[p]===void 0&&delete c[p];let l=yield n._getAppCheckToken(),d=l?`#${mE}=${encodeURIComponent(l)}`:"";return`${gE(n)}?${Nn(c).slice(1)}${d}`})}function gE({config:n}){return n.emulator?oc(n,pE):`https://${n.authDomain}/${fE}`}var Fa="webStorageSupport",nc=class{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=vc,this._completeRedirectFn=$v,this._overrideRedirectResult=Uv}_openPopup(e,t,r,i){return g(this,null,function*(){var s;Tt((s=this.eventManagers[e._key()])===null||s===void 0?void 0:s.manager,"_initialize() not called before _openPopup()");let a=yield df(e,t,r,Ua(),i);return hE(e,a,Ec())})}_openRedirect(e,t,r,i){return g(this,null,function*(){yield this._originValidation(e);let s=yield df(e,t,r,Ua(),i);return Tv(s),new Promise(()=>{})})}_initialize(e){let t=e._key();if(this.eventManagers[t]){let{manager:i,promise:s}=this.eventManagers[t];return i?Promise.resolve(i):(Tt(s,"If manager is not set, promise should be"),s)}let r=this.initAndGetManager(e);return this.eventManagers[t]={promise:r},r.catch(()=>{delete this.eventManagers[t]}),r}initAndGetManager(e){return g(this,null,function*(){let t=yield sE(e),r=new tc(e);return t.register("authEvent",i=>(B(i==null?void 0:i.authEvent,e,"invalid-auth-event"),{status:r.onEvent(i.authEvent)?"ACK":"ERROR"}),gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[e._key()]={manager:r},this.iframes[e._key()]=t,r})}_isIframeWebStorageSupported(e,t){this.iframes[e._key()].send(Fa,{type:Fa},i=>{var s;let a=(s=i==null?void 0:i[0])===null||s===void 0?void 0:s[Fa];a!==void 0&&t(!!a),Me(e,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(e){let t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=Kv(e)),this.originValidationPromises[t]}get _shouldInitProactively(){return Cf()||wf()||lc()}},Gf=nc;var ff="@firebase/auth",pf="1.10.6";var rc=class{constructor(e){this.auth=e,this.internalListeners=new Map}getUid(){var e;return this.assertAuthConfigured(),((e=this.auth.currentUser)===null||e===void 0?void 0:e.uid)||null}getToken(e){return g(this,null,function*(){return this.assertAuthConfigured(),yield this.auth._initializationPromise,this.auth.currentUser?{accessToken:yield this.auth.currentUser.getIdToken(e)}:null})}addAuthTokenListener(e){if(this.assertAuthConfigured(),this.internalListeners.has(e))return;let t=this.auth.onIdTokenChanged(r=>{e((r==null?void 0:r.stsTokenManager.accessToken)||null)});this.internalListeners.set(e,t),this.updateProactiveRefresh()}removeAuthTokenListener(e){this.assertAuthConfigured();let t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())}assertAuthConfigured(){B(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){this.internalListeners.size>0?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}};function _E(n){switch(n){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}}function yE(n){Oe(new ye("auth",(e,{options:t})=>{let r=e.getProvider("app").getImmediate(),i=e.getProvider("heartbeat"),s=e.getProvider("app-check-internal"),{apiKey:a,authDomain:c}=r.options;B(a&&!a.includes(":"),"invalid-api-key",{appName:r.name});let l={apiKey:a,authDomain:c,clientPlatform:n,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:kf(n)},d=new Ga(r,i,s,l);return nv(d,t),d},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e,t,r)=>{e.getProvider("auth-internal").initialize()})),Oe(new ye("auth-internal",e=>{let t=Kt(e.getProvider("auth").getImmediate());return(r=>new rc(r))(t)},"PRIVATE").setInstantiationMode("EXPLICIT")),te(ff,pf,_E(n)),te(ff,pf,"esm2017")}var IE=5*60,vE=fa("authIdTokenMaxAge")||IE,mf=null,EE=n=>e=>g(null,null,function*(){let t=e&&(yield e.getIdTokenResult()),r=t&&(new Date().getTime()-Date.parse(t.issuedAtTime))/1e3;if(r&&r>vE)return;let i=t==null?void 0:t.token;mf!==i&&(mf=i,yield fetch(n,{method:i?"POST":"DELETE",headers:i?{Authorization:`Bearer ${i}`}:{}}))});function wc(n=_t()){let e=Bt(n,"auth");if(e.isInitialized())return e.getImmediate();let t=hc(n,{popupRedirectResolver:Gf,persistence:[jf,Uf,vc]}),r=fa("authTokenSyncURL");if(r&&typeof isSecureContext=="boolean"&&isSecureContext){let s=new URL(r,location.origin);if(location.origin===s.origin){let a=EE(s.toString());_c(t,a,()=>a(t.currentUser)),gc(t,c=>a(c))}}let i=ha("auth");return i&&dc(t,`http://${i}`),t}function TE(){var n,e;return(e=(n=document.getElementsByTagName("head"))===null||n===void 0?void 0:n[0])!==null&&e!==void 0?e:document}XI({loadJS(n){return new Promise((e,t)=>{let r=document.createElement("script");r.setAttribute("src",n),r.onload=e,r.onerror=i=>{let s=ze("internal-error");s.customData=i,t(s)},r.type="text/javascript",r.charset="UTF-8",TE().appendChild(r)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="});yE("Browser");var Hf="auth",Qr=class{constructor(e){return e}},fn=class{constructor(){return qt(Hf)}};var Ac=new Mt("angularfire2.auth-instances");function uT(n,e){let t=ln(Hf,n,e);return t&&new Qr(t)}function lT(n){return(e,t)=>{let r=e.runOutsideAngular(()=>n(t));return new Qr(r)}}var hT={provide:fn,deps:[[new _e,Ac]]},dT={provide:Qr,useFactory:uT,deps:[[new _e,Ac],$e]};function nS(n,...e){return te("angularfire",nt.full,"auth"),Ft([dT,hT,{provide:Ac,useFactory:lT(n),multi:!0,deps:[Le,Ye,rt,yt,[new _e,jt],...e]}])}var rS=J(pc,!0,2);var iS=J(wc,!0);var sS=J(yc,!0);var oS=J(mc,!0,2);var aS=J(Tc,!0,2);var cS=J(Ic,!0,2);var Kf=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Qf={};var wt,bc;(function(){var n;function e(T,m){function v(){}v.prototype=m.prototype,T.D=m.prototype,T.prototype=new v,T.prototype.constructor=T,T.C=function(E,w,b){for(var y=Array(arguments.length-2),dt=2;dt<arguments.length;dt++)y[dt-2]=arguments[dt];return m.prototype[w].apply(E,y)}}function t(){this.blockSize=-1}function r(){this.blockSize=-1,this.blockSize=64,this.g=Array(4),this.B=Array(this.blockSize),this.o=this.h=0,this.s()}e(r,t),r.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0};function i(T,m,v){v||(v=0);var E=Array(16);if(typeof m=="string")for(var w=0;16>w;++w)E[w]=m.charCodeAt(v++)|m.charCodeAt(v++)<<8|m.charCodeAt(v++)<<16|m.charCodeAt(v++)<<24;else for(w=0;16>w;++w)E[w]=m[v++]|m[v++]<<8|m[v++]<<16|m[v++]<<24;m=T.g[0],v=T.g[1],w=T.g[2];var b=T.g[3],y=m+(b^v&(w^b))+E[0]+3614090360&4294967295;m=v+(y<<7&4294967295|y>>>25),y=b+(w^m&(v^w))+E[1]+3905402710&4294967295,b=m+(y<<12&4294967295|y>>>20),y=w+(v^b&(m^v))+E[2]+606105819&4294967295,w=b+(y<<17&4294967295|y>>>15),y=v+(m^w&(b^m))+E[3]+3250441966&4294967295,v=w+(y<<22&4294967295|y>>>10),y=m+(b^v&(w^b))+E[4]+4118548399&4294967295,m=v+(y<<7&4294967295|y>>>25),y=b+(w^m&(v^w))+E[5]+1200080426&4294967295,b=m+(y<<12&4294967295|y>>>20),y=w+(v^b&(m^v))+E[6]+2821735955&4294967295,w=b+(y<<17&4294967295|y>>>15),y=v+(m^w&(b^m))+E[7]+4249261313&4294967295,v=w+(y<<22&4294967295|y>>>10),y=m+(b^v&(w^b))+E[8]+1770035416&4294967295,m=v+(y<<7&4294967295|y>>>25),y=b+(w^m&(v^w))+E[9]+2336552879&4294967295,b=m+(y<<12&4294967295|y>>>20),y=w+(v^b&(m^v))+E[10]+4294925233&4294967295,w=b+(y<<17&4294967295|y>>>15),y=v+(m^w&(b^m))+E[11]+2304563134&4294967295,v=w+(y<<22&4294967295|y>>>10),y=m+(b^v&(w^b))+E[12]+1804603682&4294967295,m=v+(y<<7&4294967295|y>>>25),y=b+(w^m&(v^w))+E[13]+4254626195&4294967295,b=m+(y<<12&4294967295|y>>>20),y=w+(v^b&(m^v))+E[14]+2792965006&4294967295,w=b+(y<<17&4294967295|y>>>15),y=v+(m^w&(b^m))+E[15]+1236535329&4294967295,v=w+(y<<22&4294967295|y>>>10),y=m+(w^b&(v^w))+E[1]+4129170786&4294967295,m=v+(y<<5&4294967295|y>>>27),y=b+(v^w&(m^v))+E[6]+3225465664&4294967295,b=m+(y<<9&4294967295|y>>>23),y=w+(m^v&(b^m))+E[11]+643717713&4294967295,w=b+(y<<14&4294967295|y>>>18),y=v+(b^m&(w^b))+E[0]+3921069994&4294967295,v=w+(y<<20&4294967295|y>>>12),y=m+(w^b&(v^w))+E[5]+3593408605&4294967295,m=v+(y<<5&4294967295|y>>>27),y=b+(v^w&(m^v))+E[10]+38016083&4294967295,b=m+(y<<9&4294967295|y>>>23),y=w+(m^v&(b^m))+E[15]+3634488961&4294967295,w=b+(y<<14&4294967295|y>>>18),y=v+(b^m&(w^b))+E[4]+3889429448&4294967295,v=w+(y<<20&4294967295|y>>>12),y=m+(w^b&(v^w))+E[9]+568446438&4294967295,m=v+(y<<5&4294967295|y>>>27),y=b+(v^w&(m^v))+E[14]+3275163606&4294967295,b=m+(y<<9&4294967295|y>>>23),y=w+(m^v&(b^m))+E[3]+4107603335&4294967295,w=b+(y<<14&4294967295|y>>>18),y=v+(b^m&(w^b))+E[8]+1163531501&4294967295,v=w+(y<<20&4294967295|y>>>12),y=m+(w^b&(v^w))+E[13]+2850285829&4294967295,m=v+(y<<5&4294967295|y>>>27),y=b+(v^w&(m^v))+E[2]+4243563512&4294967295,b=m+(y<<9&4294967295|y>>>23),y=w+(m^v&(b^m))+E[7]+1735328473&4294967295,w=b+(y<<14&4294967295|y>>>18),y=v+(b^m&(w^b))+E[12]+2368359562&4294967295,v=w+(y<<20&4294967295|y>>>12),y=m+(v^w^b)+E[5]+4294588738&4294967295,m=v+(y<<4&4294967295|y>>>28),y=b+(m^v^w)+E[8]+2272392833&4294967295,b=m+(y<<11&4294967295|y>>>21),y=w+(b^m^v)+E[11]+1839030562&4294967295,w=b+(y<<16&4294967295|y>>>16),y=v+(w^b^m)+E[14]+4259657740&4294967295,v=w+(y<<23&4294967295|y>>>9),y=m+(v^w^b)+E[1]+2763975236&4294967295,m=v+(y<<4&4294967295|y>>>28),y=b+(m^v^w)+E[4]+1272893353&4294967295,b=m+(y<<11&4294967295|y>>>21),y=w+(b^m^v)+E[7]+4139469664&4294967295,w=b+(y<<16&4294967295|y>>>16),y=v+(w^b^m)+E[10]+3200236656&4294967295,v=w+(y<<23&4294967295|y>>>9),y=m+(v^w^b)+E[13]+681279174&4294967295,m=v+(y<<4&4294967295|y>>>28),y=b+(m^v^w)+E[0]+3936430074&4294967295,b=m+(y<<11&4294967295|y>>>21),y=w+(b^m^v)+E[3]+3572445317&4294967295,w=b+(y<<16&4294967295|y>>>16),y=v+(w^b^m)+E[6]+76029189&4294967295,v=w+(y<<23&4294967295|y>>>9),y=m+(v^w^b)+E[9]+3654602809&4294967295,m=v+(y<<4&4294967295|y>>>28),y=b+(m^v^w)+E[12]+3873151461&4294967295,b=m+(y<<11&4294967295|y>>>21),y=w+(b^m^v)+E[15]+530742520&4294967295,w=b+(y<<16&4294967295|y>>>16),y=v+(w^b^m)+E[2]+3299628645&4294967295,v=w+(y<<23&4294967295|y>>>9),y=m+(w^(v|~b))+E[0]+4096336452&4294967295,m=v+(y<<6&4294967295|y>>>26),y=b+(v^(m|~w))+E[7]+1126891415&4294967295,b=m+(y<<10&4294967295|y>>>22),y=w+(m^(b|~v))+E[14]+2878612391&4294967295,w=b+(y<<15&4294967295|y>>>17),y=v+(b^(w|~m))+E[5]+4237533241&4294967295,v=w+(y<<21&4294967295|y>>>11),y=m+(w^(v|~b))+E[12]+1700485571&4294967295,m=v+(y<<6&4294967295|y>>>26),y=b+(v^(m|~w))+E[3]+2399980690&4294967295,b=m+(y<<10&4294967295|y>>>22),y=w+(m^(b|~v))+E[10]+4293915773&4294967295,w=b+(y<<15&4294967295|y>>>17),y=v+(b^(w|~m))+E[1]+2240044497&4294967295,v=w+(y<<21&4294967295|y>>>11),y=m+(w^(v|~b))+E[8]+1873313359&4294967295,m=v+(y<<6&4294967295|y>>>26),y=b+(v^(m|~w))+E[15]+4264355552&4294967295,b=m+(y<<10&4294967295|y>>>22),y=w+(m^(b|~v))+E[6]+2734768916&4294967295,w=b+(y<<15&4294967295|y>>>17),y=v+(b^(w|~m))+E[13]+1309151649&4294967295,v=w+(y<<21&4294967295|y>>>11),y=m+(w^(v|~b))+E[4]+4149444226&4294967295,m=v+(y<<6&4294967295|y>>>26),y=b+(v^(m|~w))+E[11]+3174756917&4294967295,b=m+(y<<10&4294967295|y>>>22),y=w+(m^(b|~v))+E[2]+718787259&4294967295,w=b+(y<<15&4294967295|y>>>17),y=v+(b^(w|~m))+E[9]+3951481745&4294967295,T.g[0]=T.g[0]+m&4294967295,T.g[1]=T.g[1]+(w+(y<<21&4294967295|y>>>11))&4294967295,T.g[2]=T.g[2]+w&4294967295,T.g[3]=T.g[3]+b&4294967295}r.prototype.u=function(T,m){m===void 0&&(m=T.length);for(var v=m-this.blockSize,E=this.B,w=this.h,b=0;b<m;){if(w==0)for(;b<=v;)i(this,T,b),b+=this.blockSize;if(typeof T=="string"){for(;b<m;)if(E[w++]=T.charCodeAt(b++),w==this.blockSize){i(this,E),w=0;break}}else for(;b<m;)if(E[w++]=T[b++],w==this.blockSize){i(this,E),w=0;break}}this.h=w,this.o+=m},r.prototype.v=function(){var T=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);T[0]=128;for(var m=1;m<T.length-8;++m)T[m]=0;var v=8*this.o;for(m=T.length-8;m<T.length;++m)T[m]=v&255,v/=256;for(this.u(T),T=Array(16),m=v=0;4>m;++m)for(var E=0;32>E;E+=8)T[v++]=this.g[m]>>>E&255;return T};function s(T,m){var v=c;return Object.prototype.hasOwnProperty.call(v,T)?v[T]:v[T]=m(T)}function a(T,m){this.h=m;for(var v=[],E=!0,w=T.length-1;0<=w;w--){var b=T[w]|0;E&&b==m||(v[w]=b,E=!1)}this.g=v}var c={};function l(T){return-128<=T&&128>T?s(T,function(m){return new a([m|0],0>m?-1:0)}):new a([T|0],0>T?-1:0)}function d(T){if(isNaN(T)||!isFinite(T))return _;if(0>T)return D(d(-T));for(var m=[],v=1,E=0;T>=v;E++)m[E]=T/v|0,v*=4294967296;return new a(m,0)}function p(T,m){if(T.length==0)throw Error("number format error: empty string");if(m=m||10,2>m||36<m)throw Error("radix out of range: "+m);if(T.charAt(0)=="-")return D(p(T.substring(1),m));if(0<=T.indexOf("-"))throw Error('number format error: interior "-" character');for(var v=d(Math.pow(m,8)),E=_,w=0;w<T.length;w+=8){var b=Math.min(8,T.length-w),y=parseInt(T.substring(w,w+b),m);8>b?(b=d(Math.pow(m,b)),E=E.j(b).add(d(y))):(E=E.j(v),E=E.add(d(y)))}return E}var _=l(0),I=l(1),R=l(16777216);n=a.prototype,n.m=function(){if(O(this))return-D(this).m();for(var T=0,m=1,v=0;v<this.g.length;v++){var E=this.i(v);T+=(0<=E?E:4294967296+E)*m,m*=4294967296}return T},n.toString=function(T){if(T=T||10,2>T||36<T)throw Error("radix out of range: "+T);if(k(this))return"0";if(O(this))return"-"+D(this).toString(T);for(var m=d(Math.pow(T,6)),v=this,E="";;){var w=z(v,m).g;v=q(v,w.j(m));var b=((0<v.g.length?v.g[0]:v.h)>>>0).toString(T);if(v=w,k(v))return b+E;for(;6>b.length;)b="0"+b;E=b+E}},n.i=function(T){return 0>T?0:T<this.g.length?this.g[T]:this.h};function k(T){if(T.h!=0)return!1;for(var m=0;m<T.g.length;m++)if(T.g[m]!=0)return!1;return!0}function O(T){return T.h==-1}n.l=function(T){return T=q(this,T),O(T)?-1:k(T)?0:1};function D(T){for(var m=T.g.length,v=[],E=0;E<m;E++)v[E]=~T.g[E];return new a(v,~T.h).add(I)}n.abs=function(){return O(this)?D(this):this},n.add=function(T){for(var m=Math.max(this.g.length,T.g.length),v=[],E=0,w=0;w<=m;w++){var b=E+(this.i(w)&65535)+(T.i(w)&65535),y=(b>>>16)+(this.i(w)>>>16)+(T.i(w)>>>16);E=y>>>16,b&=65535,y&=65535,v[w]=y<<16|b}return new a(v,v[v.length-1]&-2147483648?-1:0)};function q(T,m){return T.add(D(m))}n.j=function(T){if(k(this)||k(T))return _;if(O(this))return O(T)?D(this).j(D(T)):D(D(this).j(T));if(O(T))return D(this.j(D(T)));if(0>this.l(R)&&0>T.l(R))return d(this.m()*T.m());for(var m=this.g.length+T.g.length,v=[],E=0;E<2*m;E++)v[E]=0;for(E=0;E<this.g.length;E++)for(var w=0;w<T.g.length;w++){var b=this.i(E)>>>16,y=this.i(E)&65535,dt=T.i(w)>>>16,fr=T.i(w)&65535;v[2*E+2*w]+=y*fr,L(v,2*E+2*w),v[2*E+2*w+1]+=b*fr,L(v,2*E+2*w+1),v[2*E+2*w+1]+=y*dt,L(v,2*E+2*w+1),v[2*E+2*w+2]+=b*dt,L(v,2*E+2*w+2)}for(E=0;E<m;E++)v[E]=v[2*E+1]<<16|v[2*E];for(E=m;E<2*m;E++)v[E]=0;return new a(v,0)};function L(T,m){for(;(T[m]&65535)!=T[m];)T[m+1]+=T[m]>>>16,T[m]&=65535,m++}function F(T,m){this.g=T,this.h=m}function z(T,m){if(k(m))throw Error("division by zero");if(k(T))return new F(_,_);if(O(T))return m=z(D(T),m),new F(D(m.g),D(m.h));if(O(m))return m=z(T,D(m)),new F(D(m.g),m.h);if(30<T.g.length){if(O(T)||O(m))throw Error("slowDivide_ only works with positive integers.");for(var v=I,E=m;0>=E.l(T);)v=ue(v),E=ue(E);var w=Q(v,1),b=Q(E,1);for(E=Q(E,2),v=Q(v,2);!k(E);){var y=b.add(E);0>=y.l(T)&&(w=w.add(v),b=y),E=Q(E,1),v=Q(v,1)}return m=q(T,w.j(m)),new F(w,m)}for(w=_;0<=T.l(m);){for(v=Math.max(1,Math.floor(T.m()/m.m())),E=Math.ceil(Math.log(v)/Math.LN2),E=48>=E?1:Math.pow(2,E-48),b=d(v),y=b.j(m);O(y)||0<y.l(T);)v-=E,b=d(v),y=b.j(m);k(b)&&(b=I),w=w.add(b),T=q(T,y)}return new F(w,T)}n.A=function(T){return z(this,T).h},n.and=function(T){for(var m=Math.max(this.g.length,T.g.length),v=[],E=0;E<m;E++)v[E]=this.i(E)&T.i(E);return new a(v,this.h&T.h)},n.or=function(T){for(var m=Math.max(this.g.length,T.g.length),v=[],E=0;E<m;E++)v[E]=this.i(E)|T.i(E);return new a(v,this.h|T.h)},n.xor=function(T){for(var m=Math.max(this.g.length,T.g.length),v=[],E=0;E<m;E++)v[E]=this.i(E)^T.i(E);return new a(v,this.h^T.h)};function ue(T){for(var m=T.g.length+1,v=[],E=0;E<m;E++)v[E]=T.i(E)<<1|T.i(E-1)>>>31;return new a(v,T.h)}function Q(T,m){var v=m>>5;m%=32;for(var E=T.g.length-v,w=[],b=0;b<E;b++)w[b]=0<m?T.i(b+v)>>>m|T.i(b+v+1)<<32-m:T.i(b+v);return new a(w,T.h)}r.prototype.digest=r.prototype.v,r.prototype.reset=r.prototype.s,r.prototype.update=r.prototype.u,bc=Qf.Md5=r,a.prototype.add=a.prototype.add,a.prototype.multiply=a.prototype.j,a.prototype.modulo=a.prototype.A,a.prototype.compare=a.prototype.l,a.prototype.toNumber=a.prototype.m,a.prototype.toString=a.prototype.toString,a.prototype.getBits=a.prototype.i,a.fromNumber=d,a.fromString=p,wt=Qf.Integer=a}).apply(typeof Kf<"u"?Kf:typeof self<"u"?self:typeof window<"u"?window:{});var ws=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},At={};var Rc,fT,Un,Sc,Yr,As,Pc,Cc,kc;(function(){var n,e=typeof Object.defineProperties=="function"?Object.defineProperty:function(o,u,h){return o==Array.prototype||o==Object.prototype||(o[u]=h.value),o};function t(o){o=[typeof globalThis=="object"&&globalThis,o,typeof window=="object"&&window,typeof self=="object"&&self,typeof ws=="object"&&ws];for(var u=0;u<o.length;++u){var h=o[u];if(h&&h.Math==Math)return h}throw Error("Cannot find global object")}var r=t(this);function i(o,u){if(u)e:{var h=r;o=o.split(".");for(var f=0;f<o.length-1;f++){var A=o[f];if(!(A in h))break e;h=h[A]}o=o[o.length-1],f=h[o],u=u(f),u!=f&&u!=null&&e(h,o,{configurable:!0,writable:!0,value:u})}}function s(o,u){o instanceof String&&(o+="");var h=0,f=!1,A={next:function(){if(!f&&h<o.length){var S=h++;return{value:u(S,o[S]),done:!1}}return f=!0,{done:!0,value:void 0}}};return A[Symbol.iterator]=function(){return A},A}i("Array.prototype.values",function(o){return o||function(){return s(this,function(u,h){return h})}});var a=a||{},c=this||self;function l(o){var u=typeof o;return u=u!="object"?u:o?Array.isArray(o)?"array":u:"null",u=="array"||u=="object"&&typeof o.length=="number"}function d(o){var u=typeof o;return u=="object"&&o!=null||u=="function"}function p(o,u,h){return o.call.apply(o.bind,arguments)}function _(o,u,h){if(!o)throw Error();if(2<arguments.length){var f=Array.prototype.slice.call(arguments,2);return function(){var A=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(A,f),o.apply(u,A)}}return function(){return o.apply(u,arguments)}}function I(o,u,h){return I=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?p:_,I.apply(null,arguments)}function R(o,u){var h=Array.prototype.slice.call(arguments,1);return function(){var f=h.slice();return f.push.apply(f,arguments),o.apply(this,f)}}function k(o,u){function h(){}h.prototype=u.prototype,o.aa=u.prototype,o.prototype=new h,o.prototype.constructor=o,o.Qb=function(f,A,S){for(var N=Array(arguments.length-2),Z=2;Z<arguments.length;Z++)N[Z-2]=arguments[Z];return u.prototype[A].apply(f,N)}}function O(o){let u=o.length;if(0<u){let h=Array(u);for(let f=0;f<u;f++)h[f]=o[f];return h}return[]}function D(o,u){for(let h=1;h<arguments.length;h++){let f=arguments[h];if(l(f)){let A=o.length||0,S=f.length||0;o.length=A+S;for(let N=0;N<S;N++)o[A+N]=f[N]}else o.push(f)}}class q{constructor(u,h){this.i=u,this.j=h,this.h=0,this.g=null}get(){let u;return 0<this.h?(this.h--,u=this.g,this.g=u.next,u.next=null):u=this.i(),u}}function L(o){return/^[\s\xa0]*$/.test(o)}function F(){var o=c.navigator;return o&&(o=o.userAgent)?o:""}function z(o){return z[" "](o),o}z[" "]=function(){};var ue=F().indexOf("Gecko")!=-1&&!(F().toLowerCase().indexOf("webkit")!=-1&&F().indexOf("Edge")==-1)&&!(F().indexOf("Trident")!=-1||F().indexOf("MSIE")!=-1)&&F().indexOf("Edge")==-1;function Q(o,u,h){for(let f in o)u.call(h,o[f],f,o)}function T(o,u){for(let h in o)u.call(void 0,o[h],h,o)}function m(o){let u={};for(let h in o)u[h]=o[h];return u}let v="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function E(o,u){let h,f;for(let A=1;A<arguments.length;A++){f=arguments[A];for(h in f)o[h]=f[h];for(let S=0;S<v.length;S++)h=v[S],Object.prototype.hasOwnProperty.call(f,h)&&(o[h]=f[h])}}function w(o){var u=1;o=o.split(":");let h=[];for(;0<u&&o.length;)h.push(o.shift()),u--;return o.length&&h.push(o.join(":")),h}function b(o){c.setTimeout(()=>{throw o},0)}function y(){var o=Do;let u=null;return o.g&&(u=o.g,o.g=o.g.next,o.g||(o.h=null),u.next=null),u}class dt{constructor(){this.h=this.g=null}add(u,h){let f=fr.get();f.set(u,h),this.h?this.h.next=f:this.g=f,this.h=f}}var fr=new q(()=>new r_,o=>o.reset());class r_{constructor(){this.next=this.g=this.h=null}set(u,h){this.h=u,this.g=h,this.next=null}reset(){this.next=this.g=this.h=null}}let pr,mr=!1,Do=new dt,oh=()=>{let o=c.Promise.resolve(void 0);pr=()=>{o.then(i_)}};var i_=()=>{for(var o;o=y();){try{o.h.call(o.g)}catch(h){b(h)}var u=fr;u.j(o),100>u.h&&(u.h++,o.next=u.g,u.g=o)}mr=!1};function Dt(){this.s=this.s,this.C=this.C}Dt.prototype.s=!1,Dt.prototype.ma=function(){this.s||(this.s=!0,this.N())},Dt.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()};function Te(o,u){this.type=o,this.g=this.target=u,this.defaultPrevented=!1}Te.prototype.h=function(){this.defaultPrevented=!0};var s_=function(){if(!c.addEventListener||!Object.defineProperty)return!1;var o=!1,u=Object.defineProperty({},"passive",{get:function(){o=!0}});try{let h=()=>{};c.addEventListener("test",h,u),c.removeEventListener("test",h,u)}catch{}return o}();function gr(o,u){if(Te.call(this,o?o.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,o){var h=this.type=o.type,f=o.changedTouches&&o.changedTouches.length?o.changedTouches[0]:null;if(this.target=o.target||o.srcElement,this.g=u,u=o.relatedTarget){if(ue){e:{try{z(u.nodeName);var A=!0;break e}catch{}A=!1}A||(u=null)}}else h=="mouseover"?u=o.fromElement:h=="mouseout"&&(u=o.toElement);this.relatedTarget=u,f?(this.clientX=f.clientX!==void 0?f.clientX:f.pageX,this.clientY=f.clientY!==void 0?f.clientY:f.pageY,this.screenX=f.screenX||0,this.screenY=f.screenY||0):(this.clientX=o.clientX!==void 0?o.clientX:o.pageX,this.clientY=o.clientY!==void 0?o.clientY:o.pageY,this.screenX=o.screenX||0,this.screenY=o.screenY||0),this.button=o.button,this.key=o.key||"",this.ctrlKey=o.ctrlKey,this.altKey=o.altKey,this.shiftKey=o.shiftKey,this.metaKey=o.metaKey,this.pointerId=o.pointerId||0,this.pointerType=typeof o.pointerType=="string"?o.pointerType:o_[o.pointerType]||"",this.state=o.state,this.i=o,o.defaultPrevented&&gr.aa.h.call(this)}}k(gr,Te);var o_={2:"touch",3:"pen",4:"mouse"};gr.prototype.h=function(){gr.aa.h.call(this);var o=this.i;o.preventDefault?o.preventDefault():o.returnValue=!1};var _r="closure_listenable_"+(1e6*Math.random()|0),a_=0;function c_(o,u,h,f,A){this.listener=o,this.proxy=null,this.src=u,this.type=h,this.capture=!!f,this.ha=A,this.key=++a_,this.da=this.fa=!1}function Ri(o){o.da=!0,o.listener=null,o.proxy=null,o.src=null,o.ha=null}function Si(o){this.src=o,this.g={},this.h=0}Si.prototype.add=function(o,u,h,f,A){var S=o.toString();o=this.g[S],o||(o=this.g[S]=[],this.h++);var N=Oo(o,u,f,A);return-1<N?(u=o[N],h||(u.fa=!1)):(u=new c_(u,this.src,S,!!f,A),u.fa=h,o.push(u)),u};function No(o,u){var h=u.type;if(h in o.g){var f=o.g[h],A=Array.prototype.indexOf.call(f,u,void 0),S;(S=0<=A)&&Array.prototype.splice.call(f,A,1),S&&(Ri(u),o.g[h].length==0&&(delete o.g[h],o.h--))}}function Oo(o,u,h,f){for(var A=0;A<o.length;++A){var S=o[A];if(!S.da&&S.listener==u&&S.capture==!!h&&S.ha==f)return A}return-1}var xo="closure_lm_"+(1e6*Math.random()|0),Vo={};function ah(o,u,h,f,A){if(f&&f.once)return uh(o,u,h,f,A);if(Array.isArray(u)){for(var S=0;S<u.length;S++)ah(o,u[S],h,f,A);return null}return h=Uo(h),o&&o[_r]?o.K(u,h,d(f)?!!f.capture:!!f,A):ch(o,u,h,!1,f,A)}function ch(o,u,h,f,A,S){if(!u)throw Error("Invalid event type");var N=d(A)?!!A.capture:!!A,Z=Mo(o);if(Z||(o[xo]=Z=new Si(o)),h=Z.add(u,h,f,N,S),h.proxy)return h;if(f=u_(),h.proxy=f,f.src=o,f.listener=h,o.addEventListener)s_||(A=N),A===void 0&&(A=!1),o.addEventListener(u.toString(),f,A);else if(o.attachEvent)o.attachEvent(hh(u.toString()),f);else if(o.addListener&&o.removeListener)o.addListener(f);else throw Error("addEventListener and attachEvent are unavailable.");return h}function u_(){function o(h){return u.call(o.src,o.listener,h)}let u=l_;return o}function uh(o,u,h,f,A){if(Array.isArray(u)){for(var S=0;S<u.length;S++)uh(o,u[S],h,f,A);return null}return h=Uo(h),o&&o[_r]?o.L(u,h,d(f)?!!f.capture:!!f,A):ch(o,u,h,!0,f,A)}function lh(o,u,h,f,A){if(Array.isArray(u))for(var S=0;S<u.length;S++)lh(o,u[S],h,f,A);else f=d(f)?!!f.capture:!!f,h=Uo(h),o&&o[_r]?(o=o.i,u=String(u).toString(),u in o.g&&(S=o.g[u],h=Oo(S,h,f,A),-1<h&&(Ri(S[h]),Array.prototype.splice.call(S,h,1),S.length==0&&(delete o.g[u],o.h--)))):o&&(o=Mo(o))&&(u=o.g[u.toString()],o=-1,u&&(o=Oo(u,h,f,A)),(h=-1<o?u[o]:null)&&Lo(h))}function Lo(o){if(typeof o!="number"&&o&&!o.da){var u=o.src;if(u&&u[_r])No(u.i,o);else{var h=o.type,f=o.proxy;u.removeEventListener?u.removeEventListener(h,f,o.capture):u.detachEvent?u.detachEvent(hh(h),f):u.addListener&&u.removeListener&&u.removeListener(f),(h=Mo(u))?(No(h,o),h.h==0&&(h.src=null,u[xo]=null)):Ri(o)}}}function hh(o){return o in Vo?Vo[o]:Vo[o]="on"+o}function l_(o,u){if(o.da)o=!0;else{u=new gr(u,this);var h=o.listener,f=o.ha||o.src;o.fa&&Lo(o),o=h.call(f,u)}return o}function Mo(o){return o=o[xo],o instanceof Si?o:null}var Fo="__closure_events_fn_"+(1e9*Math.random()>>>0);function Uo(o){return typeof o=="function"?o:(o[Fo]||(o[Fo]=function(u){return o.handleEvent(u)}),o[Fo])}function we(){Dt.call(this),this.i=new Si(this),this.M=this,this.F=null}k(we,Dt),we.prototype[_r]=!0,we.prototype.removeEventListener=function(o,u,h,f){lh(this,o,u,h,f)};function Pe(o,u){var h,f=o.F;if(f)for(h=[];f;f=f.F)h.push(f);if(o=o.M,f=u.type||u,typeof u=="string")u=new Te(u,o);else if(u instanceof Te)u.target=u.target||o;else{var A=u;u=new Te(f,o),E(u,A)}if(A=!0,h)for(var S=h.length-1;0<=S;S--){var N=u.g=h[S];A=Pi(N,f,!0,u)&&A}if(N=u.g=o,A=Pi(N,f,!0,u)&&A,A=Pi(N,f,!1,u)&&A,h)for(S=0;S<h.length;S++)N=u.g=h[S],A=Pi(N,f,!1,u)&&A}we.prototype.N=function(){if(we.aa.N.call(this),this.i){var o=this.i,u;for(u in o.g){for(var h=o.g[u],f=0;f<h.length;f++)Ri(h[f]);delete o.g[u],o.h--}}this.F=null},we.prototype.K=function(o,u,h,f){return this.i.add(String(o),u,!1,h,f)},we.prototype.L=function(o,u,h,f){return this.i.add(String(o),u,!0,h,f)};function Pi(o,u,h,f){if(u=o.i.g[String(u)],!u)return!0;u=u.concat();for(var A=!0,S=0;S<u.length;++S){var N=u[S];if(N&&!N.da&&N.capture==h){var Z=N.listener,ge=N.ha||N.src;N.fa&&No(o.i,N),A=Z.call(ge,f)!==!1&&A}}return A&&!f.defaultPrevented}function dh(o,u,h){if(typeof o=="function")h&&(o=I(o,h));else if(o&&typeof o.handleEvent=="function")o=I(o.handleEvent,o);else throw Error("Invalid listener argument");return 2147483647<Number(u)?-1:c.setTimeout(o,u||0)}function fh(o){o.g=dh(()=>{o.g=null,o.i&&(o.i=!1,fh(o))},o.l);let u=o.h;o.h=null,o.m.apply(null,u)}class h_ extends Dt{constructor(u,h){super(),this.m=u,this.l=h,this.h=null,this.i=!1,this.g=null}j(u){this.h=arguments,this.g?this.i=!0:fh(this)}N(){super.N(),this.g&&(c.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function yr(o){Dt.call(this),this.h=o,this.g={}}k(yr,Dt);var ph=[];function mh(o){Q(o.g,function(u,h){this.g.hasOwnProperty(h)&&Lo(u)},o),o.g={}}yr.prototype.N=function(){yr.aa.N.call(this),mh(this)},yr.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var Bo=c.JSON.stringify,d_=c.JSON.parse,f_=class{stringify(o){return c.JSON.stringify(o,void 0)}parse(o){return c.JSON.parse(o,void 0)}};function qo(){}qo.prototype.h=null;function gh(o){return o.h||(o.h=o.i())}function _h(){}var Ir={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function $o(){Te.call(this,"d")}k($o,Te);function jo(){Te.call(this,"c")}k(jo,Te);var nn={},yh=null;function Ci(){return yh=yh||new we}nn.La="serverreachability";function Ih(o){Te.call(this,nn.La,o)}k(Ih,Te);function vr(o){let u=Ci();Pe(u,new Ih(u))}nn.STAT_EVENT="statevent";function vh(o,u){Te.call(this,nn.STAT_EVENT,o),this.stat=u}k(vh,Te);function Ce(o){let u=Ci();Pe(u,new vh(u,o))}nn.Ma="timingevent";function Eh(o,u){Te.call(this,nn.Ma,o),this.size=u}k(Eh,Te);function Er(o,u){if(typeof o!="function")throw Error("Fn must not be null and must be a function");return c.setTimeout(function(){o()},u)}function Tr(){this.g=!0}Tr.prototype.xa=function(){this.g=!1};function p_(o,u,h,f,A,S){o.info(function(){if(o.g)if(S)for(var N="",Z=S.split("&"),ge=0;ge<Z.length;ge++){var Y=Z[ge].split("=");if(1<Y.length){var Ae=Y[0];Y=Y[1];var be=Ae.split("_");N=2<=be.length&&be[1]=="type"?N+(Ae+"="+Y+"&"):N+(Ae+"=redacted&")}}else N=null;else N=S;return"XMLHTTP REQ ("+f+") [attempt "+A+"]: "+u+`
`+h+`
`+N})}function m_(o,u,h,f,A,S,N){o.info(function(){return"XMLHTTP RESP ("+f+") [ attempt "+A+"]: "+u+`
`+h+`
`+S+" "+N})}function Rn(o,u,h,f){o.info(function(){return"XMLHTTP TEXT ("+u+"): "+__(o,h)+(f?" "+f:"")})}function g_(o,u){o.info(function(){return"TIMEOUT: "+u})}Tr.prototype.info=function(){};function __(o,u){if(!o.g)return u;if(!u)return null;try{var h=JSON.parse(u);if(h){for(o=0;o<h.length;o++)if(Array.isArray(h[o])){var f=h[o];if(!(2>f.length)){var A=f[1];if(Array.isArray(A)&&!(1>A.length)){var S=A[0];if(S!="noop"&&S!="stop"&&S!="close")for(var N=1;N<A.length;N++)A[N]=""}}}}return Bo(h)}catch{return u}}var ki={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},Th={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"},zo;function Di(){}k(Di,qo),Di.prototype.g=function(){return new XMLHttpRequest},Di.prototype.i=function(){return{}},zo=new Di;function Nt(o,u,h,f){this.j=o,this.i=u,this.l=h,this.R=f||1,this.U=new yr(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new wh}function wh(){this.i=null,this.g="",this.h=!1}var Ah={},Wo={};function Go(o,u,h){o.L=1,o.v=Vi(ft(u)),o.m=h,o.P=!0,bh(o,null)}function bh(o,u){o.F=Date.now(),Ni(o),o.A=ft(o.v);var h=o.A,f=o.R;Array.isArray(f)||(f=[String(f)]),Uh(h.i,"t",f),o.C=0,h=o.j.J,o.h=new wh,o.g=rd(o.j,h?u:null,!o.m),0<o.O&&(o.M=new h_(I(o.Y,o,o.g),o.O)),u=o.U,h=o.g,f=o.ca;var A="readystatechange";Array.isArray(A)||(A&&(ph[0]=A.toString()),A=ph);for(var S=0;S<A.length;S++){var N=ah(h,A[S],f||u.handleEvent,!1,u.h||u);if(!N)break;u.g[N.key]=N}u=o.H?m(o.H):{},o.m?(o.u||(o.u="POST"),u["Content-Type"]="application/x-www-form-urlencoded",o.g.ea(o.A,o.u,o.m,u)):(o.u="GET",o.g.ea(o.A,o.u,null,u)),vr(),p_(o.i,o.u,o.A,o.l,o.R,o.m)}Nt.prototype.ca=function(o){o=o.target;let u=this.M;u&&pt(o)==3?u.j():this.Y(o)},Nt.prototype.Y=function(o){try{if(o==this.g)e:{let be=pt(this.g);var u=this.g.Ba();let Cn=this.g.Z();if(!(3>be)&&(be!=3||this.g&&(this.h.h||this.g.oa()||Gh(this.g)))){this.J||be!=4||u==7||(u==8||0>=Cn?vr(3):vr(2)),Ho(this);var h=this.g.Z();this.X=h;t:if(Rh(this)){var f=Gh(this.g);o="";var A=f.length,S=pt(this.g)==4;if(!this.h.i){if(typeof TextDecoder>"u"){rn(this),wr(this);var N="";break t}this.h.i=new c.TextDecoder}for(u=0;u<A;u++)this.h.h=!0,o+=this.h.i.decode(f[u],{stream:!(S&&u==A-1)});f.length=0,this.h.g+=o,this.C=0,N=this.h.g}else N=this.g.oa();if(this.o=h==200,m_(this.i,this.u,this.A,this.l,this.R,be,h),this.o){if(this.T&&!this.K){t:{if(this.g){var Z,ge=this.g;if((Z=ge.g?ge.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!L(Z)){var Y=Z;break t}}Y=null}if(h=Y)Rn(this.i,this.l,h,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,Ko(this,h);else{this.o=!1,this.s=3,Ce(12),rn(this),wr(this);break e}}if(this.P){h=!0;let Be;for(;!this.J&&this.C<N.length;)if(Be=y_(this,N),Be==Wo){be==4&&(this.s=4,Ce(14),h=!1),Rn(this.i,this.l,null,"[Incomplete Response]");break}else if(Be==Ah){this.s=4,Ce(15),Rn(this.i,this.l,N,"[Invalid Chunk]"),h=!1;break}else Rn(this.i,this.l,Be,null),Ko(this,Be);if(Rh(this)&&this.C!=0&&(this.h.g=this.h.g.slice(this.C),this.C=0),be!=4||N.length!=0||this.h.h||(this.s=1,Ce(16),h=!1),this.o=this.o&&h,!h)Rn(this.i,this.l,N,"[Invalid Chunked Response]"),rn(this),wr(this);else if(0<N.length&&!this.W){this.W=!0;var Ae=this.j;Ae.g==this&&Ae.ba&&!Ae.M&&(Ae.j.info("Great, no buffering proxy detected. Bytes received: "+N.length),ea(Ae),Ae.M=!0,Ce(11))}}else Rn(this.i,this.l,N,null),Ko(this,N);be==4&&rn(this),this.o&&!this.J&&(be==4?Zh(this.j,this):(this.o=!1,Ni(this)))}else V_(this.g),h==400&&0<N.indexOf("Unknown SID")?(this.s=3,Ce(12)):(this.s=0,Ce(13)),rn(this),wr(this)}}}catch{}finally{}};function Rh(o){return o.g?o.u=="GET"&&o.L!=2&&o.j.Ca:!1}function y_(o,u){var h=o.C,f=u.indexOf(`
`,h);return f==-1?Wo:(h=Number(u.substring(h,f)),isNaN(h)?Ah:(f+=1,f+h>u.length?Wo:(u=u.slice(f,f+h),o.C=f+h,u)))}Nt.prototype.cancel=function(){this.J=!0,rn(this)};function Ni(o){o.S=Date.now()+o.I,Sh(o,o.I)}function Sh(o,u){if(o.B!=null)throw Error("WatchDog timer not null");o.B=Er(I(o.ba,o),u)}function Ho(o){o.B&&(c.clearTimeout(o.B),o.B=null)}Nt.prototype.ba=function(){this.B=null;let o=Date.now();0<=o-this.S?(g_(this.i,this.A),this.L!=2&&(vr(),Ce(17)),rn(this),this.s=2,wr(this)):Sh(this,this.S-o)};function wr(o){o.j.G==0||o.J||Zh(o.j,o)}function rn(o){Ho(o);var u=o.M;u&&typeof u.ma=="function"&&u.ma(),o.M=null,mh(o.U),o.g&&(u=o.g,o.g=null,u.abort(),u.ma())}function Ko(o,u){try{var h=o.j;if(h.G!=0&&(h.g==o||Qo(h.h,o))){if(!o.K&&Qo(h.h,o)&&h.G==3){try{var f=h.Da.g.parse(u)}catch{f=null}if(Array.isArray(f)&&f.length==3){var A=f;if(A[0]==0){e:if(!h.u){if(h.g)if(h.g.F+3e3<o.F)Bi(h),Fi(h);else break e;Zo(h),Ce(18)}}else h.za=A[1],0<h.za-h.T&&37500>A[2]&&h.F&&h.v==0&&!h.C&&(h.C=Er(I(h.Za,h),6e3));if(1>=kh(h.h)&&h.ca){try{h.ca()}catch{}h.ca=void 0}}else on(h,11)}else if((o.K||h.g==o)&&Bi(h),!L(u))for(A=h.Da.g.parse(u),u=0;u<A.length;u++){let Y=A[u];if(h.T=Y[0],Y=Y[1],h.G==2)if(Y[0]=="c"){h.K=Y[1],h.ia=Y[2];let Ae=Y[3];Ae!=null&&(h.la=Ae,h.j.info("VER="+h.la));let be=Y[4];be!=null&&(h.Aa=be,h.j.info("SVER="+h.Aa));let Cn=Y[5];Cn!=null&&typeof Cn=="number"&&0<Cn&&(f=1.5*Cn,h.L=f,h.j.info("backChannelRequestTimeoutMs_="+f)),f=h;let Be=o.g;if(Be){let $i=Be.g?Be.g.getResponseHeader("X-Client-Wire-Protocol"):null;if($i){var S=f.h;S.g||$i.indexOf("spdy")==-1&&$i.indexOf("quic")==-1&&$i.indexOf("h2")==-1||(S.j=S.l,S.g=new Set,S.h&&(Yo(S,S.h),S.h=null))}if(f.D){let ta=Be.g?Be.g.getResponseHeader("X-HTTP-Session-Id"):null;ta&&(f.ya=ta,ne(f.I,f.D,ta))}}h.G=3,h.l&&h.l.ua(),h.ba&&(h.R=Date.now()-o.F,h.j.info("Handshake RTT: "+h.R+"ms")),f=h;var N=o;if(f.qa=nd(f,f.J?f.ia:null,f.W),N.K){Dh(f.h,N);var Z=N,ge=f.L;ge&&(Z.I=ge),Z.B&&(Ho(Z),Ni(Z)),f.g=N}else Jh(f);0<h.i.length&&Ui(h)}else Y[0]!="stop"&&Y[0]!="close"||on(h,7);else h.G==3&&(Y[0]=="stop"||Y[0]=="close"?Y[0]=="stop"?on(h,7):Xo(h):Y[0]!="noop"&&h.l&&h.l.ta(Y),h.v=0)}}vr(4)}catch{}}var I_=class{constructor(o,u){this.g=o,this.map=u}};function Ph(o){this.l=o||10,c.PerformanceNavigationTiming?(o=c.performance.getEntriesByType("navigation"),o=0<o.length&&(o[0].nextHopProtocol=="hq"||o[0].nextHopProtocol=="h2")):o=!!(c.chrome&&c.chrome.loadTimes&&c.chrome.loadTimes()&&c.chrome.loadTimes().wasFetchedViaSpdy),this.j=o?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function Ch(o){return o.h?!0:o.g?o.g.size>=o.j:!1}function kh(o){return o.h?1:o.g?o.g.size:0}function Qo(o,u){return o.h?o.h==u:o.g?o.g.has(u):!1}function Yo(o,u){o.g?o.g.add(u):o.h=u}function Dh(o,u){o.h&&o.h==u?o.h=null:o.g&&o.g.has(u)&&o.g.delete(u)}Ph.prototype.cancel=function(){if(this.i=Nh(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&this.g.size!==0){for(let o of this.g.values())o.cancel();this.g.clear()}};function Nh(o){if(o.h!=null)return o.i.concat(o.h.D);if(o.g!=null&&o.g.size!==0){let u=o.i;for(let h of o.g.values())u=u.concat(h.D);return u}return O(o.i)}function v_(o){if(o.V&&typeof o.V=="function")return o.V();if(typeof Map<"u"&&o instanceof Map||typeof Set<"u"&&o instanceof Set)return Array.from(o.values());if(typeof o=="string")return o.split("");if(l(o)){for(var u=[],h=o.length,f=0;f<h;f++)u.push(o[f]);return u}u=[],h=0;for(f in o)u[h++]=o[f];return u}function E_(o){if(o.na&&typeof o.na=="function")return o.na();if(!o.V||typeof o.V!="function"){if(typeof Map<"u"&&o instanceof Map)return Array.from(o.keys());if(!(typeof Set<"u"&&o instanceof Set)){if(l(o)||typeof o=="string"){var u=[];o=o.length;for(var h=0;h<o;h++)u.push(h);return u}u=[],h=0;for(let f in o)u[h++]=f;return u}}}function Oh(o,u){if(o.forEach&&typeof o.forEach=="function")o.forEach(u,void 0);else if(l(o)||typeof o=="string")Array.prototype.forEach.call(o,u,void 0);else for(var h=E_(o),f=v_(o),A=f.length,S=0;S<A;S++)u.call(void 0,f[S],h&&h[S],o)}var xh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function T_(o,u){if(o){o=o.split("&");for(var h=0;h<o.length;h++){var f=o[h].indexOf("="),A=null;if(0<=f){var S=o[h].substring(0,f);A=o[h].substring(f+1)}else S=o[h];u(S,A?decodeURIComponent(A.replace(/\+/g," ")):"")}}}function sn(o){if(this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,o instanceof sn){this.h=o.h,Oi(this,o.j),this.o=o.o,this.g=o.g,xi(this,o.s),this.l=o.l;var u=o.i,h=new Rr;h.i=u.i,u.g&&(h.g=new Map(u.g),h.h=u.h),Vh(this,h),this.m=o.m}else o&&(u=String(o).match(xh))?(this.h=!1,Oi(this,u[1]||"",!0),this.o=Ar(u[2]||""),this.g=Ar(u[3]||"",!0),xi(this,u[4]),this.l=Ar(u[5]||"",!0),Vh(this,u[6]||"",!0),this.m=Ar(u[7]||"")):(this.h=!1,this.i=new Rr(null,this.h))}sn.prototype.toString=function(){var o=[],u=this.j;u&&o.push(br(u,Lh,!0),":");var h=this.g;return(h||u=="file")&&(o.push("//"),(u=this.o)&&o.push(br(u,Lh,!0),"@"),o.push(encodeURIComponent(String(h)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),h=this.s,h!=null&&o.push(":",String(h))),(h=this.l)&&(this.g&&h.charAt(0)!="/"&&o.push("/"),o.push(br(h,h.charAt(0)=="/"?b_:A_,!0))),(h=this.i.toString())&&o.push("?",h),(h=this.m)&&o.push("#",br(h,S_)),o.join("")};function ft(o){return new sn(o)}function Oi(o,u,h){o.j=h?Ar(u,!0):u,o.j&&(o.j=o.j.replace(/:$/,""))}function xi(o,u){if(u){if(u=Number(u),isNaN(u)||0>u)throw Error("Bad port number "+u);o.s=u}else o.s=null}function Vh(o,u,h){u instanceof Rr?(o.i=u,P_(o.i,o.h)):(h||(u=br(u,R_)),o.i=new Rr(u,o.h))}function ne(o,u,h){o.i.set(u,h)}function Vi(o){return ne(o,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),o}function Ar(o,u){return o?u?decodeURI(o.replace(/%25/g,"%2525")):decodeURIComponent(o):""}function br(o,u,h){return typeof o=="string"?(o=encodeURI(o).replace(u,w_),h&&(o=o.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),o):null}function w_(o){return o=o.charCodeAt(0),"%"+(o>>4&15).toString(16)+(o&15).toString(16)}var Lh=/[#\/\?@]/g,A_=/[#\?:]/g,b_=/[#\?]/g,R_=/[#\?@]/g,S_=/#/g;function Rr(o,u){this.h=this.g=null,this.i=o||null,this.j=!!u}function Ot(o){o.g||(o.g=new Map,o.h=0,o.i&&T_(o.i,function(u,h){o.add(decodeURIComponent(u.replace(/\+/g," ")),h)}))}n=Rr.prototype,n.add=function(o,u){Ot(this),this.i=null,o=Sn(this,o);var h=this.g.get(o);return h||this.g.set(o,h=[]),h.push(u),this.h+=1,this};function Mh(o,u){Ot(o),u=Sn(o,u),o.g.has(u)&&(o.i=null,o.h-=o.g.get(u).length,o.g.delete(u))}function Fh(o,u){return Ot(o),u=Sn(o,u),o.g.has(u)}n.forEach=function(o,u){Ot(this),this.g.forEach(function(h,f){h.forEach(function(A){o.call(u,A,f,this)},this)},this)},n.na=function(){Ot(this);let o=Array.from(this.g.values()),u=Array.from(this.g.keys()),h=[];for(let f=0;f<u.length;f++){let A=o[f];for(let S=0;S<A.length;S++)h.push(u[f])}return h},n.V=function(o){Ot(this);let u=[];if(typeof o=="string")Fh(this,o)&&(u=u.concat(this.g.get(Sn(this,o))));else{o=Array.from(this.g.values());for(let h=0;h<o.length;h++)u=u.concat(o[h])}return u},n.set=function(o,u){return Ot(this),this.i=null,o=Sn(this,o),Fh(this,o)&&(this.h-=this.g.get(o).length),this.g.set(o,[u]),this.h+=1,this},n.get=function(o,u){return o?(o=this.V(o),0<o.length?String(o[0]):u):u};function Uh(o,u,h){Mh(o,u),0<h.length&&(o.i=null,o.g.set(Sn(o,u),O(h)),o.h+=h.length)}n.toString=function(){if(this.i)return this.i;if(!this.g)return"";let o=[],u=Array.from(this.g.keys());for(var h=0;h<u.length;h++){var f=u[h];let S=encodeURIComponent(String(f)),N=this.V(f);for(f=0;f<N.length;f++){var A=S;N[f]!==""&&(A+="="+encodeURIComponent(String(N[f]))),o.push(A)}}return this.i=o.join("&")};function Sn(o,u){return u=String(u),o.j&&(u=u.toLowerCase()),u}function P_(o,u){u&&!o.j&&(Ot(o),o.i=null,o.g.forEach(function(h,f){var A=f.toLowerCase();f!=A&&(Mh(this,f),Uh(this,A,h))},o)),o.j=u}function C_(o,u){let h=new Tr;if(c.Image){let f=new Image;f.onload=R(xt,h,"TestLoadImage: loaded",!0,u,f),f.onerror=R(xt,h,"TestLoadImage: error",!1,u,f),f.onabort=R(xt,h,"TestLoadImage: abort",!1,u,f),f.ontimeout=R(xt,h,"TestLoadImage: timeout",!1,u,f),c.setTimeout(function(){f.ontimeout&&f.ontimeout()},1e4),f.src=o}else u(!1)}function k_(o,u){let h=new Tr,f=new AbortController,A=setTimeout(()=>{f.abort(),xt(h,"TestPingServer: timeout",!1,u)},1e4);fetch(o,{signal:f.signal}).then(S=>{clearTimeout(A),S.ok?xt(h,"TestPingServer: ok",!0,u):xt(h,"TestPingServer: server error",!1,u)}).catch(()=>{clearTimeout(A),xt(h,"TestPingServer: error",!1,u)})}function xt(o,u,h,f,A){try{A&&(A.onload=null,A.onerror=null,A.onabort=null,A.ontimeout=null),f(h)}catch{}}function D_(){this.g=new f_}function N_(o,u,h){let f=h||"";try{Oh(o,function(A,S){let N=A;d(A)&&(N=Bo(A)),u.push(f+S+"="+encodeURIComponent(N))})}catch(A){throw u.push(f+"type="+encodeURIComponent("_badmap")),A}}function Sr(o){this.l=o.Ub||null,this.j=o.eb||!1}k(Sr,qo),Sr.prototype.g=function(){return new Li(this.l,this.j)},Sr.prototype.i=function(o){return function(){return o}}({});function Li(o,u){we.call(this),this.D=o,this.o=u,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}k(Li,we),n=Li.prototype,n.open=function(o,u){if(this.readyState!=0)throw this.abort(),Error("Error reopening a connection");this.B=o,this.A=u,this.readyState=1,Cr(this)},n.send=function(o){if(this.readyState!=1)throw this.abort(),Error("need to call open() first. ");this.g=!0;let u={headers:this.u,method:this.B,credentials:this.m,cache:void 0};o&&(u.body=o),(this.D||c).fetch(new Request(this.A,u)).then(this.Sa.bind(this),this.ga.bind(this))},n.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&this.readyState!=4&&(this.g=!1,Pr(this)),this.readyState=0},n.Sa=function(o){if(this.g&&(this.l=o,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=o.headers,this.readyState=2,Cr(this)),this.g&&(this.readyState=3,Cr(this),this.g)))if(this.responseType==="arraybuffer")o.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(typeof c.ReadableStream<"u"&&"body"in o){if(this.j=o.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;Bh(this)}else o.text().then(this.Ra.bind(this),this.ga.bind(this))};function Bh(o){o.j.read().then(o.Pa.bind(o)).catch(o.ga.bind(o))}n.Pa=function(o){if(this.g){if(this.o&&o.value)this.response.push(o.value);else if(!this.o){var u=o.value?o.value:new Uint8Array(0);(u=this.v.decode(u,{stream:!o.done}))&&(this.response=this.responseText+=u)}o.done?Pr(this):Cr(this),this.readyState==3&&Bh(this)}},n.Ra=function(o){this.g&&(this.response=this.responseText=o,Pr(this))},n.Qa=function(o){this.g&&(this.response=o,Pr(this))},n.ga=function(){this.g&&Pr(this)};function Pr(o){o.readyState=4,o.l=null,o.j=null,o.v=null,Cr(o)}n.setRequestHeader=function(o,u){this.u.append(o,u)},n.getResponseHeader=function(o){return this.h&&this.h.get(o.toLowerCase())||""},n.getAllResponseHeaders=function(){if(!this.h)return"";let o=[],u=this.h.entries();for(var h=u.next();!h.done;)h=h.value,o.push(h[0]+": "+h[1]),h=u.next();return o.join(`\r
`)};function Cr(o){o.onreadystatechange&&o.onreadystatechange.call(o)}Object.defineProperty(Li.prototype,"withCredentials",{get:function(){return this.m==="include"},set:function(o){this.m=o?"include":"same-origin"}});function qh(o){let u="";return Q(o,function(h,f){u+=f,u+=":",u+=h,u+=`\r
`}),u}function Jo(o,u,h){e:{for(f in h){var f=!1;break e}f=!0}f||(h=qh(h),typeof o=="string"?h!=null&&encodeURIComponent(String(h)):ne(o,u,h))}function se(o){we.call(this),this.headers=new Map,this.o=o||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}k(se,we);var O_=/^https?$/i,x_=["POST","PUT"];n=se.prototype,n.Ha=function(o){this.J=o},n.ea=function(o,u,h,f){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+o);u=u?u.toUpperCase():"GET",this.D=o,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=this.o?this.o.g():zo.g(),this.v=this.o?gh(this.o):gh(zo),this.g.onreadystatechange=I(this.Ea,this);try{this.B=!0,this.g.open(u,String(o),!0),this.B=!1}catch(S){$h(this,S);return}if(o=h||"",h=new Map(this.headers),f)if(Object.getPrototypeOf(f)===Object.prototype)for(var A in f)h.set(A,f[A]);else if(typeof f.keys=="function"&&typeof f.get=="function")for(let S of f.keys())h.set(S,f.get(S));else throw Error("Unknown input type for opt_headers: "+String(f));f=Array.from(h.keys()).find(S=>S.toLowerCase()=="content-type"),A=c.FormData&&o instanceof c.FormData,!(0<=Array.prototype.indexOf.call(x_,u,void 0))||f||A||h.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");for(let[S,N]of h)this.g.setRequestHeader(S,N);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{Wh(this),this.u=!0,this.g.send(o),this.u=!1}catch(S){$h(this,S)}};function $h(o,u){o.h=!1,o.g&&(o.j=!0,o.g.abort(),o.j=!1),o.l=u,o.m=5,jh(o),Mi(o)}function jh(o){o.A||(o.A=!0,Pe(o,"complete"),Pe(o,"error"))}n.abort=function(o){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=o||7,Pe(this,"complete"),Pe(this,"abort"),Mi(this))},n.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Mi(this,!0)),se.aa.N.call(this)},n.Ea=function(){this.s||(this.B||this.u||this.j?zh(this):this.bb())},n.bb=function(){zh(this)};function zh(o){if(o.h&&typeof a<"u"&&(!o.v[1]||pt(o)!=4||o.Z()!=2)){if(o.u&&pt(o)==4)dh(o.Ea,0,o);else if(Pe(o,"readystatechange"),pt(o)==4){o.h=!1;try{let N=o.Z();e:switch(N){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var u=!0;break e;default:u=!1}var h;if(!(h=u)){var f;if(f=N===0){var A=String(o.D).match(xh)[1]||null;!A&&c.self&&c.self.location&&(A=c.self.location.protocol.slice(0,-1)),f=!O_.test(A?A.toLowerCase():"")}h=f}if(h)Pe(o,"complete"),Pe(o,"success");else{o.m=6;try{var S=2<pt(o)?o.g.statusText:""}catch{S=""}o.l=S+" ["+o.Z()+"]",jh(o)}}finally{Mi(o)}}}}function Mi(o,u){if(o.g){Wh(o);let h=o.g,f=o.v[0]?()=>{}:null;o.g=null,o.v=null,u||Pe(o,"ready");try{h.onreadystatechange=f}catch{}}}function Wh(o){o.I&&(c.clearTimeout(o.I),o.I=null)}n.isActive=function(){return!!this.g};function pt(o){return o.g?o.g.readyState:0}n.Z=function(){try{return 2<pt(this)?this.g.status:-1}catch{return-1}},n.oa=function(){try{return this.g?this.g.responseText:""}catch{return""}},n.Oa=function(o){if(this.g){var u=this.g.responseText;return o&&u.indexOf(o)==0&&(u=u.substring(o.length)),d_(u)}};function Gh(o){try{if(!o.g)return null;if("response"in o.g)return o.g.response;switch(o.H){case"":case"text":return o.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in o.g)return o.g.mozResponseArrayBuffer}return null}catch{return null}}function V_(o){let u={};o=(o.g&&2<=pt(o)&&o.g.getAllResponseHeaders()||"").split(`\r
`);for(let f=0;f<o.length;f++){if(L(o[f]))continue;var h=w(o[f]);let A=h[0];if(h=h[1],typeof h!="string")continue;h=h.trim();let S=u[A]||[];u[A]=S,S.push(h)}T(u,function(f){return f.join(", ")})}n.Ba=function(){return this.m},n.Ka=function(){return typeof this.l=="string"?this.l:String(this.l)};function kr(o,u,h){return h&&h.internalChannelParams&&h.internalChannelParams[o]||u}function Hh(o){this.Aa=0,this.i=[],this.j=new Tr,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=kr("failFast",!1,o),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=kr("baseRetryDelayMs",5e3,o),this.cb=kr("retryDelaySeedMs",1e4,o),this.Wa=kr("forwardChannelMaxRetries",2,o),this.wa=kr("forwardChannelRequestTimeoutMs",2e4,o),this.pa=o&&o.xmlHttpFactory||void 0,this.Xa=o&&o.Tb||void 0,this.Ca=o&&o.useFetchStreams||!1,this.L=void 0,this.J=o&&o.supportsCrossDomainXhr||!1,this.K="",this.h=new Ph(o&&o.concurrentRequestLimit),this.Da=new D_,this.P=o&&o.fastHandshake||!1,this.O=o&&o.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=o&&o.Rb||!1,o&&o.xa&&this.j.xa(),o&&o.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&o&&o.detectBufferingProxy||!1,this.ja=void 0,o&&o.longPollingTimeout&&0<o.longPollingTimeout&&(this.ja=o.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}n=Hh.prototype,n.la=8,n.G=1,n.connect=function(o,u,h,f){Ce(0),this.W=o,this.H=u||{},h&&f!==void 0&&(this.H.OSID=h,this.H.OAID=f),this.F=this.X,this.I=nd(this,null,this.W),Ui(this)};function Xo(o){if(Kh(o),o.G==3){var u=o.U++,h=ft(o.I);if(ne(h,"SID",o.K),ne(h,"RID",u),ne(h,"TYPE","terminate"),Dr(o,h),u=new Nt(o,o.j,u),u.L=2,u.v=Vi(ft(h)),h=!1,c.navigator&&c.navigator.sendBeacon)try{h=c.navigator.sendBeacon(u.v.toString(),"")}catch{}!h&&c.Image&&(new Image().src=u.v,h=!0),h||(u.g=rd(u.j,null),u.g.ea(u.v)),u.F=Date.now(),Ni(u)}td(o)}function Fi(o){o.g&&(ea(o),o.g.cancel(),o.g=null)}function Kh(o){Fi(o),o.u&&(c.clearTimeout(o.u),o.u=null),Bi(o),o.h.cancel(),o.s&&(typeof o.s=="number"&&c.clearTimeout(o.s),o.s=null)}function Ui(o){if(!Ch(o.h)&&!o.s){o.s=!0;var u=o.Ga;pr||oh(),mr||(pr(),mr=!0),Do.add(u,o),o.B=0}}function L_(o,u){return kh(o.h)>=o.h.j-(o.s?1:0)?!1:o.s?(o.i=u.D.concat(o.i),!0):o.G==1||o.G==2||o.B>=(o.Va?0:o.Wa)?!1:(o.s=Er(I(o.Ga,o,u),ed(o,o.B)),o.B++,!0)}n.Ga=function(o){if(this.s)if(this.s=null,this.G==1){if(!o){this.U=Math.floor(1e5*Math.random()),o=this.U++;let A=new Nt(this,this.j,o),S=this.o;if(this.S&&(S?(S=m(S),E(S,this.S)):S=this.S),this.m!==null||this.O||(A.H=S,S=null),this.P)e:{for(var u=0,h=0;h<this.i.length;h++){t:{var f=this.i[h];if("__data__"in f.map&&(f=f.map.__data__,typeof f=="string")){f=f.length;break t}f=void 0}if(f===void 0)break;if(u+=f,4096<u){u=h;break e}if(u===4096||h===this.i.length-1){u=h+1;break e}}u=1e3}else u=1e3;u=Yh(this,A,u),h=ft(this.I),ne(h,"RID",o),ne(h,"CVER",22),this.D&&ne(h,"X-HTTP-Session-Id",this.D),Dr(this,h),S&&(this.O?u="headers="+encodeURIComponent(String(qh(S)))+"&"+u:this.m&&Jo(h,this.m,S)),Yo(this.h,A),this.Ua&&ne(h,"TYPE","init"),this.P?(ne(h,"$req",u),ne(h,"SID","null"),A.T=!0,Go(A,h,null)):Go(A,h,u),this.G=2}}else this.G==3&&(o?Qh(this,o):this.i.length==0||Ch(this.h)||Qh(this))};function Qh(o,u){var h;u?h=u.l:h=o.U++;let f=ft(o.I);ne(f,"SID",o.K),ne(f,"RID",h),ne(f,"AID",o.T),Dr(o,f),o.m&&o.o&&Jo(f,o.m,o.o),h=new Nt(o,o.j,h,o.B+1),o.m===null&&(h.H=o.o),u&&(o.i=u.D.concat(o.i)),u=Yh(o,h,1e3),h.I=Math.round(.5*o.wa)+Math.round(.5*o.wa*Math.random()),Yo(o.h,h),Go(h,f,u)}function Dr(o,u){o.H&&Q(o.H,function(h,f){ne(u,f,h)}),o.l&&Oh({},function(h,f){ne(u,f,h)})}function Yh(o,u,h){h=Math.min(o.i.length,h);var f=o.l?I(o.l.Na,o.l,o):null;e:{var A=o.i;let S=-1;for(;;){let N=["count="+h];S==-1?0<h?(S=A[0].g,N.push("ofs="+S)):S=0:N.push("ofs="+S);let Z=!0;for(let ge=0;ge<h;ge++){let Y=A[ge].g,Ae=A[ge].map;if(Y-=S,0>Y)S=Math.max(0,A[ge].g-100),Z=!1;else try{N_(Ae,N,"req"+Y+"_")}catch{f&&f(Ae)}}if(Z){f=N.join("&");break e}}}return o=o.i.splice(0,h),u.D=o,f}function Jh(o){if(!o.g&&!o.u){o.Y=1;var u=o.Fa;pr||oh(),mr||(pr(),mr=!0),Do.add(u,o),o.v=0}}function Zo(o){return o.g||o.u||3<=o.v?!1:(o.Y++,o.u=Er(I(o.Fa,o),ed(o,o.v)),o.v++,!0)}n.Fa=function(){if(this.u=null,Xh(this),this.ba&&!(this.M||this.g==null||0>=this.R)){var o=2*this.R;this.j.info("BP detection timer enabled: "+o),this.A=Er(I(this.ab,this),o)}},n.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,Ce(10),Fi(this),Xh(this))};function ea(o){o.A!=null&&(c.clearTimeout(o.A),o.A=null)}function Xh(o){o.g=new Nt(o,o.j,"rpc",o.Y),o.m===null&&(o.g.H=o.o),o.g.O=0;var u=ft(o.qa);ne(u,"RID","rpc"),ne(u,"SID",o.K),ne(u,"AID",o.T),ne(u,"CI",o.F?"0":"1"),!o.F&&o.ja&&ne(u,"TO",o.ja),ne(u,"TYPE","xmlhttp"),Dr(o,u),o.m&&o.o&&Jo(u,o.m,o.o),o.L&&(o.g.I=o.L);var h=o.g;o=o.ia,h.L=1,h.v=Vi(ft(u)),h.m=null,h.P=!0,bh(h,o)}n.Za=function(){this.C!=null&&(this.C=null,Fi(this),Zo(this),Ce(19))};function Bi(o){o.C!=null&&(c.clearTimeout(o.C),o.C=null)}function Zh(o,u){var h=null;if(o.g==u){Bi(o),ea(o),o.g=null;var f=2}else if(Qo(o.h,u))h=u.D,Dh(o.h,u),f=1;else return;if(o.G!=0){if(u.o)if(f==1){h=u.m?u.m.length:0,u=Date.now()-u.F;var A=o.B;f=Ci(),Pe(f,new Eh(f,h)),Ui(o)}else Jh(o);else if(A=u.s,A==3||A==0&&0<u.X||!(f==1&&L_(o,u)||f==2&&Zo(o)))switch(h&&0<h.length&&(u=o.h,u.i=u.i.concat(h)),A){case 1:on(o,5);break;case 4:on(o,10);break;case 3:on(o,6);break;default:on(o,2)}}}function ed(o,u){let h=o.Ta+Math.floor(Math.random()*o.cb);return o.isActive()||(h*=2),h*u}function on(o,u){if(o.j.info("Error code "+u),u==2){var h=I(o.fb,o),f=o.Xa;let A=!f;f=new sn(f||"//www.google.com/images/cleardot.gif"),c.location&&c.location.protocol=="http"||Oi(f,"https"),Vi(f),A?C_(f.toString(),h):k_(f.toString(),h)}else Ce(2);o.G=0,o.l&&o.l.sa(u),td(o),Kh(o)}n.fb=function(o){o?(this.j.info("Successfully pinged google.com"),Ce(2)):(this.j.info("Failed to ping google.com"),Ce(1))};function td(o){if(o.G=0,o.ka=[],o.l){let u=Nh(o.h);(u.length!=0||o.i.length!=0)&&(D(o.ka,u),D(o.ka,o.i),o.h.i.length=0,O(o.i),o.i.length=0),o.l.ra()}}function nd(o,u,h){var f=h instanceof sn?ft(h):new sn(h);if(f.g!="")u&&(f.g=u+"."+f.g),xi(f,f.s);else{var A=c.location;f=A.protocol,u=u?u+"."+A.hostname:A.hostname,A=+A.port;var S=new sn(null);f&&Oi(S,f),u&&(S.g=u),A&&xi(S,A),h&&(S.l=h),f=S}return h=o.D,u=o.ya,h&&u&&ne(f,h,u),ne(f,"VER",o.la),Dr(o,f),f}function rd(o,u,h){if(u&&!o.J)throw Error("Can't create secondary domain capable XhrIo object.");return u=o.Ca&&!o.pa?new se(new Sr({eb:h})):new se(o.pa),u.Ha(o.J),u}n.isActive=function(){return!!this.l&&this.l.isActive(this)};function id(){}n=id.prototype,n.ua=function(){},n.ta=function(){},n.sa=function(){},n.ra=function(){},n.isActive=function(){return!0},n.Na=function(){};function qi(){}qi.prototype.g=function(o,u){return new Ne(o,u)};function Ne(o,u){we.call(this),this.g=new Hh(u),this.l=o,this.h=u&&u.messageUrlParams||null,o=u&&u.messageHeaders||null,u&&u.clientProtocolHeaderRequired&&(o?o["X-Client-Protocol"]="webchannel":o={"X-Client-Protocol":"webchannel"}),this.g.o=o,o=u&&u.initMessageHeaders||null,u&&u.messageContentType&&(o?o["X-WebChannel-Content-Type"]=u.messageContentType:o={"X-WebChannel-Content-Type":u.messageContentType}),u&&u.va&&(o?o["X-WebChannel-Client-Profile"]=u.va:o={"X-WebChannel-Client-Profile":u.va}),this.g.S=o,(o=u&&u.Sb)&&!L(o)&&(this.g.m=o),this.v=u&&u.supportsCrossDomainXhr||!1,this.u=u&&u.sendRawJson||!1,(u=u&&u.httpSessionIdParam)&&!L(u)&&(this.g.D=u,o=this.h,o!==null&&u in o&&(o=this.h,u in o&&delete o[u])),this.j=new Pn(this)}k(Ne,we),Ne.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},Ne.prototype.close=function(){Xo(this.g)},Ne.prototype.o=function(o){var u=this.g;if(typeof o=="string"){var h={};h.__data__=o,o=h}else this.u&&(h={},h.__data__=Bo(o),o=h);u.i.push(new I_(u.Ya++,o)),u.G==3&&Ui(u)},Ne.prototype.N=function(){this.g.l=null,delete this.j,Xo(this.g),delete this.g,Ne.aa.N.call(this)};function sd(o){$o.call(this),o.__headers__&&(this.headers=o.__headers__,this.statusCode=o.__status__,delete o.__headers__,delete o.__status__);var u=o.__sm__;if(u){e:{for(let h in u){o=h;break e}o=void 0}(this.i=o)&&(o=this.i,u=u!==null&&o in u?u[o]:void 0),this.data=u}else this.data=o}k(sd,$o);function od(){jo.call(this),this.status=1}k(od,jo);function Pn(o){this.g=o}k(Pn,id),Pn.prototype.ua=function(){Pe(this.g,"a")},Pn.prototype.ta=function(o){Pe(this.g,new sd(o))},Pn.prototype.sa=function(o){Pe(this.g,new od)},Pn.prototype.ra=function(){Pe(this.g,"b")},qi.prototype.createWebChannel=qi.prototype.g,Ne.prototype.send=Ne.prototype.o,Ne.prototype.open=Ne.prototype.m,Ne.prototype.close=Ne.prototype.close,kc=At.createWebChannelTransport=function(){return new qi},Cc=At.getStatEventTarget=function(){return Ci()},Pc=At.Event=nn,As=At.Stat={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},ki.NO_ERROR=0,ki.TIMEOUT=8,ki.HTTP_ERROR=6,Yr=At.ErrorCode=ki,Th.COMPLETE="complete",Sc=At.EventType=Th,_h.EventType=Ir,Ir.OPEN="a",Ir.CLOSE="b",Ir.ERROR="c",Ir.MESSAGE="d",we.prototype.listen=we.prototype.K,Un=At.WebChannel=_h,fT=At.FetchXmlHttpFactory=Sr,se.prototype.listenOnce=se.prototype.L,se.prototype.getLastError=se.prototype.Ka,se.prototype.getLastErrorCode=se.prototype.Ba,se.prototype.getStatus=se.prototype.Z,se.prototype.getResponseJson=se.prototype.Oa,se.prototype.getResponseText=se.prototype.oa,se.prototype.send=se.prototype.ea,se.prototype.setWithCredentials=se.prototype.Ha,Rc=At.XhrIo=se}).apply(typeof ws<"u"?ws:typeof self<"u"?self:typeof window<"u"?window:{});var Yf="@firebase/firestore",Jf="4.7.16";var pe=class{constructor(e){this.uid=e}isAuthenticated(){return this.uid!=null}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}};pe.UNAUTHENTICATED=new pe(null),pe.GOOGLE_CREDENTIALS=new pe("google-credentials-uid"),pe.FIRST_PARTY=new pe("first-party-uid"),pe.MOCK_USER=new pe("mock-user");var or="11.8.1";var _n=new Ze("@firebase/firestore");function Bn(){return _n.logLevel}function V(n,...e){if(_n.logLevel<=G.DEBUG){let t=e.map(El);_n.debug(`Firestore (${or}): ${n}`,...t)}}function bt(n,...e){if(_n.logLevel<=G.ERROR){let t=e.map(El);_n.error(`Firestore (${or}): ${n}`,...t)}}function Hn(n,...e){if(_n.logLevel<=G.WARN){let t=e.map(El);_n.warn(`Firestore (${or}): ${n}`,...t)}}function El(n){if(typeof n=="string")return n;try{return function(t){return JSON.stringify(t)}(n)}catch{return n}}function U(n,e,t){let r="Unexpected state";typeof e=="string"?r=e:t=e,$p(n,r,t)}function $p(n,e,t){let r=`FIRESTORE (${or}) INTERNAL ASSERTION FAILED: ${e} (ID: ${n.toString(16)})`;if(t!==void 0)try{r+=" CONTEXT: "+JSON.stringify(t)}catch{r+=" CONTEXT: "+t}throw bt(r),new Error(r)}function X(n,e,t,r){let i="Unexpected state";typeof t=="string"?i=t:r=t,n||$p(e,i,r)}function $(n,e){return n}var P={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"},x=class extends ke{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}};var We=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};var Ds=class{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${e}`)}},Vc=class{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable(()=>t(pe.UNAUTHENTICATED))}shutdown(){}},Lc=class{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable(()=>t(this.token.user))}shutdown(){this.changeListener=null}},Mc=class{constructor(e){this.t=e,this.currentUser=pe.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(e,t){X(this.o===void 0,42304);let r=this.i,i=l=>this.i!==r?(r=this.i,t(l)):Promise.resolve(),s=new We;this.o=()=>{this.i++,this.currentUser=this.u(),s.resolve(),s=new We,e.enqueueRetryable(()=>i(this.currentUser))};let a=()=>{let l=s;e.enqueueRetryable(()=>g(this,null,function*(){yield l.promise,yield i(this.currentUser)}))},c=l=>{V("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=l,this.o&&(this.auth.addAuthTokenListener(this.o),a())};this.t.onInit(l=>c(l)),setTimeout(()=>{if(!this.auth){let l=this.t.getImmediate({optional:!0});l?c(l):(V("FirebaseAuthCredentialsProvider","Auth not yet detected"),s.resolve(),s=new We)}},0),a()}getToken(){let e=this.i,t=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(t).then(r=>this.i!==e?(V("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):r?(X(typeof r.accessToken=="string",31837,{l:r}),new Ds(r.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){let e=this.auth&&this.auth.getUid();return X(e===null||typeof e=="string",2055,{h:e}),new pe(e)}},Fc=class{constructor(e,t,r){this.P=e,this.T=t,this.I=r,this.type="FirstParty",this.user=pe.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);let e=this.R();return e&&this.A.set("Authorization",e),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}},Uc=class{constructor(e,t,r){this.P=e,this.T=t,this.I=r}getToken(){return Promise.resolve(new Fc(this.P,this.T,this.I))}start(e,t){e.enqueueRetryable(()=>t(pe.FIRST_PARTY))}shutdown(){}invalidateToken(){}},Ns=class{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&e.length>0&&this.headers.set("x-firebase-appcheck",this.value)}},Bc=class{constructor(e,t){this.V=t,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,Ie(e)&&e.settings.appCheckToken&&(this.p=e.settings.appCheckToken)}start(e,t){X(this.o===void 0,3512);let r=s=>{s.error!=null&&V("FirebaseAppCheckTokenProvider",`Error getting App Check token; using placeholder token instead. Error: ${s.error.message}`);let a=s.token!==this.m;return this.m=s.token,V("FirebaseAppCheckTokenProvider",`Received ${a?"new":"existing"} token.`),a?t(s.token):Promise.resolve()};this.o=s=>{e.enqueueRetryable(()=>r(s))};let i=s=>{V("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=s,this.o&&this.appCheck.addTokenListener(this.o)};this.V.onInit(s=>i(s)),setTimeout(()=>{if(!this.appCheck){let s=this.V.getImmediate({optional:!0});s?i(s):V("FirebaseAppCheckTokenProvider","AppCheck not yet detected")}},0)}getToken(){if(this.p)return Promise.resolve(new Ns(this.p));let e=this.forceRefresh;return this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then(t=>t?(X(typeof t.token=="string",44558,{tokenResult:t}),this.m=t.token,new Ns(t.token)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}};function pT(n){let e=typeof self<"u"&&(self.crypto||self.msCrypto),t=new Uint8Array(n);if(e&&typeof e.getRandomValues=="function")e.getRandomValues(t);else for(let r=0;r<n;r++)t[r]=Math.floor(256*Math.random());return t}function jp(){return new TextEncoder}var Os=class{static newId(){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t=62*Math.floor(4.129032258064516),r="";for(;r.length<20;){let i=pT(40);for(let s=0;s<i.length;++s)r.length<20&&i[s]<t&&(r+=e.charAt(i[s]%62))}return r}};function W(n,e){return n<e?-1:n>e?1:0}function qc(n,e){let t=0;for(;t<n.length&&t<e.length;){let r=n.codePointAt(t),i=e.codePointAt(t);if(r!==i){if(r<128&&i<128)return W(r,i);{let s=jp(),a=mT(s.encode(Xf(n,t)),s.encode(Xf(e,t)));return a!==0?a:W(r,i)}}t+=r>65535?2:1}return W(n.length,e.length)}function Xf(n,e){return n.codePointAt(e)>65535?n.substring(e,e+2):n.substring(e,e+1)}function mT(n,e){for(let t=0;t<n.length&&t<e.length;++t)if(n[t]!==e[t])return W(n[t],e[t]);return W(n.length,e.length)}function Kn(n,e,t){return n.length===e.length&&n.every((r,i)=>t(r,e[i]))}var Zf=-62135596800,ep=1e6,ve=class n{static now(){return n.fromMillis(Date.now())}static fromDate(e){return n.fromMillis(e.getTime())}static fromMillis(e){let t=Math.floor(e/1e3),r=Math.floor((e-1e3*t)*ep);return new n(t,r)}constructor(e,t){if(this.seconds=e,this.nanoseconds=t,t<0)throw new x(P.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(t>=1e9)throw new x(P.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<Zf)throw new x(P.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e);if(e>=253402300800)throw new x(P.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/ep}_compareTo(e){return this.seconds===e.seconds?W(this.nanoseconds,e.nanoseconds):W(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{seconds:this.seconds,nanoseconds:this.nanoseconds}}valueOf(){let e=this.seconds-Zf;return String(e).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}};var j=class n{static fromTimestamp(e){return new n(e)}static min(){return new n(new ve(0,0))}static max(){return new n(new ve(253402300799,999999999))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}};var tp="__name__",xs=class n{constructor(e,t,r){t===void 0?t=0:t>e.length&&U(637,{offset:t,range:e.length}),r===void 0?r=e.length-t:r>e.length-t&&U(1746,{length:r,range:e.length-t}),this.segments=e,this.offset=t,this.len=r}get length(){return this.len}isEqual(e){return n.comparator(this,e)===0}child(e){let t=this.segments.slice(this.offset,this.limit());return e instanceof n?e.forEach(r=>{t.push(r)}):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return e=e===void 0?1:e,this.construct(this.segments,this.offset+e,this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return this.length===0}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,r=this.limit();t<r;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(e,t){let r=Math.min(e.length,t.length);for(let i=0;i<r;i++){let s=n.compareSegments(e.get(i),t.get(i));if(s!==0)return s}return W(e.length,t.length)}static compareSegments(e,t){let r=n.isNumericId(e),i=n.isNumericId(t);return r&&!i?-1:!r&&i?1:r&&i?n.extractNumericId(e).compare(n.extractNumericId(t)):qc(e,t)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return wt.fromString(e.substring(4,e.length-2))}},ie=class n extends xs{construct(e,t,r){return new n(e,t,r)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){let t=[];for(let r of e){if(r.indexOf("//")>=0)throw new x(P.INVALID_ARGUMENT,`Invalid segment (${r}). Paths must not contain // in them.`);t.push(...r.split("/").filter(i=>i.length>0))}return new n(t)}static emptyPath(){return new n([])}},gT=/^[_a-zA-Z][_a-zA-Z0-9]*$/,Ve=class n extends xs{construct(e,t,r){return new n(e,t,r)}static isValidIdentifier(e){return gT.test(e)}canonicalString(){return this.toArray().map(e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),n.isValidIdentifier(e)||(e="`"+e+"`"),e)).join(".")}toString(){return this.canonicalString()}isKeyField(){return this.length===1&&this.get(0)===tp}static keyField(){return new n([tp])}static fromServerFormat(e){let t=[],r="",i=0,s=()=>{if(r.length===0)throw new x(P.INVALID_ARGUMENT,`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);t.push(r),r=""},a=!1;for(;i<e.length;){let c=e[i];if(c==="\\"){if(i+1===e.length)throw new x(P.INVALID_ARGUMENT,"Path has trailing escape character: "+e);let l=e[i+1];if(l!=="\\"&&l!=="."&&l!=="`")throw new x(P.INVALID_ARGUMENT,"Path has invalid escape sequence: "+e);r+=l,i+=2}else c==="`"?(a=!a,i++):c!=="."||a?(r+=c,i++):(s(),i++)}if(s(),a)throw new x(P.INVALID_ARGUMENT,"Unterminated ` in path: "+e);return new n(t)}static emptyPath(){return new n([])}};var M=class n{constructor(e){this.path=e}static fromPath(e){return new n(ie.fromString(e))}static fromName(e){return new n(ie.fromString(e).popFirst(5))}static empty(){return new n(ie.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return this.path.length>=2&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return e!==null&&ie.comparator(this.path,e.path)===0}toString(){return this.path.toString()}static comparator(e,t){return ie.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new n(new ie(e.slice()))}};var ni=-1,$c=class{constructor(e,t,r,i){this.indexId=e,this.collectionGroup=t,this.fields=r,this.indexState=i}};$c.UNKNOWN_ID=-1;function _T(n,e){let t=n.toTimestamp().seconds,r=n.toTimestamp().nanoseconds+1,i=j.fromTimestamp(r===1e9?new ve(t+1,0):new ve(t,r));return new yn(i,M.empty(),e)}function yT(n){return new yn(n.readTime,n.key,ni)}var yn=class n{constructor(e,t,r){this.readTime=e,this.documentKey=t,this.largestBatchId=r}static min(){return new n(j.min(),M.empty(),ni)}static max(){return new n(j.max(),M.empty(),ni)}};function IT(n,e){let t=n.readTime.compareTo(e.readTime);return t!==0?t:(t=M.comparator(n.documentKey,e.documentKey),t!==0?t:W(n.largestBatchId,e.largestBatchId))}var vT="The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab.",jc=class{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach(e=>e())}};function ar(n){return g(this,null,function*(){if(n.code!==P.FAILED_PRECONDITION||n.message!==vT)throw n;V("LocalStore","Unexpectedly lost primary lease")})}var C=class n{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e(t=>{this.isDone=!0,this.result=t,this.nextCallback&&this.nextCallback(t)},t=>{this.isDone=!0,this.error=t,this.catchCallback&&this.catchCallback(t)})}catch(e){return this.next(void 0,e)}next(e,t){return this.callbackAttached&&U(59440),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(t,this.error):this.wrapSuccess(e,this.result):new n((r,i)=>{this.nextCallback=s=>{this.wrapSuccess(e,s).next(r,i)},this.catchCallback=s=>{this.wrapFailure(t,s).next(r,i)}})}toPromise(){return new Promise((e,t)=>{this.next(e,t)})}wrapUserFunction(e){try{let t=e();return t instanceof n?t:n.resolve(t)}catch(t){return n.reject(t)}}wrapSuccess(e,t){return e?this.wrapUserFunction(()=>e(t)):n.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction(()=>e(t)):n.reject(t)}static resolve(e){return new n((t,r)=>{t(e)})}static reject(e){return new n((t,r)=>{r(e)})}static waitFor(e){return new n((t,r)=>{let i=0,s=0,a=!1;e.forEach(c=>{++i,c.next(()=>{++s,a&&s===i&&t()},l=>r(l))}),a=!0,s===i&&t()})}static or(e){let t=n.resolve(!1);for(let r of e)t=t.next(i=>i?n.resolve(i):r());return t}static forEach(e,t){let r=[];return e.forEach((i,s)=>{r.push(t.call(this,i,s))}),this.waitFor(r)}static mapArray(e,t){return new n((r,i)=>{let s=e.length,a=new Array(s),c=0;for(let l=0;l<s;l++){let d=l;t(e[d]).next(p=>{a[d]=p,++c,c===s&&r(a)},p=>i(p))}})}static doWhile(e,t){return new n((r,i)=>{let s=()=>{e()===!0?t().next(()=>{s()},i):r()};s()})}};function ET(n){let e=n.match(/Android ([\d.]+)/i),t=e?e[1].split(".").slice(0,2).join("."):"-1";return Number(t)}function cr(n){return n.name==="IndexedDbTransactionError"}var Tl=(()=>{class n{constructor(t,r){this.previousValue=t,r&&(r.sequenceNumberHandler=i=>this.ue(i),this.ce=i=>r.writeSequenceNumber(i))}ue(t){return this.previousValue=Math.max(t,this.previousValue),this.previousValue}next(){let t=++this.previousValue;return this.ce&&this.ce(t),t}}n.le=-1;return n})(),wl=-1;function go(n){return n==null}function ri(n){return n===0&&1/n==-1/0}function TT(n){return typeof n=="number"&&Number.isInteger(n)&&!ri(n)&&n<=Number.MAX_SAFE_INTEGER&&n>=Number.MIN_SAFE_INTEGER}var zp="";function wT(n){let e="";for(let t=0;t<n.length;t++)e.length>0&&(e=np(e)),e=AT(n.get(t),e);return np(e)}function AT(n,e){let t=e,r=n.length;for(let i=0;i<r;i++){let s=n.charAt(i);switch(s){case"\0":t+="";break;case zp:t+="";break;default:t+=s}}return t}function np(n){return n+zp+""}var bT="remoteDocuments",Wp="owner";var Gp="mutationQueues";var Hp="mutations";var Kp="documentMutations",RT="remoteDocumentsV14";var Qp="remoteDocumentGlobal";var Yp="targets";var Jp="targetDocuments";var Xp="targetGlobal",Zp="collectionParents";var em="clientMetadata";var tm="bundles";var nm="namedQueries";var ST="indexConfiguration";var PT="indexState";var CT="indexEntries";var rm="documentOverlays";var kT="globals";var DT=[Gp,Hp,Kp,bT,Yp,Wp,Xp,Jp,em,Qp,Zp,tm,nm],AS=[...DT,rm],NT=[Gp,Hp,Kp,RT,Yp,Wp,Xp,Jp,em,Qp,Zp,tm,nm,rm],OT=NT,xT=[...OT,ST,PT,CT];var bS=[...xT,kT];function rp(n){let e=0;for(let t in n)Object.prototype.hasOwnProperty.call(n,t)&&e++;return e}function tn(n,e){for(let t in n)Object.prototype.hasOwnProperty.call(n,t)&&e(t,n[t])}function im(n){for(let e in n)if(Object.prototype.hasOwnProperty.call(n,e))return!1;return!0}var oe=class n{constructor(e,t){this.comparator=e,this.root=t||st.EMPTY}insert(e,t){return new n(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,st.BLACK,null,null))}remove(e){return new n(this.comparator,this.root.remove(e,this.comparator).copy(null,null,st.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){let r=this.comparator(e,t.key);if(r===0)return t.value;r<0?t=t.left:r>0&&(t=t.right)}return null}indexOf(e){let t=0,r=this.root;for(;!r.isEmpty();){let i=this.comparator(e,r.key);if(i===0)return t+r.left.size;i<0?r=r.left:(t+=r.left.size+1,r=r.right)}return-1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(e){this.inorderTraversal((t,r)=>(e(t,r),!1))}toString(){let e=[];return this.inorderTraversal((t,r)=>(e.push(`${t}:${r}`),!1)),`{${e.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new zn(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new zn(this.root,e,this.comparator,!1)}getReverseIterator(){return new zn(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new zn(this.root,e,this.comparator,!0)}},zn=class{constructor(e,t,r,i){this.isReverse=i,this.nodeStack=[];let s=1;for(;!e.isEmpty();)if(s=t?r(e.key,t):1,t&&i&&(s*=-1),s<0)e=this.isReverse?e.left:e.right;else{if(s===0){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop(),t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return this.nodeStack.length>0}peek(){if(this.nodeStack.length===0)return null;let e=this.nodeStack[this.nodeStack.length-1];return{key:e.key,value:e.value}}},st=class n{constructor(e,t,r,i,s){this.key=e,this.value=t,this.color=r!=null?r:n.RED,this.left=i!=null?i:n.EMPTY,this.right=s!=null?s:n.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,r,i,s){return new n(e!=null?e:this.key,t!=null?t:this.value,r!=null?r:this.color,i!=null?i:this.left,s!=null?s:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,r){let i=this,s=r(e,i.key);return i=s<0?i.copy(null,null,null,i.left.insert(e,t,r),null):s===0?i.copy(null,t,null,null,null):i.copy(null,null,null,null,i.right.insert(e,t,r)),i.fixUp()}removeMin(){if(this.left.isEmpty())return n.EMPTY;let e=this;return e.left.isRed()||e.left.left.isRed()||(e=e.moveRedLeft()),e=e.copy(null,null,null,e.left.removeMin(),null),e.fixUp()}remove(e,t){let r,i=this;if(t(e,i.key)<0)i.left.isEmpty()||i.left.isRed()||i.left.left.isRed()||(i=i.moveRedLeft()),i=i.copy(null,null,null,i.left.remove(e,t),null);else{if(i.left.isRed()&&(i=i.rotateRight()),i.right.isEmpty()||i.right.isRed()||i.right.left.isRed()||(i=i.moveRedRight()),t(e,i.key)===0){if(i.right.isEmpty())return n.EMPTY;r=i.right.min(),i=i.copy(r.key,r.value,null,null,i.right.removeMin())}i=i.copy(null,null,null,null,i.right.remove(e,t))}return i.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e.right.isRed()&&!e.left.isRed()&&(e=e.rotateLeft()),e.left.isRed()&&e.left.left.isRed()&&(e=e.rotateRight()),e.left.isRed()&&e.right.isRed()&&(e=e.colorFlip()),e}moveRedLeft(){let e=this.colorFlip();return e.right.left.isRed()&&(e=e.copy(null,null,null,null,e.right.rotateRight()),e=e.rotateLeft(),e=e.colorFlip()),e}moveRedRight(){let e=this.colorFlip();return e.left.left.isRed()&&(e=e.rotateRight(),e=e.colorFlip()),e}rotateLeft(){let e=this.copy(null,null,n.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){let e=this.copy(null,null,n.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){let e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){let e=this.check();return Math.pow(2,e)<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw U(43730,{key:this.key,value:this.value});if(this.right.isRed())throw U(14113,{key:this.key,value:this.value});let e=this.left.check();if(e!==this.right.check())throw U(27949);return e+(this.isRed()?0:1)}};st.EMPTY=null,st.RED=!0,st.BLACK=!1;st.EMPTY=new class{constructor(){this.size=0}get key(){throw U(57766)}get value(){throw U(16141)}get color(){throw U(16727)}get left(){throw U(29726)}get right(){throw U(36894)}copy(e,t,r,i,s){return this}insert(e,t,r){return new st(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};var me=class n{constructor(e){this.comparator=e,this.data=new oe(this.comparator)}has(e){return this.data.get(e)!==null}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(e){this.data.inorderTraversal((t,r)=>(e(t),!1))}forEachInRange(e,t){let r=this.data.getIteratorFrom(e[0]);for(;r.hasNext();){let i=r.getNext();if(this.comparator(i.key,e[1])>=0)return;t(i.key)}}forEachWhile(e,t){let r;for(r=t!==void 0?this.data.getIteratorFrom(t):this.data.getIterator();r.hasNext();)if(!e(r.getNext().key))return}firstAfterOrEqual(e){let t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new Vs(this.data.getIterator())}getIteratorFrom(e){return new Vs(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach(r=>{t=t.add(r)}),t}isEqual(e){if(!(e instanceof n)||this.size!==e.size)return!1;let t=this.data.getIterator(),r=e.data.getIterator();for(;t.hasNext();){let i=t.getNext().key,s=r.getNext().key;if(this.comparator(i,s)!==0)return!1}return!0}toArray(){let e=[];return this.forEach(t=>{e.push(t)}),e}toString(){let e=[];return this.forEach(t=>e.push(t)),"SortedSet("+e.toString()+")"}copy(e){let t=new n(this.comparator);return t.data=e,t}},Vs=class{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}};var Ue=class n{constructor(e){this.fields=e,e.sort(Ve.comparator)}static empty(){return new n([])}unionWith(e){let t=new me(Ve.comparator);for(let r of this.fields)t=t.add(r);for(let r of e)t=t.add(r);return new n(t.toArray())}covers(e){for(let t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return Kn(this.fields,e.fields,(t,r)=>t.isEqual(r))}};var Ls=class extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}};var Se=class n{constructor(e){this.binaryString=e}static fromBase64String(e){let t=function(i){try{return atob(i)}catch(s){throw typeof DOMException<"u"&&s instanceof DOMException?new Ls("Invalid base64 string: "+s):s}}(e);return new n(t)}static fromUint8Array(e){let t=function(i){let s="";for(let a=0;a<i.length;++a)s+=String.fromCharCode(i[a]);return s}(e);return new n(t)}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return function(t){return btoa(t)}(this.binaryString)}toUint8Array(){return function(t){let r=new Uint8Array(t.length);for(let i=0;i<t.length;i++)r[i]=t.charCodeAt(i);return r}(this.binaryString)}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return W(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}};Se.EMPTY_BYTE_STRING=new Se("");var VT=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function Rt(n){if(X(!!n,39018),typeof n=="string"){let e=0,t=VT.exec(n);if(X(!!t,46558,{timestamp:n}),t[1]){let i=t[1];i=(i+"000000000").substr(0,9),e=Number(i)}let r=new Date(n);return{seconds:Math.floor(r.getTime()/1e3),nanos:e}}return{seconds:re(n.seconds),nanos:re(n.nanos)}}function re(n){return typeof n=="number"?n:typeof n=="string"?Number(n):0}function St(n){return typeof n=="string"?Se.fromBase64String(n):Se.fromUint8Array(n)}var sm="server_timestamp",om="__type__",am="__previous_value__",cm="__local_write_time__";function Al(n){var e,t;return((t=(((e=n==null?void 0:n.mapValue)===null||e===void 0?void 0:e.fields)||{})[om])===null||t===void 0?void 0:t.stringValue)===sm}function _o(n){let e=n.mapValue.fields[am];return Al(e)?_o(e):e}function ii(n){let e=Rt(n.mapValue.fields[cm].timestampValue);return new ve(e.seconds,e.nanos)}var zc=class{constructor(e,t,r,i,s,a,c,l,d,p){this.databaseId=e,this.appId=t,this.persistenceKey=r,this.host=i,this.ssl=s,this.forceLongPolling=a,this.autoDetectLongPolling=c,this.longPollingOptions=l,this.useFetchStreams=d,this.isUsingEmulator=p}},Ms="(default)",Fs=class n{constructor(e,t){this.projectId=e,this.database=t||Ms}static empty(){return new n("","")}get isDefaultDatabase(){return this.database===Ms}isEqual(e){return e instanceof n&&e.projectId===this.projectId&&e.database===this.database}};var bl="__type__",um="__max__",bs={mapValue:{fields:{__type__:{stringValue:um}}}},Rl="__vector__",Qn="value";function Yt(n){return"nullValue"in n?0:"booleanValue"in n?1:"integerValue"in n||"doubleValue"in n?2:"timestampValue"in n?3:"stringValue"in n?5:"bytesValue"in n?6:"referenceValue"in n?7:"geoPointValue"in n?8:"arrayValue"in n?9:"mapValue"in n?Al(n)?4:hm(n)?9007199254740991:lm(n)?10:11:U(28295,{value:n})}function ut(n,e){if(n===e)return!0;let t=Yt(n);if(t!==Yt(e))return!1;switch(t){case 0:case 9007199254740991:return!0;case 1:return n.booleanValue===e.booleanValue;case 4:return ii(n).isEqual(ii(e));case 3:return function(i,s){if(typeof i.timestampValue=="string"&&typeof s.timestampValue=="string"&&i.timestampValue.length===s.timestampValue.length)return i.timestampValue===s.timestampValue;let a=Rt(i.timestampValue),c=Rt(s.timestampValue);return a.seconds===c.seconds&&a.nanos===c.nanos}(n,e);case 5:return n.stringValue===e.stringValue;case 6:return function(i,s){return St(i.bytesValue).isEqual(St(s.bytesValue))}(n,e);case 7:return n.referenceValue===e.referenceValue;case 8:return function(i,s){return re(i.geoPointValue.latitude)===re(s.geoPointValue.latitude)&&re(i.geoPointValue.longitude)===re(s.geoPointValue.longitude)}(n,e);case 2:return function(i,s){if("integerValue"in i&&"integerValue"in s)return re(i.integerValue)===re(s.integerValue);if("doubleValue"in i&&"doubleValue"in s){let a=re(i.doubleValue),c=re(s.doubleValue);return a===c?ri(a)===ri(c):isNaN(a)&&isNaN(c)}return!1}(n,e);case 9:return Kn(n.arrayValue.values||[],e.arrayValue.values||[],ut);case 10:case 11:return function(i,s){let a=i.mapValue.fields||{},c=s.mapValue.fields||{};if(rp(a)!==rp(c))return!1;for(let l in a)if(a.hasOwnProperty(l)&&(c[l]===void 0||!ut(a[l],c[l])))return!1;return!0}(n,e);default:return U(52216,{left:n})}}function si(n,e){return(n.values||[]).find(t=>ut(t,e))!==void 0}function Yn(n,e){if(n===e)return 0;let t=Yt(n),r=Yt(e);if(t!==r)return W(t,r);switch(t){case 0:case 9007199254740991:return 0;case 1:return W(n.booleanValue,e.booleanValue);case 2:return function(s,a){let c=re(s.integerValue||s.doubleValue),l=re(a.integerValue||a.doubleValue);return c<l?-1:c>l?1:c===l?0:isNaN(c)?isNaN(l)?0:-1:1}(n,e);case 3:return ip(n.timestampValue,e.timestampValue);case 4:return ip(ii(n),ii(e));case 5:return qc(n.stringValue,e.stringValue);case 6:return function(s,a){let c=St(s),l=St(a);return c.compareTo(l)}(n.bytesValue,e.bytesValue);case 7:return function(s,a){let c=s.split("/"),l=a.split("/");for(let d=0;d<c.length&&d<l.length;d++){let p=W(c[d],l[d]);if(p!==0)return p}return W(c.length,l.length)}(n.referenceValue,e.referenceValue);case 8:return function(s,a){let c=W(re(s.latitude),re(a.latitude));return c!==0?c:W(re(s.longitude),re(a.longitude))}(n.geoPointValue,e.geoPointValue);case 9:return sp(n.arrayValue,e.arrayValue);case 10:return function(s,a){var c,l,d,p;let _=s.fields||{},I=a.fields||{},R=(c=_[Qn])===null||c===void 0?void 0:c.arrayValue,k=(l=I[Qn])===null||l===void 0?void 0:l.arrayValue,O=W(((d=R==null?void 0:R.values)===null||d===void 0?void 0:d.length)||0,((p=k==null?void 0:k.values)===null||p===void 0?void 0:p.length)||0);return O!==0?O:sp(R,k)}(n.mapValue,e.mapValue);case 11:return function(s,a){if(s===bs.mapValue&&a===bs.mapValue)return 0;if(s===bs.mapValue)return 1;if(a===bs.mapValue)return-1;let c=s.fields||{},l=Object.keys(c),d=a.fields||{},p=Object.keys(d);l.sort(),p.sort();for(let _=0;_<l.length&&_<p.length;++_){let I=qc(l[_],p[_]);if(I!==0)return I;let R=Yn(c[l[_]],d[p[_]]);if(R!==0)return R}return W(l.length,p.length)}(n.mapValue,e.mapValue);default:throw U(23264,{Pe:t})}}function ip(n,e){if(typeof n=="string"&&typeof e=="string"&&n.length===e.length)return W(n,e);let t=Rt(n),r=Rt(e),i=W(t.seconds,r.seconds);return i!==0?i:W(t.nanos,r.nanos)}function sp(n,e){let t=n.values||[],r=e.values||[];for(let i=0;i<t.length&&i<r.length;++i){let s=Yn(t[i],r[i]);if(s)return s}return W(t.length,r.length)}function Jn(n){return Wc(n)}function Wc(n){return"nullValue"in n?"null":"booleanValue"in n?""+n.booleanValue:"integerValue"in n?""+n.integerValue:"doubleValue"in n?""+n.doubleValue:"timestampValue"in n?function(t){let r=Rt(t);return`time(${r.seconds},${r.nanos})`}(n.timestampValue):"stringValue"in n?n.stringValue:"bytesValue"in n?function(t){return St(t).toBase64()}(n.bytesValue):"referenceValue"in n?function(t){return M.fromName(t).toString()}(n.referenceValue):"geoPointValue"in n?function(t){return`geo(${t.latitude},${t.longitude})`}(n.geoPointValue):"arrayValue"in n?function(t){let r="[",i=!0;for(let s of t.values||[])i?i=!1:r+=",",r+=Wc(s);return r+"]"}(n.arrayValue):"mapValue"in n?function(t){let r=Object.keys(t.fields||{}).sort(),i="{",s=!0;for(let a of r)s?s=!1:i+=",",i+=`${a}:${Wc(t.fields[a])}`;return i+"}"}(n.mapValue):U(61005,{value:n})}function Ps(n){switch(Yt(n)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:let e=_o(n);return e?16+Ps(e):16;case 5:return 2*n.stringValue.length;case 6:return St(n.bytesValue).approximateByteSize();case 7:return n.referenceValue.length;case 9:return function(r){return(r.values||[]).reduce((i,s)=>i+Ps(s),0)}(n.arrayValue);case 10:case 11:return function(r){let i=0;return tn(r.fields,(s,a)=>{i+=s.length+Ps(a)}),i}(n.mapValue);default:throw U(13486,{value:n})}}function op(n,e){return{referenceValue:`projects/${n.projectId}/databases/${n.database}/documents/${e.path.canonicalString()}`}}function Gc(n){return!!n&&"integerValue"in n}function Sl(n){return!!n&&"arrayValue"in n}function ap(n){return!!n&&"nullValue"in n}function cp(n){return!!n&&"doubleValue"in n&&isNaN(Number(n.doubleValue))}function Cs(n){return!!n&&"mapValue"in n}function lm(n){var e,t;return((t=(((e=n==null?void 0:n.mapValue)===null||e===void 0?void 0:e.fields)||{})[bl])===null||t===void 0?void 0:t.stringValue)===Rl}function Xr(n){if(n.geoPointValue)return{geoPointValue:Object.assign({},n.geoPointValue)};if(n.timestampValue&&typeof n.timestampValue=="object")return{timestampValue:Object.assign({},n.timestampValue)};if(n.mapValue){let e={mapValue:{fields:{}}};return tn(n.mapValue.fields,(t,r)=>e.mapValue.fields[t]=Xr(r)),e}if(n.arrayValue){let e={arrayValue:{values:[]}};for(let t=0;t<(n.arrayValue.values||[]).length;++t)e.arrayValue.values[t]=Xr(n.arrayValue.values[t]);return e}return Object.assign({},n)}function hm(n){return(((n.mapValue||{}).fields||{}).__type__||{}).stringValue===um}var SS={mapValue:{fields:{[bl]:{stringValue:Rl},[Qn]:{arrayValue:{}}}}};var xe=class n{constructor(e){this.value=e}static empty(){return new n({mapValue:{}})}field(e){if(e.isEmpty())return this.value;{let t=this.value;for(let r=0;r<e.length-1;++r)if(t=(t.mapValue.fields||{})[e.get(r)],!Cs(t))return null;return t=(t.mapValue.fields||{})[e.lastSegment()],t||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=Xr(t)}setAll(e){let t=Ve.emptyPath(),r={},i=[];e.forEach((a,c)=>{if(!t.isImmediateParentOf(c)){let l=this.getFieldsMap(t);this.applyChanges(l,r,i),r={},i=[],t=c.popLast()}a?r[c.lastSegment()]=Xr(a):i.push(c.lastSegment())});let s=this.getFieldsMap(t);this.applyChanges(s,r,i)}delete(e){let t=this.field(e.popLast());Cs(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return ut(this.value,e.value)}getFieldsMap(e){let t=this.value;t.mapValue.fields||(t.mapValue={fields:{}});for(let r=0;r<e.length;++r){let i=t.mapValue.fields[e.get(r)];Cs(i)&&i.mapValue.fields||(i={mapValue:{fields:{}}},t.mapValue.fields[e.get(r)]=i),t=i}return t.mapValue.fields}applyChanges(e,t,r){tn(t,(i,s)=>e[i]=s);for(let i of r)delete e[i]}clone(){return new n(Xr(this.value))}};function dm(n){let e=[];return tn(n.fields,(t,r)=>{let i=new Ve([t]);if(Cs(r)){let s=dm(r.mapValue).fields;if(s.length===0)e.push(i);else for(let a of s)e.push(i.child(a))}else e.push(i)}),new Ue(e)}var Ge=class n{constructor(e,t,r,i,s,a,c){this.key=e,this.documentType=t,this.version=r,this.readTime=i,this.createTime=s,this.data=a,this.documentState=c}static newInvalidDocument(e){return new n(e,0,j.min(),j.min(),j.min(),xe.empty(),0)}static newFoundDocument(e,t,r,i){return new n(e,1,t,j.min(),r,i,0)}static newNoDocument(e,t){return new n(e,2,t,j.min(),j.min(),xe.empty(),0)}static newUnknownDocument(e,t){return new n(e,3,t,j.min(),j.min(),xe.empty(),2)}convertToFoundDocument(e,t){return!this.createTime.isEqual(j.min())||this.documentType!==2&&this.documentType!==0||(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=xe.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=xe.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=j.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return this.documentState===1}get hasCommittedMutations(){return this.documentState===2}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return this.documentType!==0}isFoundDocument(){return this.documentType===1}isNoDocument(){return this.documentType===2}isUnknownDocument(){return this.documentType===3}isEqual(e){return e instanceof n&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new n(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}};var Xn=class{constructor(e,t){this.position=e,this.inclusive=t}};function up(n,e,t){let r=0;for(let i=0;i<n.position.length;i++){let s=e[i],a=n.position[i];if(s.field.isKeyField()?r=M.comparator(M.fromName(a.referenceValue),t.key):r=Yn(a,t.data.field(s.field)),s.dir==="desc"&&(r*=-1),r!==0)break}return r}function lp(n,e){if(n===null)return e===null;if(e===null||n.inclusive!==e.inclusive||n.position.length!==e.position.length)return!1;for(let t=0;t<n.position.length;t++)if(!ut(n.position[t],e.position[t]))return!1;return!0}var In=class{constructor(e,t="asc"){this.field=e,this.dir=t}};function LT(n,e){return n.dir===e.dir&&n.field.isEqual(e.field)}var Us=class{},de=class n extends Us{constructor(e,t,r){super(),this.field=e,this.op=t,this.value=r}static create(e,t,r){return e.isKeyField()?t==="in"||t==="not-in"?this.createKeyFieldInFilter(e,t,r):new Kc(e,t,r):t==="array-contains"?new Jc(e,r):t==="in"?new Xc(e,r):t==="not-in"?new Zc(e,r):t==="array-contains-any"?new eu(e,r):new n(e,t,r)}static createKeyFieldInFilter(e,t,r){return t==="in"?new Qc(e,r):new Yc(e,r)}matches(e){let t=e.data.field(this.field);return this.op==="!="?t!==null&&t.nullValue===void 0&&this.matchesComparison(Yn(t,this.value)):t!==null&&Yt(this.value)===Yt(t)&&this.matchesComparison(Yn(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return e===0;case"!=":return e!==0;case">":return e>0;case">=":return e>=0;default:return U(47266,{operator:this.op})}}isInequality(){return["<","<=",">",">=","!=","not-in"].indexOf(this.op)>=0}getFlattenedFilters(){return[this]}getFilters(){return[this]}},He=class n extends Us{constructor(e,t){super(),this.filters=e,this.op=t,this.Te=null}static create(e,t){return new n(e,t)}matches(e){return fm(this)?this.filters.find(t=>!t.matches(e))===void 0:this.filters.find(t=>t.matches(e))!==void 0}getFlattenedFilters(){return this.Te!==null||(this.Te=this.filters.reduce((e,t)=>e.concat(t.getFlattenedFilters()),[])),this.Te}getFilters(){return Object.assign([],this.filters)}};function fm(n){return n.op==="and"}function pm(n){return MT(n)&&fm(n)}function MT(n){for(let e of n.filters)if(e instanceof He)return!1;return!0}function Hc(n){if(n instanceof de)return n.field.canonicalString()+n.op.toString()+Jn(n.value);if(pm(n))return n.filters.map(e=>Hc(e)).join(",");{let e=n.filters.map(t=>Hc(t)).join(",");return`${n.op}(${e})`}}function mm(n,e){return n instanceof de?function(r,i){return i instanceof de&&r.op===i.op&&r.field.isEqual(i.field)&&ut(r.value,i.value)}(n,e):n instanceof He?function(r,i){return i instanceof He&&r.op===i.op&&r.filters.length===i.filters.length?r.filters.reduce((s,a,c)=>s&&mm(a,i.filters[c]),!0):!1}(n,e):void U(19439)}function gm(n){return n instanceof de?function(t){return`${t.field.canonicalString()} ${t.op} ${Jn(t.value)}`}(n):n instanceof He?function(t){return t.op.toString()+" {"+t.getFilters().map(gm).join(" ,")+"}"}(n):"Filter"}var Kc=class extends de{constructor(e,t,r){super(e,t,r),this.key=M.fromName(r.referenceValue)}matches(e){let t=M.comparator(e.key,this.key);return this.matchesComparison(t)}},Qc=class extends de{constructor(e,t){super(e,"in",t),this.keys=_m("in",t)}matches(e){return this.keys.some(t=>t.isEqual(e.key))}},Yc=class extends de{constructor(e,t){super(e,"not-in",t),this.keys=_m("not-in",t)}matches(e){return!this.keys.some(t=>t.isEqual(e.key))}};function _m(n,e){var t;return(((t=e.arrayValue)===null||t===void 0?void 0:t.values)||[]).map(r=>M.fromName(r.referenceValue))}var Jc=class extends de{constructor(e,t){super(e,"array-contains",t)}matches(e){let t=e.data.field(this.field);return Sl(t)&&si(t.arrayValue,this.value)}},Xc=class extends de{constructor(e,t){super(e,"in",t)}matches(e){let t=e.data.field(this.field);return t!==null&&si(this.value.arrayValue,t)}},Zc=class extends de{constructor(e,t){super(e,"not-in",t)}matches(e){if(si(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;let t=e.data.field(this.field);return t!==null&&t.nullValue===void 0&&!si(this.value.arrayValue,t)}},eu=class extends de{constructor(e,t){super(e,"array-contains-any",t)}matches(e){let t=e.data.field(this.field);return!(!Sl(t)||!t.arrayValue.values)&&t.arrayValue.values.some(r=>si(this.value.arrayValue,r))}};var tu=class{constructor(e,t=null,r=[],i=[],s=null,a=null,c=null){this.path=e,this.collectionGroup=t,this.orderBy=r,this.filters=i,this.limit=s,this.startAt=a,this.endAt=c,this.Ie=null}};function hp(n,e=null,t=[],r=[],i=null,s=null,a=null){return new tu(n,e,t,r,i,s,a)}function Pl(n){let e=$(n);if(e.Ie===null){let t=e.path.canonicalString();e.collectionGroup!==null&&(t+="|cg:"+e.collectionGroup),t+="|f:",t+=e.filters.map(r=>Hc(r)).join(","),t+="|ob:",t+=e.orderBy.map(r=>function(s){return s.field.canonicalString()+s.dir}(r)).join(","),go(e.limit)||(t+="|l:",t+=e.limit),e.startAt&&(t+="|lb:",t+=e.startAt.inclusive?"b:":"a:",t+=e.startAt.position.map(r=>Jn(r)).join(",")),e.endAt&&(t+="|ub:",t+=e.endAt.inclusive?"a:":"b:",t+=e.endAt.position.map(r=>Jn(r)).join(",")),e.Ie=t}return e.Ie}function Cl(n,e){if(n.limit!==e.limit||n.orderBy.length!==e.orderBy.length)return!1;for(let t=0;t<n.orderBy.length;t++)if(!LT(n.orderBy[t],e.orderBy[t]))return!1;if(n.filters.length!==e.filters.length)return!1;for(let t=0;t<n.filters.length;t++)if(!mm(n.filters[t],e.filters[t]))return!1;return n.collectionGroup===e.collectionGroup&&!!n.path.isEqual(e.path)&&!!lp(n.startAt,e.startAt)&&lp(n.endAt,e.endAt)}function nu(n){return M.isDocumentKey(n.path)&&n.collectionGroup===null&&n.filters.length===0}var Jt=class{constructor(e,t=null,r=[],i=[],s=null,a="F",c=null,l=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=r,this.filters=i,this.limit=s,this.limitType=a,this.startAt=c,this.endAt=l,this.Ee=null,this.de=null,this.Ae=null,this.startAt,this.endAt}};function FT(n,e,t,r,i,s,a,c){return new Jt(n,e,t,r,i,s,a,c)}function kl(n){return new Jt(n)}function dp(n){return n.filters.length===0&&n.limit===null&&n.startAt==null&&n.endAt==null&&(n.explicitOrderBy.length===0||n.explicitOrderBy.length===1&&n.explicitOrderBy[0].field.isKeyField())}function ym(n){return n.collectionGroup!==null}function Zr(n){let e=$(n);if(e.Ee===null){e.Ee=[];let t=new Set;for(let s of e.explicitOrderBy)e.Ee.push(s),t.add(s.field.canonicalString());let r=e.explicitOrderBy.length>0?e.explicitOrderBy[e.explicitOrderBy.length-1].dir:"asc";(function(a){let c=new me(Ve.comparator);return a.filters.forEach(l=>{l.getFlattenedFilters().forEach(d=>{d.isInequality()&&(c=c.add(d.field))})}),c})(e).forEach(s=>{t.has(s.canonicalString())||s.isKeyField()||e.Ee.push(new In(s,r))}),t.has(Ve.keyField().canonicalString())||e.Ee.push(new In(Ve.keyField(),r))}return e.Ee}function ot(n){let e=$(n);return e.de||(e.de=UT(e,Zr(n))),e.de}function UT(n,e){if(n.limitType==="F")return hp(n.path,n.collectionGroup,e,n.filters,n.limit,n.startAt,n.endAt);{e=e.map(i=>{let s=i.dir==="desc"?"asc":"desc";return new In(i.field,s)});let t=n.endAt?new Xn(n.endAt.position,n.endAt.inclusive):null,r=n.startAt?new Xn(n.startAt.position,n.startAt.inclusive):null;return hp(n.path,n.collectionGroup,e,n.filters,n.limit,t,r)}}function ru(n,e){let t=n.filters.concat([e]);return new Jt(n.path,n.collectionGroup,n.explicitOrderBy.slice(),t,n.limit,n.limitType,n.startAt,n.endAt)}function Bs(n,e,t){return new Jt(n.path,n.collectionGroup,n.explicitOrderBy.slice(),n.filters.slice(),e,t,n.startAt,n.endAt)}function yo(n,e){return Cl(ot(n),ot(e))&&n.limitType===e.limitType}function Im(n){return`${Pl(ot(n))}|lt:${n.limitType}`}function qn(n){return`Query(target=${function(t){let r=t.path.canonicalString();return t.collectionGroup!==null&&(r+=" collectionGroup="+t.collectionGroup),t.filters.length>0&&(r+=`, filters: [${t.filters.map(i=>gm(i)).join(", ")}]`),go(t.limit)||(r+=", limit: "+t.limit),t.orderBy.length>0&&(r+=`, orderBy: [${t.orderBy.map(i=>function(a){return`${a.field.canonicalString()} (${a.dir})`}(i)).join(", ")}]`),t.startAt&&(r+=", startAt: ",r+=t.startAt.inclusive?"b:":"a:",r+=t.startAt.position.map(i=>Jn(i)).join(",")),t.endAt&&(r+=", endAt: ",r+=t.endAt.inclusive?"a:":"b:",r+=t.endAt.position.map(i=>Jn(i)).join(",")),`Target(${r})`}(ot(n))}; limitType=${n.limitType})`}function Io(n,e){return e.isFoundDocument()&&function(r,i){let s=i.key.path;return r.collectionGroup!==null?i.key.hasCollectionId(r.collectionGroup)&&r.path.isPrefixOf(s):M.isDocumentKey(r.path)?r.path.isEqual(s):r.path.isImmediateParentOf(s)}(n,e)&&function(r,i){for(let s of Zr(r))if(!s.field.isKeyField()&&i.data.field(s.field)===null)return!1;return!0}(n,e)&&function(r,i){for(let s of r.filters)if(!s.matches(i))return!1;return!0}(n,e)&&function(r,i){return!(r.startAt&&!function(a,c,l){let d=up(a,c,l);return a.inclusive?d<=0:d<0}(r.startAt,Zr(r),i)||r.endAt&&!function(a,c,l){let d=up(a,c,l);return a.inclusive?d>=0:d>0}(r.endAt,Zr(r),i))}(n,e)}function BT(n){return n.collectionGroup||(n.path.length%2==1?n.path.lastSegment():n.path.get(n.path.length-2))}function vm(n){return(e,t)=>{let r=!1;for(let i of Zr(n)){let s=qT(i,e,t);if(s!==0)return s;r=r||i.field.isKeyField()}return 0}}function qT(n,e,t){let r=n.field.isKeyField()?M.comparator(e.key,t.key):function(s,a,c){let l=a.data.field(s),d=c.data.field(s);return l!==null&&d!==null?Yn(l,d):U(42886)}(n.field,e,t);switch(n.dir){case"asc":return r;case"desc":return-1*r;default:return U(19790,{direction:n.dir})}}var Pt=class{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(e){let t=this.mapKeyFn(e),r=this.inner[t];if(r!==void 0){for(let[i,s]of r)if(this.equalsFn(i,e))return s}}has(e){return this.get(e)!==void 0}set(e,t){let r=this.mapKeyFn(e),i=this.inner[r];if(i===void 0)return this.inner[r]=[[e,t]],void this.innerSize++;for(let s=0;s<i.length;s++)if(this.equalsFn(i[s][0],e))return void(i[s]=[e,t]);i.push([e,t]),this.innerSize++}delete(e){let t=this.mapKeyFn(e),r=this.inner[t];if(r===void 0)return!1;for(let i=0;i<r.length;i++)if(this.equalsFn(r[i][0],e))return r.length===1?delete this.inner[t]:r.splice(i,1),this.innerSize--,!0;return!1}forEach(e){tn(this.inner,(t,r)=>{for(let[i,s]of r)e(i,s)})}isEmpty(){return im(this.inner)}size(){return this.innerSize}};var $T=new oe(M.comparator);function Ct(){return $T}var Em=new oe(M.comparator);function Jr(...n){let e=Em;for(let t of n)e=e.insert(t.key,t);return e}function Tm(n){let e=Em;return n.forEach((t,r)=>e=e.insert(t,r.overlayedDocument)),e}function pn(){return ei()}function wm(){return ei()}function ei(){return new Pt(n=>n.toString(),(n,e)=>n.isEqual(e))}var jT=new oe(M.comparator),zT=new me(M.comparator);function H(...n){let e=zT;for(let t of n)e=e.add(t);return e}var WT=new me(W);function GT(){return WT}function Dl(n,e){if(n.useProto3Json){if(isNaN(e))return{doubleValue:"NaN"};if(e===1/0)return{doubleValue:"Infinity"};if(e===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:ri(e)?"-0":e}}function Am(n){return{integerValue:""+n}}function HT(n,e){return TT(e)?Am(e):Dl(n,e)}var Zn=class{constructor(){this._=void 0}};function KT(n,e,t){return n instanceof vn?function(i,s){let a={fields:{[om]:{stringValue:sm},[cm]:{timestampValue:{seconds:i.seconds,nanos:i.nanoseconds}}}};return s&&Al(s)&&(s=_o(s)),s&&(a.fields[am]=s),{mapValue:a}}(t,e):n instanceof En?Rm(n,e):n instanceof Tn?Sm(n,e):function(i,s){let a=bm(i,s),c=fp(a)+fp(i.Re);return Gc(a)&&Gc(i.Re)?Am(c):Dl(i.serializer,c)}(n,e)}function QT(n,e,t){return n instanceof En?Rm(n,e):n instanceof Tn?Sm(n,e):t}function bm(n,e){return n instanceof er?function(r){return Gc(r)||function(s){return!!s&&"doubleValue"in s}(r)}(e)?e:{integerValue:0}:null}var vn=class extends Zn{},En=class extends Zn{constructor(e){super(),this.elements=e}};function Rm(n,e){let t=Pm(e);for(let r of n.elements)t.some(i=>ut(i,r))||t.push(r);return{arrayValue:{values:t}}}var Tn=class extends Zn{constructor(e){super(),this.elements=e}};function Sm(n,e){let t=Pm(e);for(let r of n.elements)t=t.filter(i=>!ut(i,r));return{arrayValue:{values:t}}}var er=class extends Zn{constructor(e,t){super(),this.serializer=e,this.Re=t}};function fp(n){return re(n.integerValue||n.doubleValue)}function Pm(n){return Sl(n)&&n.arrayValue.values?n.arrayValue.values.slice():[]}var iu=class{constructor(e,t){this.field=e,this.transform=t}};function YT(n,e){return n.field.isEqual(e.field)&&function(r,i){return r instanceof En&&i instanceof En||r instanceof Tn&&i instanceof Tn?Kn(r.elements,i.elements,ut):r instanceof er&&i instanceof er?ut(r.Re,i.Re):r instanceof vn&&i instanceof vn}(n.transform,e.transform)}var su=class{constructor(e,t){this.version=e,this.transformResults=t}},at=class n{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new n}static exists(e){return new n(void 0,e)}static updateTime(e){return new n(e)}get isNone(){return this.updateTime===void 0&&this.exists===void 0}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}};function ks(n,e){return n.updateTime!==void 0?e.isFoundDocument()&&e.version.isEqual(n.updateTime):n.exists===void 0||n.exists===e.isFoundDocument()}var tr=class{};function Cm(n,e){if(!n.hasLocalMutations||e&&e.fields.length===0)return null;if(e===null)return n.isNoDocument()?new oi(n.key,at.none()):new wn(n.key,n.data,at.none());{let t=n.data,r=xe.empty(),i=new me(Ve.comparator);for(let s of e.fields)if(!i.has(s)){let a=t.field(s);a===null&&s.length>1&&(s=s.popLast(),a=t.field(s)),a===null?r.delete(s):r.set(s,a),i=i.add(s)}return new lt(n.key,r,new Ue(i.toArray()),at.none())}}function JT(n,e,t){n instanceof wn?function(i,s,a){let c=i.value.clone(),l=mp(i.fieldTransforms,s,a.transformResults);c.setAll(l),s.convertToFoundDocument(a.version,c).setHasCommittedMutations()}(n,e,t):n instanceof lt?function(i,s,a){if(!ks(i.precondition,s))return void s.convertToUnknownDocument(a.version);let c=mp(i.fieldTransforms,s,a.transformResults),l=s.data;l.setAll(km(i)),l.setAll(c),s.convertToFoundDocument(a.version,l).setHasCommittedMutations()}(n,e,t):function(i,s,a){s.convertToNoDocument(a.version).setHasCommittedMutations()}(0,e,t)}function ti(n,e,t,r){return n instanceof wn?function(s,a,c,l){if(!ks(s.precondition,a))return c;let d=s.value.clone(),p=gp(s.fieldTransforms,l,a);return d.setAll(p),a.convertToFoundDocument(a.version,d).setHasLocalMutations(),null}(n,e,t,r):n instanceof lt?function(s,a,c,l){if(!ks(s.precondition,a))return c;let d=gp(s.fieldTransforms,l,a),p=a.data;return p.setAll(km(s)),p.setAll(d),a.convertToFoundDocument(a.version,p).setHasLocalMutations(),c===null?null:c.unionWith(s.fieldMask.fields).unionWith(s.fieldTransforms.map(_=>_.field))}(n,e,t,r):function(s,a,c){return ks(s.precondition,a)?(a.convertToNoDocument(a.version).setHasLocalMutations(),null):c}(n,e,t)}function XT(n,e){let t=null;for(let r of n.fieldTransforms){let i=e.data.field(r.field),s=bm(r.transform,i||null);s!=null&&(t===null&&(t=xe.empty()),t.set(r.field,s))}return t||null}function pp(n,e){return n.type===e.type&&!!n.key.isEqual(e.key)&&!!n.precondition.isEqual(e.precondition)&&!!function(r,i){return r===void 0&&i===void 0||!(!r||!i)&&Kn(r,i,(s,a)=>YT(s,a))}(n.fieldTransforms,e.fieldTransforms)&&(n.type===0?n.value.isEqual(e.value):n.type!==1||n.data.isEqual(e.data)&&n.fieldMask.isEqual(e.fieldMask))}var wn=class extends tr{constructor(e,t,r,i=[]){super(),this.key=e,this.value=t,this.precondition=r,this.fieldTransforms=i,this.type=0}getFieldMask(){return null}},lt=class extends tr{constructor(e,t,r,i,s=[]){super(),this.key=e,this.data=t,this.fieldMask=r,this.precondition=i,this.fieldTransforms=s,this.type=1}getFieldMask(){return this.fieldMask}};function km(n){let e=new Map;return n.fieldMask.fields.forEach(t=>{if(!t.isEmpty()){let r=n.data.field(t);e.set(t,r)}}),e}function mp(n,e,t){let r=new Map;X(n.length===t.length,32656,{Ve:t.length,me:n.length});for(let i=0;i<t.length;i++){let s=n[i],a=s.transform,c=e.data.field(s.field);r.set(s.field,QT(a,c,t[i]))}return r}function gp(n,e,t){let r=new Map;for(let i of n){let s=i.transform,a=t.data.field(i.field);r.set(i.field,KT(s,a,e))}return r}var oi=class extends tr{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}},ou=class extends tr{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}};var au=class{constructor(e,t,r,i){this.batchId=e,this.localWriteTime=t,this.baseMutations=r,this.mutations=i}applyToRemoteDocument(e,t){let r=t.mutationResults;for(let i=0;i<this.mutations.length;i++){let s=this.mutations[i];s.key.isEqual(e.key)&&JT(s,e,r[i])}}applyToLocalView(e,t){for(let r of this.baseMutations)r.key.isEqual(e.key)&&(t=ti(r,e,t,this.localWriteTime));for(let r of this.mutations)r.key.isEqual(e.key)&&(t=ti(r,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(e,t){let r=wm();return this.mutations.forEach(i=>{let s=e.get(i.key),a=s.overlayedDocument,c=this.applyToLocalView(a,s.mutatedFields);c=t.has(i.key)?null:c;let l=Cm(a,c);l!==null&&r.set(i.key,l),a.isValidDocument()||a.convertToNoDocument(j.min())}),r}keys(){return this.mutations.reduce((e,t)=>e.add(t.key),H())}isEqual(e){return this.batchId===e.batchId&&Kn(this.mutations,e.mutations,(t,r)=>pp(t,r))&&Kn(this.baseMutations,e.baseMutations,(t,r)=>pp(t,r))}},cu=class n{constructor(e,t,r,i){this.batch=e,this.commitVersion=t,this.mutationResults=r,this.docVersions=i}static from(e,t,r){X(e.mutations.length===r.length,58842,{fe:e.mutations.length,ge:r.length});let i=function(){return jT}(),s=e.mutations;for(let a=0;a<s.length;a++)i=i.insert(s[a].key,r[a].version);return new n(e,t,r,i)}};var uu=class{constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return e!==null&&this.mutation===e.mutation}toString(){return`Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`}};var lu=class{constructor(e,t){this.count=e,this.unchangedNames=t}};var le,K;function ZT(n){switch(n){case P.OK:return U(64938);case P.CANCELLED:case P.UNKNOWN:case P.DEADLINE_EXCEEDED:case P.RESOURCE_EXHAUSTED:case P.INTERNAL:case P.UNAVAILABLE:case P.UNAUTHENTICATED:return!1;case P.INVALID_ARGUMENT:case P.NOT_FOUND:case P.ALREADY_EXISTS:case P.PERMISSION_DENIED:case P.FAILED_PRECONDITION:case P.ABORTED:case P.OUT_OF_RANGE:case P.UNIMPLEMENTED:case P.DATA_LOSS:return!0;default:return U(15467,{code:n})}}function Dm(n){if(n===void 0)return bt("GRPC error has no .code"),P.UNKNOWN;switch(n){case le.OK:return P.OK;case le.CANCELLED:return P.CANCELLED;case le.UNKNOWN:return P.UNKNOWN;case le.DEADLINE_EXCEEDED:return P.DEADLINE_EXCEEDED;case le.RESOURCE_EXHAUSTED:return P.RESOURCE_EXHAUSTED;case le.INTERNAL:return P.INTERNAL;case le.UNAVAILABLE:return P.UNAVAILABLE;case le.UNAUTHENTICATED:return P.UNAUTHENTICATED;case le.INVALID_ARGUMENT:return P.INVALID_ARGUMENT;case le.NOT_FOUND:return P.NOT_FOUND;case le.ALREADY_EXISTS:return P.ALREADY_EXISTS;case le.PERMISSION_DENIED:return P.PERMISSION_DENIED;case le.FAILED_PRECONDITION:return P.FAILED_PRECONDITION;case le.ABORTED:return P.ABORTED;case le.OUT_OF_RANGE:return P.OUT_OF_RANGE;case le.UNIMPLEMENTED:return P.UNIMPLEMENTED;case le.DATA_LOSS:return P.DATA_LOSS;default:return U(39323,{code:n})}}(K=le||(le={}))[K.OK=0]="OK",K[K.CANCELLED=1]="CANCELLED",K[K.UNKNOWN=2]="UNKNOWN",K[K.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",K[K.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",K[K.NOT_FOUND=5]="NOT_FOUND",K[K.ALREADY_EXISTS=6]="ALREADY_EXISTS",K[K.PERMISSION_DENIED=7]="PERMISSION_DENIED",K[K.UNAUTHENTICATED=16]="UNAUTHENTICATED",K[K.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",K[K.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",K[K.ABORTED=10]="ABORTED",K[K.OUT_OF_RANGE=11]="OUT_OF_RANGE",K[K.UNIMPLEMENTED=12]="UNIMPLEMENTED",K[K.INTERNAL=13]="INTERNAL",K[K.UNAVAILABLE=14]="UNAVAILABLE",K[K.DATA_LOSS=15]="DATA_LOSS";var _p=null;var ew=new wt([4294967295,4294967295],0);function yp(n){let e=jp().encode(n),t=new bc;return t.update(e),new Uint8Array(t.digest())}function Ip(n){let e=new DataView(n.buffer),t=e.getUint32(0,!0),r=e.getUint32(4,!0),i=e.getUint32(8,!0),s=e.getUint32(12,!0);return[new wt([t,r],0),new wt([i,s],0)]}var hu=class n{constructor(e,t,r){if(this.bitmap=e,this.padding=t,this.hashCount=r,t<0||t>=8)throw new mn(`Invalid padding: ${t}`);if(r<0)throw new mn(`Invalid hash count: ${r}`);if(e.length>0&&this.hashCount===0)throw new mn(`Invalid hash count: ${r}`);if(e.length===0&&t!==0)throw new mn(`Invalid padding when bitmap length is 0: ${t}`);this.pe=8*e.length-t,this.ye=wt.fromNumber(this.pe)}we(e,t,r){let i=e.add(t.multiply(wt.fromNumber(r)));return i.compare(ew)===1&&(i=new wt([i.getBits(0),i.getBits(1)],0)),i.modulo(this.ye).toNumber()}Se(e){return!!(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(this.pe===0)return!1;let t=yp(e),[r,i]=Ip(t);for(let s=0;s<this.hashCount;s++){let a=this.we(r,i,s);if(!this.Se(a))return!1}return!0}static create(e,t,r){let i=e%8==0?0:8-e%8,s=new Uint8Array(Math.ceil(e/8)),a=new n(s,i,t);return r.forEach(c=>a.insert(c)),a}insert(e){if(this.pe===0)return;let t=yp(e),[r,i]=Ip(t);for(let s=0;s<this.hashCount;s++){let a=this.we(r,i,s);this.be(a)}}be(e){let t=Math.floor(e/8),r=e%8;this.bitmap[t]|=1<<r}},mn=class extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}};var qs=class n{constructor(e,t,r,i,s){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=r,this.documentUpdates=i,this.resolvedLimboDocuments=s}static createSynthesizedRemoteEventForCurrentChange(e,t,r){let i=new Map;return i.set(e,ai.createSynthesizedTargetChangeForCurrentChange(e,t,r)),new n(j.min(),i,new oe(W),Ct(),H())}},ai=class n{constructor(e,t,r,i,s){this.resumeToken=e,this.current=t,this.addedDocuments=r,this.modifiedDocuments=i,this.removedDocuments=s}static createSynthesizedTargetChangeForCurrentChange(e,t,r){return new n(r,t,H(),H(),H())}};var Wn=class{constructor(e,t,r,i){this.De=e,this.removedTargetIds=t,this.key=r,this.ve=i}},$s=class{constructor(e,t){this.targetId=e,this.Ce=t}},js=class{constructor(e,t,r=Se.EMPTY_BYTE_STRING,i=null){this.state=e,this.targetIds=t,this.resumeToken=r,this.cause=i}},zs=class{constructor(){this.Fe=0,this.Me=vp(),this.xe=Se.EMPTY_BYTE_STRING,this.Oe=!1,this.Ne=!0}get current(){return this.Oe}get resumeToken(){return this.xe}get Be(){return this.Fe!==0}get Le(){return this.Ne}ke(e){e.approximateByteSize()>0&&(this.Ne=!0,this.xe=e)}qe(){let e=H(),t=H(),r=H();return this.Me.forEach((i,s)=>{switch(s){case 0:e=e.add(i);break;case 2:t=t.add(i);break;case 1:r=r.add(i);break;default:U(38017,{changeType:s})}}),new ai(this.xe,this.Oe,e,t,r)}Qe(){this.Ne=!1,this.Me=vp()}$e(e,t){this.Ne=!0,this.Me=this.Me.insert(e,t)}Ue(e){this.Ne=!0,this.Me=this.Me.remove(e)}Ke(){this.Fe+=1}We(){this.Fe-=1,X(this.Fe>=0,3241,{Fe:this.Fe})}Ge(){this.Ne=!0,this.Oe=!0}},du=class{constructor(e){this.ze=e,this.je=new Map,this.He=Ct(),this.Je=Rs(),this.Ye=Rs(),this.Ze=new oe(W)}Xe(e){for(let t of e.De)e.ve&&e.ve.isFoundDocument()?this.et(t,e.ve):this.tt(t,e.key,e.ve);for(let t of e.removedTargetIds)this.tt(t,e.key,e.ve)}nt(e){this.forEachTarget(e,t=>{let r=this.rt(t);switch(e.state){case 0:this.it(t)&&r.ke(e.resumeToken);break;case 1:r.We(),r.Be||r.Qe(),r.ke(e.resumeToken);break;case 2:r.We(),r.Be||this.removeTarget(t);break;case 3:this.it(t)&&(r.Ge(),r.ke(e.resumeToken));break;case 4:this.it(t)&&(this.st(t),r.ke(e.resumeToken));break;default:U(56790,{state:e.state})}})}forEachTarget(e,t){e.targetIds.length>0?e.targetIds.forEach(t):this.je.forEach((r,i)=>{this.it(i)&&t(i)})}ot(e){let t=e.targetId,r=e.Ce.count,i=this._t(t);if(i){let s=i.target;if(nu(s))if(r===0){let a=new M(s.path);this.tt(t,a,Ge.newNoDocument(a,j.min()))}else X(r===1,20013,{expectedCount:r});else{let a=this.ut(t);if(a!==r){let c=this.ct(e),l=c?this.lt(c,e,a):1;if(l!==0){this.st(t);let d=l===2?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch";this.Ze=this.Ze.insert(t,d)}_p==null||_p.ht(function(p,_,I,R,k){var O,D,q,L,F,z;let ue={localCacheCount:p,existenceFilterCount:_.count,databaseId:I.database,projectId:I.projectId},Q=_.unchangedNames;return Q&&(ue.bloomFilter={applied:k===0,hashCount:(O=Q==null?void 0:Q.hashCount)!==null&&O!==void 0?O:0,bitmapLength:(L=(q=(D=Q==null?void 0:Q.bits)===null||D===void 0?void 0:D.bitmap)===null||q===void 0?void 0:q.length)!==null&&L!==void 0?L:0,padding:(z=(F=Q==null?void 0:Q.bits)===null||F===void 0?void 0:F.padding)!==null&&z!==void 0?z:0,mightContain:T=>{var m;return(m=R==null?void 0:R.mightContain(T))!==null&&m!==void 0&&m}}),ue}(a,e.Ce,this.ze.Pt(),c,l))}}}}ct(e){let t=e.Ce.unchangedNames;if(!t||!t.bits)return null;let{bits:{bitmap:r="",padding:i=0},hashCount:s=0}=t,a,c;try{a=St(r).toUint8Array()}catch(l){if(l instanceof Ls)return Hn("Decoding the base64 bloom filter in existence filter failed ("+l.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw l}try{c=new hu(a,i,s)}catch(l){return Hn(l instanceof mn?"BloomFilter error: ":"Applying bloom filter failed: ",l),null}return c.pe===0?null:c}lt(e,t,r){return t.Ce.count===r-this.Tt(e,t.targetId)?0:2}Tt(e,t){let r=this.ze.getRemoteKeysForTarget(t),i=0;return r.forEach(s=>{let a=this.ze.Pt(),c=`projects/${a.projectId}/databases/${a.database}/documents/${s.path.canonicalString()}`;e.mightContain(c)||(this.tt(t,s,null),i++)}),i}It(e){let t=new Map;this.je.forEach((s,a)=>{let c=this._t(a);if(c){if(s.current&&nu(c.target)){let l=new M(c.target.path);this.Et(l).has(a)||this.dt(a,l)||this.tt(a,l,Ge.newNoDocument(l,e))}s.Le&&(t.set(a,s.qe()),s.Qe())}});let r=H();this.Ye.forEach((s,a)=>{let c=!0;a.forEachWhile(l=>{let d=this._t(l);return!d||d.purpose==="TargetPurposeLimboResolution"||(c=!1,!1)}),c&&(r=r.add(s))}),this.He.forEach((s,a)=>a.setReadTime(e));let i=new qs(e,t,this.Ze,this.He,r);return this.He=Ct(),this.Je=Rs(),this.Ye=Rs(),this.Ze=new oe(W),i}et(e,t){if(!this.it(e))return;let r=this.dt(e,t.key)?2:0;this.rt(e).$e(t.key,r),this.He=this.He.insert(t.key,t),this.Je=this.Je.insert(t.key,this.Et(t.key).add(e)),this.Ye=this.Ye.insert(t.key,this.At(t.key).add(e))}tt(e,t,r){if(!this.it(e))return;let i=this.rt(e);this.dt(e,t)?i.$e(t,1):i.Ue(t),this.Ye=this.Ye.insert(t,this.At(t).delete(e)),this.Ye=this.Ye.insert(t,this.At(t).add(e)),r&&(this.He=this.He.insert(t,r))}removeTarget(e){this.je.delete(e)}ut(e){let t=this.rt(e).qe();return this.ze.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}Ke(e){this.rt(e).Ke()}rt(e){let t=this.je.get(e);return t||(t=new zs,this.je.set(e,t)),t}At(e){let t=this.Ye.get(e);return t||(t=new me(W),this.Ye=this.Ye.insert(e,t)),t}Et(e){let t=this.Je.get(e);return t||(t=new me(W),this.Je=this.Je.insert(e,t)),t}it(e){let t=this._t(e)!==null;return t||V("WatchChangeAggregator","Detected inactive target",e),t}_t(e){let t=this.je.get(e);return t&&t.Be?null:this.ze.Rt(e)}st(e){this.je.set(e,new zs),this.ze.getRemoteKeysForTarget(e).forEach(t=>{this.tt(e,t,null)})}dt(e,t){return this.ze.getRemoteKeysForTarget(e).has(t)}};function Rs(){return new oe(M.comparator)}function vp(){return new oe(M.comparator)}var tw={asc:"ASCENDING",desc:"DESCENDING"},nw={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},rw={and:"AND",or:"OR"},fu=class{constructor(e,t){this.databaseId=e,this.useProto3Json=t}};function pu(n,e){return n.useProto3Json||go(e)?e:{value:e}}function Ws(n,e){return n.useProto3Json?`${new Date(1e3*e.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+e.nanoseconds).slice(-9)}Z`:{seconds:""+e.seconds,nanos:e.nanoseconds}}function Nm(n,e){return n.useProto3Json?e.toBase64():e.toUint8Array()}function iw(n,e){return Ws(n,e.toTimestamp())}function ct(n){return X(!!n,49232),j.fromTimestamp(function(t){let r=Rt(t);return new ve(r.seconds,r.nanos)}(n))}function Nl(n,e){return mu(n,e).canonicalString()}function mu(n,e){let t=function(i){return new ie(["projects",i.projectId,"databases",i.database])}(n).child("documents");return e===void 0?t:t.child(e)}function Om(n){let e=ie.fromString(n);return X(Fm(e),10190,{key:e.toString()}),e}function gu(n,e){return Nl(n.databaseId,e.path)}function Dc(n,e){let t=Om(e);if(t.get(1)!==n.databaseId.projectId)throw new x(P.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+t.get(1)+" vs "+n.databaseId.projectId);if(t.get(3)!==n.databaseId.database)throw new x(P.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+t.get(3)+" vs "+n.databaseId.database);return new M(Vm(t))}function xm(n,e){return Nl(n.databaseId,e)}function sw(n){let e=Om(n);return e.length===4?ie.emptyPath():Vm(e)}function _u(n){return new ie(["projects",n.databaseId.projectId,"databases",n.databaseId.database]).canonicalString()}function Vm(n){return X(n.length>4&&n.get(4)==="documents",29091,{key:n.toString()}),n.popFirst(5)}function Ep(n,e,t){return{name:gu(n,e),fields:t.value.mapValue.fields}}function ow(n,e){let t;if("targetChange"in e){e.targetChange;let r=function(d){return d==="NO_CHANGE"?0:d==="ADD"?1:d==="REMOVE"?2:d==="CURRENT"?3:d==="RESET"?4:U(39313,{state:d})}(e.targetChange.targetChangeType||"NO_CHANGE"),i=e.targetChange.targetIds||[],s=function(d,p){return d.useProto3Json?(X(p===void 0||typeof p=="string",58123),Se.fromBase64String(p||"")):(X(p===void 0||p instanceof Buffer||p instanceof Uint8Array,16193),Se.fromUint8Array(p||new Uint8Array))}(n,e.targetChange.resumeToken),a=e.targetChange.cause,c=a&&function(d){let p=d.code===void 0?P.UNKNOWN:Dm(d.code);return new x(p,d.message||"")}(a);t=new js(r,i,s,c||null)}else if("documentChange"in e){e.documentChange;let r=e.documentChange;r.document,r.document.name,r.document.updateTime;let i=Dc(n,r.document.name),s=ct(r.document.updateTime),a=r.document.createTime?ct(r.document.createTime):j.min(),c=new xe({mapValue:{fields:r.document.fields}}),l=Ge.newFoundDocument(i,s,a,c),d=r.targetIds||[],p=r.removedTargetIds||[];t=new Wn(d,p,l.key,l)}else if("documentDelete"in e){e.documentDelete;let r=e.documentDelete;r.document;let i=Dc(n,r.document),s=r.readTime?ct(r.readTime):j.min(),a=Ge.newNoDocument(i,s),c=r.removedTargetIds||[];t=new Wn([],c,a.key,a)}else if("documentRemove"in e){e.documentRemove;let r=e.documentRemove;r.document;let i=Dc(n,r.document),s=r.removedTargetIds||[];t=new Wn([],s,i,null)}else{if(!("filter"in e))return U(11601,{Vt:e});{e.filter;let r=e.filter;r.targetId;let{count:i=0,unchangedNames:s}=r,a=new lu(i,s),c=r.targetId;t=new $s(c,a)}}return t}function aw(n,e){let t;if(e instanceof wn)t={update:Ep(n,e.key,e.value)};else if(e instanceof oi)t={delete:gu(n,e.key)};else if(e instanceof lt)t={update:Ep(n,e.key,e.data),updateMask:gw(e.fieldMask)};else{if(!(e instanceof ou))return U(16599,{ft:e.type});t={verify:gu(n,e.key)}}return e.fieldTransforms.length>0&&(t.updateTransforms=e.fieldTransforms.map(r=>function(s,a){let c=a.transform;if(c instanceof vn)return{fieldPath:a.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(c instanceof En)return{fieldPath:a.field.canonicalString(),appendMissingElements:{values:c.elements}};if(c instanceof Tn)return{fieldPath:a.field.canonicalString(),removeAllFromArray:{values:c.elements}};if(c instanceof er)return{fieldPath:a.field.canonicalString(),increment:c.Re};throw U(20930,{transform:a.transform})}(0,r))),e.precondition.isNone||(t.currentDocument=function(i,s){return s.updateTime!==void 0?{updateTime:iw(i,s.updateTime)}:s.exists!==void 0?{exists:s.exists}:U(27497)}(n,e.precondition)),t}function cw(n,e){return n&&n.length>0?(X(e!==void 0,14353),n.map(t=>function(i,s){let a=i.updateTime?ct(i.updateTime):ct(s);return a.isEqual(j.min())&&(a=ct(s)),new su(a,i.transformResults||[])}(t,e))):[]}function uw(n,e){return{documents:[xm(n,e.path)]}}function lw(n,e){let t={structuredQuery:{}},r=e.path,i;e.collectionGroup!==null?(i=r,t.structuredQuery.from=[{collectionId:e.collectionGroup,allDescendants:!0}]):(i=r.popLast(),t.structuredQuery.from=[{collectionId:r.lastSegment()}]),t.parent=xm(n,i);let s=function(d){if(d.length!==0)return Mm(He.create(d,"and"))}(e.filters);s&&(t.structuredQuery.where=s);let a=function(d){if(d.length!==0)return d.map(p=>function(I){return{field:$n(I.field),direction:fw(I.dir)}}(p))}(e.orderBy);a&&(t.structuredQuery.orderBy=a);let c=pu(n,e.limit);return c!==null&&(t.structuredQuery.limit=c),e.startAt&&(t.structuredQuery.startAt=function(d){return{before:d.inclusive,values:d.position}}(e.startAt)),e.endAt&&(t.structuredQuery.endAt=function(d){return{before:!d.inclusive,values:d.position}}(e.endAt)),{gt:t,parent:i}}function hw(n){let e=sw(n.parent),t=n.structuredQuery,r=t.from?t.from.length:0,i=null;if(r>0){X(r===1,65062);let p=t.from[0];p.allDescendants?i=p.collectionId:e=e.child(p.collectionId)}let s=[];t.where&&(s=function(_){let I=Lm(_);return I instanceof He&&pm(I)?I.getFilters():[I]}(t.where));let a=[];t.orderBy&&(a=function(_){return _.map(I=>function(k){return new In(jn(k.field),function(D){switch(D){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}}(k.direction))}(I))}(t.orderBy));let c=null;t.limit&&(c=function(_){let I;return I=typeof _=="object"?_.value:_,go(I)?null:I}(t.limit));let l=null;t.startAt&&(l=function(_){let I=!!_.before,R=_.values||[];return new Xn(R,I)}(t.startAt));let d=null;return t.endAt&&(d=function(_){let I=!_.before,R=_.values||[];return new Xn(R,I)}(t.endAt)),FT(e,i,a,s,c,"F",l,d)}function dw(n,e){let t=function(i){switch(i){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return U(28987,{purpose:i})}}(e.purpose);return t==null?null:{"goog-listen-tags":t}}function Lm(n){return n.unaryFilter!==void 0?function(t){switch(t.unaryFilter.op){case"IS_NAN":let r=jn(t.unaryFilter.field);return de.create(r,"==",{doubleValue:NaN});case"IS_NULL":let i=jn(t.unaryFilter.field);return de.create(i,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":let s=jn(t.unaryFilter.field);return de.create(s,"!=",{doubleValue:NaN});case"IS_NOT_NULL":let a=jn(t.unaryFilter.field);return de.create(a,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":return U(61313);default:return U(60726)}}(n):n.fieldFilter!==void 0?function(t){return de.create(jn(t.fieldFilter.field),function(i){switch(i){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":return U(58110);default:return U(50506)}}(t.fieldFilter.op),t.fieldFilter.value)}(n):n.compositeFilter!==void 0?function(t){return He.create(t.compositeFilter.filters.map(r=>Lm(r)),function(i){switch(i){case"AND":return"and";case"OR":return"or";default:return U(1026)}}(t.compositeFilter.op))}(n):U(30097,{filter:n})}function fw(n){return tw[n]}function pw(n){return nw[n]}function mw(n){return rw[n]}function $n(n){return{fieldPath:n.canonicalString()}}function jn(n){return Ve.fromServerFormat(n.fieldPath)}function Mm(n){return n instanceof de?function(t){if(t.op==="=="){if(cp(t.value))return{unaryFilter:{field:$n(t.field),op:"IS_NAN"}};if(ap(t.value))return{unaryFilter:{field:$n(t.field),op:"IS_NULL"}}}else if(t.op==="!="){if(cp(t.value))return{unaryFilter:{field:$n(t.field),op:"IS_NOT_NAN"}};if(ap(t.value))return{unaryFilter:{field:$n(t.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:$n(t.field),op:pw(t.op),value:t.value}}}(n):n instanceof He?function(t){let r=t.getFilters().map(i=>Mm(i));return r.length===1?r[0]:{compositeFilter:{op:mw(t.op),filters:r}}}(n):U(54877,{filter:n})}function gw(n){let e=[];return n.fields.forEach(t=>e.push(t.canonicalString())),{fieldPaths:e}}function Fm(n){return n.length>=4&&n.get(0)==="projects"&&n.get(2)==="databases"}var ci=class n{constructor(e,t,r,i,s=j.min(),a=j.min(),c=Se.EMPTY_BYTE_STRING,l=null){this.target=e,this.targetId=t,this.purpose=r,this.sequenceNumber=i,this.snapshotVersion=s,this.lastLimboFreeSnapshotVersion=a,this.resumeToken=c,this.expectedCount=l}withSequenceNumber(e){return new n(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new n(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new n(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new n(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}};var yu=class{constructor(e){this.wt=e}};function _w(n){let e=hw({parent:n.parent,structuredQuery:n.structuredQuery});return n.limitType==="LAST"?Bs(e,e.limit,"L"):e}var Gs=class{constructor(){}vt(e,t){this.Ct(e,t),t.Ft()}Ct(e,t){if("nullValue"in e)this.Mt(t,5);else if("booleanValue"in e)this.Mt(t,10),t.xt(e.booleanValue?1:0);else if("integerValue"in e)this.Mt(t,15),t.xt(re(e.integerValue));else if("doubleValue"in e){let r=re(e.doubleValue);isNaN(r)?this.Mt(t,13):(this.Mt(t,15),ri(r)?t.xt(0):t.xt(r))}else if("timestampValue"in e){let r=e.timestampValue;this.Mt(t,20),typeof r=="string"&&(r=Rt(r)),t.Ot(`${r.seconds||""}`),t.xt(r.nanos||0)}else if("stringValue"in e)this.Nt(e.stringValue,t),this.Bt(t);else if("bytesValue"in e)this.Mt(t,30),t.Lt(St(e.bytesValue)),this.Bt(t);else if("referenceValue"in e)this.kt(e.referenceValue,t);else if("geoPointValue"in e){let r=e.geoPointValue;this.Mt(t,45),t.xt(r.latitude||0),t.xt(r.longitude||0)}else"mapValue"in e?hm(e)?this.Mt(t,Number.MAX_SAFE_INTEGER):lm(e)?this.qt(e.mapValue,t):(this.Qt(e.mapValue,t),this.Bt(t)):"arrayValue"in e?(this.$t(e.arrayValue,t),this.Bt(t)):U(19022,{Ut:e})}Nt(e,t){this.Mt(t,25),this.Kt(e,t)}Kt(e,t){t.Ot(e)}Qt(e,t){let r=e.fields||{};this.Mt(t,55);for(let i of Object.keys(r))this.Nt(i,t),this.Ct(r[i],t)}qt(e,t){var r,i;let s=e.fields||{};this.Mt(t,53);let a=Qn,c=((i=(r=s[a].arrayValue)===null||r===void 0?void 0:r.values)===null||i===void 0?void 0:i.length)||0;this.Mt(t,15),t.xt(re(c)),this.Nt(a,t),this.Ct(s[a],t)}$t(e,t){let r=e.values||[];this.Mt(t,50);for(let i of r)this.Ct(i,t)}kt(e,t){this.Mt(t,37),M.fromName(e).path.forEach(r=>{this.Mt(t,60),this.Kt(r,t)})}Mt(e,t){e.xt(t)}Bt(e){e.xt(2)}};Gs.Wt=new Gs;var Iu=class{constructor(){this.Cn=new vu}addToCollectionParentIndex(e,t){return this.Cn.add(t),C.resolve()}getCollectionParents(e,t){return C.resolve(this.Cn.getEntries(t))}addFieldIndex(e,t){return C.resolve()}deleteFieldIndex(e,t){return C.resolve()}deleteAllFieldIndexes(e){return C.resolve()}createTargetIndexes(e,t){return C.resolve()}getDocumentsMatchingTarget(e,t){return C.resolve(null)}getIndexType(e,t){return C.resolve(0)}getFieldIndexes(e,t){return C.resolve([])}getNextCollectionGroupToUpdate(e){return C.resolve(null)}getMinOffset(e,t){return C.resolve(yn.min())}getMinOffsetFromCollectionGroup(e,t){return C.resolve(yn.min())}updateCollectionGroup(e,t,r){return C.resolve()}updateIndexEntries(e,t){return C.resolve()}},vu=class{constructor(){this.index={}}add(e){let t=e.lastSegment(),r=e.popLast(),i=this.index[t]||new me(ie.comparator),s=!i.has(r);return this.index[t]=i.add(r),s}has(e){let t=e.lastSegment(),r=e.popLast(),i=this.index[t];return i&&i.has(r)}getEntries(e){return(this.index[e]||new me(ie.comparator)).toArray()}};var PS=new Uint8Array(0);var Tp={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0},Um=41943040,Fe=class n{static withCacheSize(e){return new n(e,n.DEFAULT_COLLECTION_PERCENTILE,n.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(e,t,r){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=r}};Fe.DEFAULT_COLLECTION_PERCENTILE=10,Fe.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,Fe.DEFAULT=new Fe(Um,Fe.DEFAULT_COLLECTION_PERCENTILE,Fe.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),Fe.DISABLED=new Fe(-1,0,0);var ui=class n{constructor(e){this.ur=e}next(){return this.ur+=2,this.ur}static cr(){return new n(0)}static lr(){return new n(-1)}};var wp="LruGarbageCollector",yw=1048576;function Ap([n,e],[t,r]){let i=W(n,t);return i===0?W(e,r):i}var Eu=class{constructor(e){this.Er=e,this.buffer=new me(Ap),this.dr=0}Ar(){return++this.dr}Rr(e){let t=[e,this.Ar()];if(this.buffer.size<this.Er)this.buffer=this.buffer.add(t);else{let r=this.buffer.last();Ap(t,r)<0&&(this.buffer=this.buffer.delete(r).add(t))}}get maxValue(){return this.buffer.last()[0]}},Tu=class{constructor(e,t,r){this.garbageCollector=e,this.asyncQueue=t,this.localStore=r,this.Vr=null}start(){this.garbageCollector.params.cacheSizeCollectionThreshold!==-1&&this.mr(6e4)}stop(){this.Vr&&(this.Vr.cancel(),this.Vr=null)}get started(){return this.Vr!==null}mr(e){V(wp,`Garbage collection scheduled in ${e}ms`),this.Vr=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,()=>g(this,null,function*(){this.Vr=null;try{yield this.localStore.collectGarbage(this.garbageCollector)}catch(t){cr(t)?V(wp,"Ignoring IndexedDB error during garbage collection: ",t):yield ar(t)}yield this.mr(3e5)}))}},wu=class{constructor(e,t){this.gr=e,this.params=t}calculateTargetCount(e,t){return this.gr.pr(e).next(r=>Math.floor(t/100*r))}nthSequenceNumber(e,t){if(t===0)return C.resolve(Tl.le);let r=new Eu(t);return this.gr.forEachTarget(e,i=>r.Rr(i.sequenceNumber)).next(()=>this.gr.yr(e,i=>r.Rr(i))).next(()=>r.maxValue)}removeTargets(e,t,r){return this.gr.removeTargets(e,t,r)}removeOrphanedDocuments(e,t){return this.gr.removeOrphanedDocuments(e,t)}collect(e,t){return this.params.cacheSizeCollectionThreshold===-1?(V("LruGarbageCollector","Garbage collection skipped; disabled"),C.resolve(Tp)):this.getCacheSize(e).next(r=>r<this.params.cacheSizeCollectionThreshold?(V("LruGarbageCollector",`Garbage collection skipped; Cache size ${r} is lower than threshold ${this.params.cacheSizeCollectionThreshold}`),Tp):this.wr(e,t))}getCacheSize(e){return this.gr.getCacheSize(e)}wr(e,t){let r,i,s,a,c,l,d,p=Date.now();return this.calculateTargetCount(e,this.params.percentileToCollect).next(_=>(_>this.params.maximumSequenceNumbersToCollect?(V("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from ${_}`),i=this.params.maximumSequenceNumbersToCollect):i=_,a=Date.now(),this.nthSequenceNumber(e,i))).next(_=>(r=_,c=Date.now(),this.removeTargets(e,r,t))).next(_=>(s=_,l=Date.now(),this.removeOrphanedDocuments(e,r))).next(_=>(d=Date.now(),Bn()<=G.DEBUG&&V("LruGarbageCollector",`LRU Garbage Collection
	Counted targets in ${a-p}ms
	Determined least recently used ${i} in `+(c-a)+`ms
	Removed ${s} targets in `+(l-c)+`ms
	Removed ${_} documents in `+(d-l)+`ms
Total Duration: ${d-p}ms`),C.resolve({didRun:!0,sequenceNumbersCollected:i,targetsRemoved:s,documentsRemoved:_})))}};function Iw(n,e){return new wu(n,e)}var Au=class{constructor(){this.changes=new Pt(e=>e.toString(),(e,t)=>e.isEqual(t)),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,Ge.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();let r=this.changes.get(t);return r!==void 0?C.resolve(r):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}};var bu=class{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}};var Ru=class{constructor(e,t,r,i){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=r,this.indexManager=i}getDocument(e,t){let r=null;return this.documentOverlayCache.getOverlay(e,t).next(i=>(r=i,this.remoteDocumentCache.getEntry(e,t))).next(i=>(r!==null&&ti(r.mutation,i,Ue.empty(),ve.now()),i))}getDocuments(e,t){return this.remoteDocumentCache.getEntries(e,t).next(r=>this.getLocalViewOfDocuments(e,r,H()).next(()=>r))}getLocalViewOfDocuments(e,t,r=H()){let i=pn();return this.populateOverlays(e,i,t).next(()=>this.computeViews(e,t,i,r).next(s=>{let a=Jr();return s.forEach((c,l)=>{a=a.insert(c,l.overlayedDocument)}),a}))}getOverlayedDocuments(e,t){let r=pn();return this.populateOverlays(e,r,t).next(()=>this.computeViews(e,t,r,H()))}populateOverlays(e,t,r){let i=[];return r.forEach(s=>{t.has(s)||i.push(s)}),this.documentOverlayCache.getOverlays(e,i).next(s=>{s.forEach((a,c)=>{t.set(a,c)})})}computeViews(e,t,r,i){let s=Ct(),a=ei(),c=function(){return ei()}();return t.forEach((l,d)=>{let p=r.get(d.key);i.has(d.key)&&(p===void 0||p.mutation instanceof lt)?s=s.insert(d.key,d):p!==void 0?(a.set(d.key,p.mutation.getFieldMask()),ti(p.mutation,d,p.mutation.getFieldMask(),ve.now())):a.set(d.key,Ue.empty())}),this.recalculateAndSaveOverlays(e,s).next(l=>(l.forEach((d,p)=>a.set(d,p)),t.forEach((d,p)=>{var _;return c.set(d,new bu(p,(_=a.get(d))!==null&&_!==void 0?_:null))}),c))}recalculateAndSaveOverlays(e,t){let r=ei(),i=new oe((a,c)=>a-c),s=H();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(e,t).next(a=>{for(let c of a)c.keys().forEach(l=>{let d=t.get(l);if(d===null)return;let p=r.get(l)||Ue.empty();p=c.applyToLocalView(d,p),r.set(l,p);let _=(i.get(c.batchId)||H()).add(l);i=i.insert(c.batchId,_)})}).next(()=>{let a=[],c=i.getReverseIterator();for(;c.hasNext();){let l=c.getNext(),d=l.key,p=l.value,_=wm();p.forEach(I=>{if(!s.has(I)){let R=Cm(t.get(I),r.get(I));R!==null&&_.set(I,R),s=s.add(I)}}),a.push(this.documentOverlayCache.saveOverlays(e,d,_))}return C.waitFor(a)}).next(()=>r)}recalculateAndSaveOverlaysForDocumentKeys(e,t){return this.remoteDocumentCache.getEntries(e,t).next(r=>this.recalculateAndSaveOverlays(e,r))}getDocumentsMatchingQuery(e,t,r,i){return function(a){return M.isDocumentKey(a.path)&&a.collectionGroup===null&&a.filters.length===0}(t)?this.getDocumentsMatchingDocumentQuery(e,t.path):ym(t)?this.getDocumentsMatchingCollectionGroupQuery(e,t,r,i):this.getDocumentsMatchingCollectionQuery(e,t,r,i)}getNextDocuments(e,t,r,i){return this.remoteDocumentCache.getAllFromCollectionGroup(e,t,r,i).next(s=>{let a=i-s.size>0?this.documentOverlayCache.getOverlaysForCollectionGroup(e,t,r.largestBatchId,i-s.size):C.resolve(pn()),c=ni,l=s;return a.next(d=>C.forEach(d,(p,_)=>(c<_.largestBatchId&&(c=_.largestBatchId),s.get(p)?C.resolve():this.remoteDocumentCache.getEntry(e,p).next(I=>{l=l.insert(p,I)}))).next(()=>this.populateOverlays(e,d,s)).next(()=>this.computeViews(e,l,d,H())).next(p=>({batchId:c,changes:Tm(p)})))})}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new M(t)).next(r=>{let i=Jr();return r.isFoundDocument()&&(i=i.insert(r.key,r)),i})}getDocumentsMatchingCollectionGroupQuery(e,t,r,i){let s=t.collectionGroup,a=Jr();return this.indexManager.getCollectionParents(e,s).next(c=>C.forEach(c,l=>{let d=function(_,I){return new Jt(I,null,_.explicitOrderBy.slice(),_.filters.slice(),_.limit,_.limitType,_.startAt,_.endAt)}(t,l.child(s));return this.getDocumentsMatchingCollectionQuery(e,d,r,i).next(p=>{p.forEach((_,I)=>{a=a.insert(_,I)})})}).next(()=>a))}getDocumentsMatchingCollectionQuery(e,t,r,i){let s;return this.documentOverlayCache.getOverlaysForCollection(e,t.path,r.largestBatchId).next(a=>(s=a,this.remoteDocumentCache.getDocumentsMatchingQuery(e,t,r,s,i))).next(a=>{s.forEach((l,d)=>{let p=d.getKey();a.get(p)===null&&(a=a.insert(p,Ge.newInvalidDocument(p)))});let c=Jr();return a.forEach((l,d)=>{let p=s.get(l);p!==void 0&&ti(p.mutation,d,Ue.empty(),ve.now()),Io(t,d)&&(c=c.insert(l,d))}),c})}};var Su=class{constructor(e){this.serializer=e,this.kr=new Map,this.qr=new Map}getBundleMetadata(e,t){return C.resolve(this.kr.get(t))}saveBundleMetadata(e,t){return this.kr.set(t.id,function(i){return{id:i.id,version:i.version,createTime:ct(i.createTime)}}(t)),C.resolve()}getNamedQuery(e,t){return C.resolve(this.qr.get(t))}saveNamedQuery(e,t){return this.qr.set(t.name,function(i){return{name:i.name,query:_w(i.bundledQuery),readTime:ct(i.readTime)}}(t)),C.resolve()}};var Pu=class{constructor(){this.overlays=new oe(M.comparator),this.Qr=new Map}getOverlay(e,t){return C.resolve(this.overlays.get(t))}getOverlays(e,t){let r=pn();return C.forEach(t,i=>this.getOverlay(e,i).next(s=>{s!==null&&r.set(i,s)})).next(()=>r)}saveOverlays(e,t,r){return r.forEach((i,s)=>{this.bt(e,t,s)}),C.resolve()}removeOverlaysForBatchId(e,t,r){let i=this.Qr.get(r);return i!==void 0&&(i.forEach(s=>this.overlays=this.overlays.remove(s)),this.Qr.delete(r)),C.resolve()}getOverlaysForCollection(e,t,r){let i=pn(),s=t.length+1,a=new M(t.child("")),c=this.overlays.getIteratorFrom(a);for(;c.hasNext();){let l=c.getNext().value,d=l.getKey();if(!t.isPrefixOf(d.path))break;d.path.length===s&&l.largestBatchId>r&&i.set(l.getKey(),l)}return C.resolve(i)}getOverlaysForCollectionGroup(e,t,r,i){let s=new oe((d,p)=>d-p),a=this.overlays.getIterator();for(;a.hasNext();){let d=a.getNext().value;if(d.getKey().getCollectionGroup()===t&&d.largestBatchId>r){let p=s.get(d.largestBatchId);p===null&&(p=pn(),s=s.insert(d.largestBatchId,p)),p.set(d.getKey(),d)}}let c=pn(),l=s.getIterator();for(;l.hasNext()&&(l.getNext().value.forEach((d,p)=>c.set(d,p)),!(c.size()>=i)););return C.resolve(c)}bt(e,t,r){let i=this.overlays.get(r.key);if(i!==null){let a=this.Qr.get(i.largestBatchId).delete(r.key);this.Qr.set(i.largestBatchId,a)}this.overlays=this.overlays.insert(r.key,new uu(t,r));let s=this.Qr.get(t);s===void 0&&(s=H(),this.Qr.set(t,s)),this.Qr.set(t,s.add(r.key))}};var Cu=class{constructor(){this.sessionToken=Se.EMPTY_BYTE_STRING}getSessionToken(e){return C.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,C.resolve()}};var li=class{constructor(){this.$r=new me(he.Ur),this.Kr=new me(he.Wr)}isEmpty(){return this.$r.isEmpty()}addReference(e,t){let r=new he(e,t);this.$r=this.$r.add(r),this.Kr=this.Kr.add(r)}Gr(e,t){e.forEach(r=>this.addReference(r,t))}removeReference(e,t){this.zr(new he(e,t))}jr(e,t){e.forEach(r=>this.removeReference(r,t))}Hr(e){let t=new M(new ie([])),r=new he(t,e),i=new he(t,e+1),s=[];return this.Kr.forEachInRange([r,i],a=>{this.zr(a),s.push(a.key)}),s}Jr(){this.$r.forEach(e=>this.zr(e))}zr(e){this.$r=this.$r.delete(e),this.Kr=this.Kr.delete(e)}Yr(e){let t=new M(new ie([])),r=new he(t,e),i=new he(t,e+1),s=H();return this.Kr.forEachInRange([r,i],a=>{s=s.add(a.key)}),s}containsKey(e){let t=new he(e,0),r=this.$r.firstAfterOrEqual(t);return r!==null&&e.isEqual(r.key)}},he=class{constructor(e,t){this.key=e,this.Zr=t}static Ur(e,t){return M.comparator(e.key,t.key)||W(e.Zr,t.Zr)}static Wr(e,t){return W(e.Zr,t.Zr)||M.comparator(e.key,t.key)}};var ku=class{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.nr=1,this.Xr=new me(he.Ur)}checkEmpty(e){return C.resolve(this.mutationQueue.length===0)}addMutationBatch(e,t,r,i){let s=this.nr;this.nr++,this.mutationQueue.length>0&&this.mutationQueue[this.mutationQueue.length-1];let a=new au(s,t,r,i);this.mutationQueue.push(a);for(let c of i)this.Xr=this.Xr.add(new he(c.key,s)),this.indexManager.addToCollectionParentIndex(e,c.key.path.popLast());return C.resolve(a)}lookupMutationBatch(e,t){return C.resolve(this.ei(t))}getNextMutationBatchAfterBatchId(e,t){let r=t+1,i=this.ti(r),s=i<0?0:i;return C.resolve(this.mutationQueue.length>s?this.mutationQueue[s]:null)}getHighestUnacknowledgedBatchId(){return C.resolve(this.mutationQueue.length===0?wl:this.nr-1)}getAllMutationBatches(e){return C.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){let r=new he(t,0),i=new he(t,Number.POSITIVE_INFINITY),s=[];return this.Xr.forEachInRange([r,i],a=>{let c=this.ei(a.Zr);s.push(c)}),C.resolve(s)}getAllMutationBatchesAffectingDocumentKeys(e,t){let r=new me(W);return t.forEach(i=>{let s=new he(i,0),a=new he(i,Number.POSITIVE_INFINITY);this.Xr.forEachInRange([s,a],c=>{r=r.add(c.Zr)})}),C.resolve(this.ni(r))}getAllMutationBatchesAffectingQuery(e,t){let r=t.path,i=r.length+1,s=r;M.isDocumentKey(s)||(s=s.child(""));let a=new he(new M(s),0),c=new me(W);return this.Xr.forEachWhile(l=>{let d=l.key.path;return!!r.isPrefixOf(d)&&(d.length===i&&(c=c.add(l.Zr)),!0)},a),C.resolve(this.ni(c))}ni(e){let t=[];return e.forEach(r=>{let i=this.ei(r);i!==null&&t.push(i)}),t}removeMutationBatch(e,t){X(this.ri(t.batchId,"removed")===0,55003),this.mutationQueue.shift();let r=this.Xr;return C.forEach(t.mutations,i=>{let s=new he(i.key,t.batchId);return r=r.delete(s),this.referenceDelegate.markPotentiallyOrphaned(e,i.key)}).next(()=>{this.Xr=r})}sr(e){}containsKey(e,t){let r=new he(t,0),i=this.Xr.firstAfterOrEqual(r);return C.resolve(t.isEqual(i&&i.key))}performConsistencyCheck(e){return this.mutationQueue.length,C.resolve()}ri(e,t){return this.ti(e)}ti(e){return this.mutationQueue.length===0?0:e-this.mutationQueue[0].batchId}ei(e){let t=this.ti(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}};var Du=class{constructor(e){this.ii=e,this.docs=function(){return new oe(M.comparator)}(),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){let r=t.key,i=this.docs.get(r),s=i?i.size:0,a=this.ii(t);return this.docs=this.docs.insert(r,{document:t.mutableCopy(),size:a}),this.size+=a-s,this.indexManager.addToCollectionParentIndex(e,r.path.popLast())}removeEntry(e){let t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){let r=this.docs.get(t);return C.resolve(r?r.document.mutableCopy():Ge.newInvalidDocument(t))}getEntries(e,t){let r=Ct();return t.forEach(i=>{let s=this.docs.get(i);r=r.insert(i,s?s.document.mutableCopy():Ge.newInvalidDocument(i))}),C.resolve(r)}getDocumentsMatchingQuery(e,t,r,i){let s=Ct(),a=t.path,c=new M(a.child("__id-9223372036854775808__")),l=this.docs.getIteratorFrom(c);for(;l.hasNext();){let{key:d,value:{document:p}}=l.getNext();if(!a.isPrefixOf(d.path))break;d.path.length>a.length+1||IT(yT(p),r)<=0||(i.has(p.key)||Io(t,p))&&(s=s.insert(p.key,p.mutableCopy()))}return C.resolve(s)}getAllFromCollectionGroup(e,t,r,i){U(9500)}si(e,t){return C.forEach(this.docs,r=>t(r))}newChangeBuffer(e){return new Nu(this)}getSize(e){return C.resolve(this.size)}},Nu=class extends Au{constructor(e){super(),this.Br=e}applyChanges(e){let t=[];return this.changes.forEach((r,i)=>{i.isValidDocument()?t.push(this.Br.addEntry(e,i)):this.Br.removeEntry(r)}),C.waitFor(t)}getFromCache(e,t){return this.Br.getEntry(e,t)}getAllFromCache(e,t){return this.Br.getEntries(e,t)}};var Ou=class{constructor(e){this.persistence=e,this.oi=new Pt(t=>Pl(t),Cl),this.lastRemoteSnapshotVersion=j.min(),this.highestTargetId=0,this._i=0,this.ai=new li,this.targetCount=0,this.ui=ui.cr()}forEachTarget(e,t){return this.oi.forEach((r,i)=>t(i)),C.resolve()}getLastRemoteSnapshotVersion(e){return C.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return C.resolve(this._i)}allocateTargetId(e){return this.highestTargetId=this.ui.next(),C.resolve(this.highestTargetId)}setTargetsMetadata(e,t,r){return r&&(this.lastRemoteSnapshotVersion=r),t>this._i&&(this._i=t),C.resolve()}Tr(e){this.oi.set(e.target,e);let t=e.targetId;t>this.highestTargetId&&(this.ui=new ui(t),this.highestTargetId=t),e.sequenceNumber>this._i&&(this._i=e.sequenceNumber)}addTargetData(e,t){return this.Tr(t),this.targetCount+=1,C.resolve()}updateTargetData(e,t){return this.Tr(t),C.resolve()}removeTargetData(e,t){return this.oi.delete(t.target),this.ai.Hr(t.targetId),this.targetCount-=1,C.resolve()}removeTargets(e,t,r){let i=0,s=[];return this.oi.forEach((a,c)=>{c.sequenceNumber<=t&&r.get(c.targetId)===null&&(this.oi.delete(a),s.push(this.removeMatchingKeysForTargetId(e,c.targetId)),i++)}),C.waitFor(s).next(()=>i)}getTargetCount(e){return C.resolve(this.targetCount)}getTargetData(e,t){let r=this.oi.get(t)||null;return C.resolve(r)}addMatchingKeys(e,t,r){return this.ai.Gr(t,r),C.resolve()}removeMatchingKeys(e,t,r){this.ai.jr(t,r);let i=this.persistence.referenceDelegate,s=[];return i&&t.forEach(a=>{s.push(i.markPotentiallyOrphaned(e,a))}),C.waitFor(s)}removeMatchingKeysForTargetId(e,t){return this.ai.Hr(t),C.resolve()}getMatchingKeysForTargetId(e,t){let r=this.ai.Yr(t);return C.resolve(r)}containsKey(e,t){return C.resolve(this.ai.containsKey(t))}};var Hs=class{constructor(e,t){this.ci={},this.overlays={},this.li=new Tl(0),this.hi=!1,this.hi=!0,this.Pi=new Cu,this.referenceDelegate=e(this),this.Ti=new Ou(this),this.indexManager=new Iu,this.remoteDocumentCache=function(i){return new Du(i)}(r=>this.referenceDelegate.Ii(r)),this.serializer=new yu(t),this.Ei=new Su(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.hi=!1,Promise.resolve()}get started(){return this.hi}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new Pu,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let r=this.ci[e.toKey()];return r||(r=new ku(t,this.referenceDelegate),this.ci[e.toKey()]=r),r}getGlobalsCache(){return this.Pi}getTargetCache(){return this.Ti}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.Ei}runTransaction(e,t,r){V("MemoryPersistence","Starting transaction:",e);let i=new xu(this.li.next());return this.referenceDelegate.di(),r(i).next(s=>this.referenceDelegate.Ai(i).next(()=>s)).toPromise().then(s=>(i.raiseOnCommittedEvent(),s))}Ri(e,t){return C.or(Object.values(this.ci).map(r=>()=>r.containsKey(e,t)))}},xu=class extends jc{constructor(e){super(),this.currentSequenceNumber=e}},Vu=class n{constructor(e){this.persistence=e,this.Vi=new li,this.mi=null}static fi(e){return new n(e)}get gi(){if(this.mi)return this.mi;throw U(60996)}addReference(e,t,r){return this.Vi.addReference(r,t),this.gi.delete(r.toString()),C.resolve()}removeReference(e,t,r){return this.Vi.removeReference(r,t),this.gi.add(r.toString()),C.resolve()}markPotentiallyOrphaned(e,t){return this.gi.add(t.toString()),C.resolve()}removeTarget(e,t){this.Vi.Hr(t.targetId).forEach(i=>this.gi.add(i.toString()));let r=this.persistence.getTargetCache();return r.getMatchingKeysForTargetId(e,t.targetId).next(i=>{i.forEach(s=>this.gi.add(s.toString()))}).next(()=>r.removeTargetData(e,t))}di(){this.mi=new Set}Ai(e){let t=this.persistence.getRemoteDocumentCache().newChangeBuffer();return C.forEach(this.gi,r=>{let i=M.fromPath(r);return this.pi(e,i).next(s=>{s||t.removeEntry(i,j.min())})}).next(()=>(this.mi=null,t.apply(e)))}updateLimboDocument(e,t){return this.pi(e,t).next(r=>{r?this.gi.delete(t.toString()):this.gi.add(t.toString())})}Ii(e){return 0}pi(e,t){return C.or([()=>C.resolve(this.Vi.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Ri(e,t)])}},Ks=class n{constructor(e,t){this.persistence=e,this.yi=new Pt(r=>wT(r.path),(r,i)=>r.isEqual(i)),this.garbageCollector=Iw(this,t)}static fi(e,t){return new n(e,t)}di(){}Ai(e){return C.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}pr(e){let t=this.Sr(e);return this.persistence.getTargetCache().getTargetCount(e).next(r=>t.next(i=>r+i))}Sr(e){let t=0;return this.yr(e,r=>{t++}).next(()=>t)}yr(e,t){return C.forEach(this.yi,(r,i)=>this.Dr(e,r,i).next(s=>s?C.resolve():t(i)))}removeTargets(e,t,r){return this.persistence.getTargetCache().removeTargets(e,t,r)}removeOrphanedDocuments(e,t){let r=0,i=this.persistence.getRemoteDocumentCache(),s=i.newChangeBuffer();return i.si(e,a=>this.Dr(e,a,t).next(c=>{c||(r++,s.removeEntry(a,j.min()))})).next(()=>s.apply(e)).next(()=>r)}markPotentiallyOrphaned(e,t){return this.yi.set(t,e.currentSequenceNumber),C.resolve()}removeTarget(e,t){let r=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,r)}addReference(e,t,r){return this.yi.set(r,e.currentSequenceNumber),C.resolve()}removeReference(e,t,r){return this.yi.set(r,e.currentSequenceNumber),C.resolve()}updateLimboDocument(e,t){return this.yi.set(t,e.currentSequenceNumber),C.resolve()}Ii(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=Ps(e.data.value)),t}Dr(e,t,r){return C.or([()=>this.persistence.Ri(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{let i=this.yi.get(t);return C.resolve(i!==void 0&&i>r)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}};var Lu=class n{constructor(e,t,r,i){this.targetId=e,this.fromCache=t,this.ds=r,this.As=i}static Rs(e,t){let r=H(),i=H();for(let s of t.docChanges)switch(s.type){case 0:r=r.add(s.doc.key);break;case 1:i=i.add(s.doc.key)}return new n(e,t.fromCache,r,i)}};var Mu=class{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}};var Fu=class{constructor(){this.Vs=!1,this.fs=!1,this.gs=100,this.ps=function(){return Ad()?8:ET(fe())>0?6:4}()}initialize(e,t){this.ys=e,this.indexManager=t,this.Vs=!0}getDocumentsMatchingQuery(e,t,r,i){let s={result:null};return this.ws(e,t).next(a=>{s.result=a}).next(()=>{if(!s.result)return this.Ss(e,t,i,r).next(a=>{s.result=a})}).next(()=>{if(s.result)return;let a=new Mu;return this.bs(e,t,a).next(c=>{if(s.result=c,this.fs)return this.Ds(e,t,a,c.size)})}).next(()=>s.result)}Ds(e,t,r,i){return r.documentReadCount<this.gs?(Bn()<=G.DEBUG&&V("QueryEngine","SDK will not create cache indexes for query:",qn(t),"since it only creates cache indexes for collection contains","more than or equal to",this.gs,"documents"),C.resolve()):(Bn()<=G.DEBUG&&V("QueryEngine","Query:",qn(t),"scans",r.documentReadCount,"local documents and returns",i,"documents as results."),r.documentReadCount>this.ps*i?(Bn()<=G.DEBUG&&V("QueryEngine","The SDK decides to create cache indexes for query:",qn(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,ot(t))):C.resolve())}ws(e,t){if(dp(t))return C.resolve(null);let r=ot(t);return this.indexManager.getIndexType(e,r).next(i=>i===0?null:(t.limit!==null&&i===1&&(t=Bs(t,null,"F"),r=ot(t)),this.indexManager.getDocumentsMatchingTarget(e,r).next(s=>{let a=H(...s);return this.ys.getDocuments(e,a).next(c=>this.indexManager.getMinOffset(e,r).next(l=>{let d=this.vs(t,c);return this.Cs(t,d,a,l.readTime)?this.ws(e,Bs(t,null,"F")):this.Fs(e,d,t,l)}))})))}Ss(e,t,r,i){return dp(t)||i.isEqual(j.min())?C.resolve(null):this.ys.getDocuments(e,r).next(s=>{let a=this.vs(t,s);return this.Cs(t,a,r,i)?C.resolve(null):(Bn()<=G.DEBUG&&V("QueryEngine","Re-using previous result from %s to execute query: %s",i.toString(),qn(t)),this.Fs(e,a,t,_T(i,ni)).next(c=>c))})}vs(e,t){let r=new me(vm(e));return t.forEach((i,s)=>{Io(e,s)&&(r=r.add(s))}),r}Cs(e,t,r,i){if(e.limit===null)return!1;if(r.size!==t.size)return!0;let s=e.limitType==="F"?t.last():t.first();return!!s&&(s.hasPendingWrites||s.version.compareTo(i)>0)}bs(e,t,r){return Bn()<=G.DEBUG&&V("QueryEngine","Using full collection scan to execute query:",qn(t)),this.ys.getDocumentsMatchingQuery(e,t,yn.min(),r)}Fs(e,t,r,i){return this.ys.getDocumentsMatchingQuery(e,r,i).next(s=>(t.forEach(a=>{s=s.insert(a.key,a)}),s))}};var Ol="LocalStore",vw=3e8,Uu=class{constructor(e,t,r,i){this.persistence=e,this.Ms=t,this.serializer=i,this.xs=new oe(W),this.Os=new Pt(s=>Pl(s),Cl),this.Ns=new Map,this.Bs=e.getRemoteDocumentCache(),this.Ti=e.getTargetCache(),this.Ei=e.getBundleCache(),this.Ls(r)}Ls(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new Ru(this.Bs,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Bs.setIndexManager(this.indexManager),this.Ms.initialize(this.localDocuments,this.indexManager)}collectGarbage(e){return this.persistence.runTransaction("Collect garbage","readwrite-primary",t=>e.collect(t,this.xs))}};function Ew(n,e,t,r){return new Uu(n,e,t,r)}function Bm(n,e){return g(this,null,function*(){let t=$(n);return yield t.persistence.runTransaction("Handle user change","readonly",r=>{let i;return t.mutationQueue.getAllMutationBatches(r).next(s=>(i=s,t.Ls(e),t.mutationQueue.getAllMutationBatches(r))).next(s=>{let a=[],c=[],l=H();for(let d of i){a.push(d.batchId);for(let p of d.mutations)l=l.add(p.key)}for(let d of s){c.push(d.batchId);for(let p of d.mutations)l=l.add(p.key)}return t.localDocuments.getDocuments(r,l).next(d=>({ks:d,removedBatchIds:a,addedBatchIds:c}))})})})}function Tw(n,e){let t=$(n);return t.persistence.runTransaction("Acknowledge batch","readwrite-primary",r=>{let i=e.batch.keys(),s=t.Bs.newChangeBuffer({trackRemovals:!0});return function(c,l,d,p){let _=d.batch,I=_.keys(),R=C.resolve();return I.forEach(k=>{R=R.next(()=>p.getEntry(l,k)).next(O=>{let D=d.docVersions.get(k);X(D!==null,48541),O.version.compareTo(D)<0&&(_.applyToRemoteDocument(O,d),O.isValidDocument()&&(O.setReadTime(d.commitVersion),p.addEntry(O)))})}),R.next(()=>c.mutationQueue.removeMutationBatch(l,_))}(t,r,e,s).next(()=>s.apply(r)).next(()=>t.mutationQueue.performConsistencyCheck(r)).next(()=>t.documentOverlayCache.removeOverlaysForBatchId(r,i,e.batch.batchId)).next(()=>t.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(r,function(c){let l=H();for(let d=0;d<c.mutationResults.length;++d)c.mutationResults[d].transformResults.length>0&&(l=l.add(c.batch.mutations[d].key));return l}(e))).next(()=>t.localDocuments.getDocuments(r,i))})}function qm(n){let e=$(n);return e.persistence.runTransaction("Get last remote snapshot version","readonly",t=>e.Ti.getLastRemoteSnapshotVersion(t))}function ww(n,e){let t=$(n),r=e.snapshotVersion,i=t.xs;return t.persistence.runTransaction("Apply remote event","readwrite-primary",s=>{let a=t.Bs.newChangeBuffer({trackRemovals:!0});i=t.xs;let c=[];e.targetChanges.forEach((p,_)=>{let I=i.get(_);if(!I)return;c.push(t.Ti.removeMatchingKeys(s,p.removedDocuments,_).next(()=>t.Ti.addMatchingKeys(s,p.addedDocuments,_)));let R=I.withSequenceNumber(s.currentSequenceNumber);e.targetMismatches.get(_)!==null?R=R.withResumeToken(Se.EMPTY_BYTE_STRING,j.min()).withLastLimboFreeSnapshotVersion(j.min()):p.resumeToken.approximateByteSize()>0&&(R=R.withResumeToken(p.resumeToken,r)),i=i.insert(_,R),function(O,D,q){return O.resumeToken.approximateByteSize()===0||D.snapshotVersion.toMicroseconds()-O.snapshotVersion.toMicroseconds()>=vw?!0:q.addedDocuments.size+q.modifiedDocuments.size+q.removedDocuments.size>0}(I,R,p)&&c.push(t.Ti.updateTargetData(s,R))});let l=Ct(),d=H();if(e.documentUpdates.forEach(p=>{e.resolvedLimboDocuments.has(p)&&c.push(t.persistence.referenceDelegate.updateLimboDocument(s,p))}),c.push(Aw(s,a,e.documentUpdates).next(p=>{l=p.qs,d=p.Qs})),!r.isEqual(j.min())){let p=t.Ti.getLastRemoteSnapshotVersion(s).next(_=>t.Ti.setTargetsMetadata(s,s.currentSequenceNumber,r));c.push(p)}return C.waitFor(c).next(()=>a.apply(s)).next(()=>t.localDocuments.getLocalViewOfDocuments(s,l,d)).next(()=>l)}).then(s=>(t.xs=i,s))}function Aw(n,e,t){let r=H(),i=H();return t.forEach(s=>r=r.add(s)),e.getEntries(n,r).next(s=>{let a=Ct();return t.forEach((c,l)=>{let d=s.get(c);l.isFoundDocument()!==d.isFoundDocument()&&(i=i.add(c)),l.isNoDocument()&&l.version.isEqual(j.min())?(e.removeEntry(c,l.readTime),a=a.insert(c,l)):!d.isValidDocument()||l.version.compareTo(d.version)>0||l.version.compareTo(d.version)===0&&d.hasPendingWrites?(e.addEntry(l),a=a.insert(c,l)):V(Ol,"Ignoring outdated watch update for ",c,". Current version:",d.version," Watch version:",l.version)}),{qs:a,Qs:i}})}function bw(n,e){let t=$(n);return t.persistence.runTransaction("Get next mutation batch","readonly",r=>(e===void 0&&(e=wl),t.mutationQueue.getNextMutationBatchAfterBatchId(r,e)))}function Rw(n,e){let t=$(n);return t.persistence.runTransaction("Allocate target","readwrite",r=>{let i;return t.Ti.getTargetData(r,e).next(s=>s?(i=s,C.resolve(i)):t.Ti.allocateTargetId(r).next(a=>(i=new ci(e,a,"TargetPurposeListen",r.currentSequenceNumber),t.Ti.addTargetData(r,i).next(()=>i))))}).then(r=>{let i=t.xs.get(r.targetId);return(i===null||r.snapshotVersion.compareTo(i.snapshotVersion)>0)&&(t.xs=t.xs.insert(r.targetId,r),t.Os.set(e,r.targetId)),r})}function Bu(n,e,t){return g(this,null,function*(){let r=$(n),i=r.xs.get(e),s=t?"readwrite":"readwrite-primary";try{t||(yield r.persistence.runTransaction("Release target",s,a=>r.persistence.referenceDelegate.removeTarget(a,i)))}catch(a){if(!cr(a))throw a;V(Ol,`Failed to update sequence numbers for target ${e}: ${a}`)}r.xs=r.xs.remove(e),r.Os.delete(i.target)})}function bp(n,e,t){let r=$(n),i=j.min(),s=H();return r.persistence.runTransaction("Execute query","readwrite",a=>function(l,d,p){let _=$(l),I=_.Os.get(p);return I!==void 0?C.resolve(_.xs.get(I)):_.Ti.getTargetData(d,p)}(r,a,ot(e)).next(c=>{if(c)return i=c.lastLimboFreeSnapshotVersion,r.Ti.getMatchingKeysForTargetId(a,c.targetId).next(l=>{s=l})}).next(()=>r.Ms.getDocumentsMatchingQuery(a,e,t?i:j.min(),t?s:H())).next(c=>(Sw(r,BT(e),c),{documents:c,$s:s})))}function Sw(n,e,t){let r=n.Ns.get(e)||j.min();t.forEach((i,s)=>{s.readTime.compareTo(r)>0&&(r=s.readTime)}),n.Ns.set(e,r)}var Qs=class{constructor(){this.activeTargetIds=GT()}js(e){this.activeTargetIds=this.activeTargetIds.add(e)}Hs(e){this.activeTargetIds=this.activeTargetIds.delete(e)}zs(){let e={activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()};return JSON.stringify(e)}};var qu=class{constructor(){this.xo=new Qs,this.Oo={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,r){}addLocalQueryTarget(e,t=!0){return t&&this.xo.js(e),this.Oo[e]||"not-current"}updateQueryState(e,t,r){this.Oo[e]=t}removeLocalQueryTarget(e){this.xo.Hs(e)}isLocalQueryTarget(e){return this.xo.activeTargetIds.has(e)}clearQueryState(e){delete this.Oo[e]}getAllActiveQueryTargets(){return this.xo.activeTargetIds}isActiveQueryTarget(e){return this.xo.activeTargetIds.has(e)}start(){return this.xo=new Qs,Promise.resolve()}handleUserChange(e,t,r){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}};var $u=class{No(e){}shutdown(){}};var Rp="ConnectivityMonitor",Ys=class{constructor(){this.Bo=()=>this.Lo(),this.ko=()=>this.qo(),this.Qo=[],this.$o()}No(e){this.Qo.push(e)}shutdown(){window.removeEventListener("online",this.Bo),window.removeEventListener("offline",this.ko)}$o(){window.addEventListener("online",this.Bo),window.addEventListener("offline",this.ko)}Lo(){V(Rp,"Network connectivity changed: AVAILABLE");for(let e of this.Qo)e(0)}qo(){V(Rp,"Network connectivity changed: UNAVAILABLE");for(let e of this.Qo)e(1)}static C(){return typeof window<"u"&&window.addEventListener!==void 0&&window.removeEventListener!==void 0}};var Ss=null;function ju(){return Ss===null?Ss=function(){return 268435456+Math.round(2147483648*Math.random())}():Ss++,"0x"+Ss.toString(16)}var Nc="RestConnection",Pw={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"},zu=class{get Uo(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;let t=e.ssl?"https":"http",r=encodeURIComponent(this.databaseId.projectId),i=encodeURIComponent(this.databaseId.database);this.Ko=t+"://"+e.host,this.Wo=`projects/${r}/databases/${i}`,this.Go=this.databaseId.database===Ms?`project_id=${r}`:`project_id=${r}&database_id=${i}`}zo(e,t,r,i,s){let a=ju(),c=this.jo(e,t.toUriEncodedString());V(Nc,`Sending RPC '${e}' ${a}:`,c,r);let l={"google-cloud-resource-prefix":this.Wo,"x-goog-request-params":this.Go};this.Ho(l,i,s);let{host:d}=new URL(c),p=Je(d);return this.Jo(e,c,l,r,p).then(_=>(V(Nc,`Received RPC '${e}' ${a}: `,_),_),_=>{throw Hn(Nc,`RPC '${e}' ${a} failed with error: `,_,"url: ",c,"request:",r),_})}Yo(e,t,r,i,s,a){return this.zo(e,t,r,i,s)}Ho(e,t,r){e["X-Goog-Api-Client"]=function(){return"gl-js/ fire/"+or}(),e["Content-Type"]="text/plain",this.databaseInfo.appId&&(e["X-Firebase-GMPID"]=this.databaseInfo.appId),t&&t.headers.forEach((i,s)=>e[s]=i),r&&r.headers.forEach((i,s)=>e[s]=i)}jo(e,t){let r=Pw[e];return`${this.Ko}/v1/${t}:${r}`}terminate(){}};var Wu=class{constructor(e){this.Zo=e.Zo,this.Xo=e.Xo}e_(e){this.t_=e}n_(e){this.r_=e}i_(e){this.s_=e}onMessage(e){this.o_=e}close(){this.Xo()}send(e){this.Zo(e)}__(){this.t_()}a_(){this.r_()}u_(e){this.s_(e)}c_(e){this.o_(e)}};var Re="WebChannelConnection",Gu=class extends zu{constructor(e){super(e),this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}Jo(e,t,r,i,s){let a=ju();return new Promise((c,l)=>{let d=new Rc;d.setWithCredentials(!0),d.listenOnce(Sc.COMPLETE,()=>{try{switch(d.getLastErrorCode()){case Yr.NO_ERROR:let _=d.getResponseJson();V(Re,`XHR for RPC '${e}' ${a} received:`,JSON.stringify(_)),c(_);break;case Yr.TIMEOUT:V(Re,`RPC '${e}' ${a} timed out`),l(new x(P.DEADLINE_EXCEEDED,"Request time out"));break;case Yr.HTTP_ERROR:let I=d.getStatus();if(V(Re,`RPC '${e}' ${a} failed with status:`,I,"response text:",d.getResponseText()),I>0){let R=d.getResponseJson();Array.isArray(R)&&(R=R[0]);let k=R==null?void 0:R.error;if(k&&k.status&&k.message){let O=function(q){let L=q.toLowerCase().replace(/_/g,"-");return Object.values(P).indexOf(L)>=0?L:P.UNKNOWN}(k.status);l(new x(O,k.message))}else l(new x(P.UNKNOWN,"Server responded with status "+d.getStatus()))}else l(new x(P.UNAVAILABLE,"Connection failed."));break;default:U(9055,{l_:e,streamId:a,h_:d.getLastErrorCode(),P_:d.getLastError()})}}finally{V(Re,`RPC '${e}' ${a} completed.`)}});let p=JSON.stringify(i);V(Re,`RPC '${e}' ${a} sending request:`,i),d.send(t,"POST",p,r,15)})}T_(e,t,r){let i=ju(),s=[this.Ko,"/","google.firestore.v1.Firestore","/",e,"/channel"],a=kc(),c=Cc(),l={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/${this.databaseId.database}`},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},d=this.longPollingOptions.timeoutSeconds;d!==void 0&&(l.longPollingTimeout=Math.round(1e3*d)),this.useFetchStreams&&(l.useFetchStreams=!0),this.Ho(l.initMessageHeaders,t,r),l.encodeInitMessageHeaders=!0;let p=s.join("");V(Re,`Creating RPC '${e}' stream ${i}: ${p}`,l);let _=a.createWebChannel(p,l),I=!1,R=!1,k=new Wu({Zo:D=>{R?V(Re,`Not sending because RPC '${e}' stream ${i} is closed:`,D):(I||(V(Re,`Opening RPC '${e}' stream ${i} transport.`),_.open(),I=!0),V(Re,`RPC '${e}' stream ${i} sending:`,D),_.send(D))},Xo:()=>_.close()}),O=(D,q,L)=>{D.listen(q,F=>{try{L(F)}catch(z){setTimeout(()=>{throw z},0)}})};return O(_,Un.EventType.OPEN,()=>{R||(V(Re,`RPC '${e}' stream ${i} transport opened.`),k.__())}),O(_,Un.EventType.CLOSE,()=>{R||(R=!0,V(Re,`RPC '${e}' stream ${i} transport closed`),k.u_())}),O(_,Un.EventType.ERROR,D=>{R||(R=!0,Hn(Re,`RPC '${e}' stream ${i} transport errored. Name:`,D.name,"Message:",D.message),k.u_(new x(P.UNAVAILABLE,"The operation could not be completed")))}),O(_,Un.EventType.MESSAGE,D=>{var q;if(!R){let L=D.data[0];X(!!L,16349);let F=L,z=(F==null?void 0:F.error)||((q=F[0])===null||q===void 0?void 0:q.error);if(z){V(Re,`RPC '${e}' stream ${i} received error:`,z);let ue=z.status,Q=function(v){let E=le[v];if(E!==void 0)return Dm(E)}(ue),T=z.message;Q===void 0&&(Q=P.INTERNAL,T="Unknown error status: "+ue+" with message "+z.message),R=!0,k.u_(new x(Q,T)),_.close()}else V(Re,`RPC '${e}' stream ${i} received:`,L),k.c_(L)}}),O(c,Pc.STAT_EVENT,D=>{D.stat===As.PROXY?V(Re,`RPC '${e}' stream ${i} detected buffering proxy`):D.stat===As.NOPROXY&&V(Re,`RPC '${e}' stream ${i} detected no buffering proxy`)}),setTimeout(()=>{k.a_()},0),k}};function Oc(){return typeof document<"u"?document:null}function vo(n){return new fu(n,!0)}var Js=class{constructor(e,t,r=1e3,i=1.5,s=6e4){this.xi=e,this.timerId=t,this.I_=r,this.E_=i,this.d_=s,this.A_=0,this.R_=null,this.V_=Date.now(),this.reset()}reset(){this.A_=0}m_(){this.A_=this.d_}f_(e){this.cancel();let t=Math.floor(this.A_+this.g_()),r=Math.max(0,Date.now()-this.V_),i=Math.max(0,t-r);i>0&&V("ExponentialBackoff",`Backing off for ${i} ms (base delay: ${this.A_} ms, delay with jitter: ${t} ms, last attempt: ${r} ms ago)`),this.R_=this.xi.enqueueAfterDelay(this.timerId,i,()=>(this.V_=Date.now(),e())),this.A_*=this.E_,this.A_<this.I_&&(this.A_=this.I_),this.A_>this.d_&&(this.A_=this.d_)}p_(){this.R_!==null&&(this.R_.skipDelay(),this.R_=null)}cancel(){this.R_!==null&&(this.R_.cancel(),this.R_=null)}g_(){return(Math.random()-.5)*this.A_}};var Sp="PersistentStream",Xs=class{constructor(e,t,r,i,s,a,c,l){this.xi=e,this.y_=r,this.w_=i,this.connection=s,this.authCredentialsProvider=a,this.appCheckCredentialsProvider=c,this.listener=l,this.state=0,this.S_=0,this.b_=null,this.D_=null,this.stream=null,this.v_=0,this.C_=new Js(e,t)}F_(){return this.state===1||this.state===5||this.M_()}M_(){return this.state===2||this.state===3}start(){this.v_=0,this.state!==4?this.auth():this.x_()}stop(){return g(this,null,function*(){this.F_()&&(yield this.close(0))})}O_(){this.state=0,this.C_.reset()}N_(){this.M_()&&this.b_===null&&(this.b_=this.xi.enqueueAfterDelay(this.y_,6e4,()=>this.B_()))}L_(e){this.k_(),this.stream.send(e)}B_(){return g(this,null,function*(){if(this.M_())return this.close(0)})}k_(){this.b_&&(this.b_.cancel(),this.b_=null)}q_(){this.D_&&(this.D_.cancel(),this.D_=null)}close(e,t){return g(this,null,function*(){this.k_(),this.q_(),this.C_.cancel(),this.S_++,e!==4?this.C_.reset():t&&t.code===P.RESOURCE_EXHAUSTED?(bt(t.toString()),bt("Using maximum backoff delay to prevent overloading the backend."),this.C_.m_()):t&&t.code===P.UNAUTHENTICATED&&this.state!==3&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),this.stream!==null&&(this.Q_(),this.stream.close(),this.stream=null),this.state=e,yield this.listener.i_(t)})}Q_(){}auth(){this.state=1;let e=this.U_(this.S_),t=this.S_;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then(([r,i])=>{this.S_===t&&this.K_(r,i)},r=>{e(()=>{let i=new x(P.UNKNOWN,"Fetching auth token failed: "+r.message);return this.W_(i)})})}K_(e,t){let r=this.U_(this.S_);this.stream=this.G_(e,t),this.stream.e_(()=>{r(()=>this.listener.e_())}),this.stream.n_(()=>{r(()=>(this.state=2,this.D_=this.xi.enqueueAfterDelay(this.w_,1e4,()=>(this.M_()&&(this.state=3),Promise.resolve())),this.listener.n_()))}),this.stream.i_(i=>{r(()=>this.W_(i))}),this.stream.onMessage(i=>{r(()=>++this.v_==1?this.z_(i):this.onNext(i))})}x_(){this.state=5,this.C_.f_(()=>g(this,null,function*(){this.state=0,this.start()}))}W_(e){return V(Sp,`close with error: ${e}`),this.stream=null,this.close(4,e)}U_(e){return t=>{this.xi.enqueueAndForget(()=>this.S_===e?t():(V(Sp,"stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve()))}}},Hu=class extends Xs{constructor(e,t,r,i,s,a){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,r,i,a),this.serializer=s}G_(e,t){return this.connection.T_("Listen",e,t)}z_(e){return this.onNext(e)}onNext(e){this.C_.reset();let t=ow(this.serializer,e),r=function(s){if(!("targetChange"in s))return j.min();let a=s.targetChange;return a.targetIds&&a.targetIds.length?j.min():a.readTime?ct(a.readTime):j.min()}(e);return this.listener.j_(t,r)}H_(e){let t={};t.database=_u(this.serializer),t.addTarget=function(s,a){let c,l=a.target;if(c=nu(l)?{documents:uw(s,l)}:{query:lw(s,l).gt},c.targetId=a.targetId,a.resumeToken.approximateByteSize()>0){c.resumeToken=Nm(s,a.resumeToken);let d=pu(s,a.expectedCount);d!==null&&(c.expectedCount=d)}else if(a.snapshotVersion.compareTo(j.min())>0){c.readTime=Ws(s,a.snapshotVersion.toTimestamp());let d=pu(s,a.expectedCount);d!==null&&(c.expectedCount=d)}return c}(this.serializer,e);let r=dw(this.serializer,e);r&&(t.labels=r),this.L_(t)}J_(e){let t={};t.database=_u(this.serializer),t.removeTarget=e,this.L_(t)}},Ku=class extends Xs{constructor(e,t,r,i,s,a){super(e,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",t,r,i,a),this.serializer=s}get Y_(){return this.v_>0}start(){this.lastStreamToken=void 0,super.start()}Q_(){this.Y_&&this.Z_([])}G_(e,t){return this.connection.T_("Write",e,t)}z_(e){return X(!!e.streamToken,31322),this.lastStreamToken=e.streamToken,X(!e.writeResults||e.writeResults.length===0,55816),this.listener.X_()}onNext(e){X(!!e.streamToken,12678),this.lastStreamToken=e.streamToken,this.C_.reset();let t=cw(e.writeResults,e.commitTime),r=ct(e.commitTime);return this.listener.ea(r,t)}ta(){let e={};e.database=_u(this.serializer),this.L_(e)}Z_(e){let t={streamToken:this.lastStreamToken,writes:e.map(r=>aw(this.serializer,r))};this.L_(t)}};var Qu=class{},Yu=class extends Qu{constructor(e,t,r,i){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=r,this.serializer=i,this.na=!1}ra(){if(this.na)throw new x(P.FAILED_PRECONDITION,"The client has already been terminated.")}zo(e,t,r,i){return this.ra(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([s,a])=>this.connection.zo(e,mu(t,r),i,s,a)).catch(s=>{throw s.name==="FirebaseError"?(s.code===P.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),s):new x(P.UNKNOWN,s.toString())})}Yo(e,t,r,i,s){return this.ra(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([a,c])=>this.connection.Yo(e,mu(t,r),i,a,c,s)).catch(a=>{throw a.name==="FirebaseError"?(a.code===P.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),a):new x(P.UNKNOWN,a.toString())})}terminate(){this.na=!0,this.connection.terminate()}},Ju=class{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this.ia=0,this.sa=null,this.oa=!0}_a(){this.ia===0&&(this.aa("Unknown"),this.sa=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,()=>(this.sa=null,this.ua("Backend didn't respond within 10 seconds."),this.aa("Offline"),Promise.resolve())))}ca(e){this.state==="Online"?this.aa("Unknown"):(this.ia++,this.ia>=1&&(this.la(),this.ua(`Connection failed 1 times. Most recent error: ${e.toString()}`),this.aa("Offline")))}set(e){this.la(),this.ia=0,e==="Online"&&(this.oa=!1),this.aa(e)}aa(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}ua(e){let t=`Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.oa?(bt(t),this.oa=!1):V("OnlineStateTracker",t)}la(){this.sa!==null&&(this.sa.cancel(),this.sa=null)}};var An="RemoteStore",Xu=class{constructor(e,t,r,i,s){this.localStore=e,this.datastore=t,this.asyncQueue=r,this.remoteSyncer={},this.ha=[],this.Pa=new Map,this.Ta=new Set,this.Ia=[],this.Ea=s,this.Ea.No(a=>{r.enqueueAndForget(()=>g(this,null,function*(){bn(this)&&(V(An,"Restarting streams for network reachability change."),yield function(l){return g(this,null,function*(){let d=$(l);d.Ta.add(4),yield gi(d),d.da.set("Unknown"),d.Ta.delete(4),yield Eo(d)})}(this))}))}),this.da=new Ju(r,i)}};function Eo(n){return g(this,null,function*(){if(bn(n))for(let e of n.Ia)yield e(!0)})}function gi(n){return g(this,null,function*(){for(let e of n.Ia)yield e(!1)})}function $m(n,e){let t=$(n);t.Pa.has(e.targetId)||(t.Pa.set(e.targetId,e),Ml(t)?Ll(t):ur(t).M_()&&Vl(t,e))}function xl(n,e){let t=$(n),r=ur(t);t.Pa.delete(e),r.M_()&&jm(t,e),t.Pa.size===0&&(r.M_()?r.N_():bn(t)&&t.da.set("Unknown"))}function Vl(n,e){if(n.Aa.Ke(e.targetId),e.resumeToken.approximateByteSize()>0||e.snapshotVersion.compareTo(j.min())>0){let t=n.remoteSyncer.getRemoteKeysForTarget(e.targetId).size;e=e.withExpectedCount(t)}ur(n).H_(e)}function jm(n,e){n.Aa.Ke(e),ur(n).J_(e)}function Ll(n){n.Aa=new du({getRemoteKeysForTarget:e=>n.remoteSyncer.getRemoteKeysForTarget(e),Rt:e=>n.Pa.get(e)||null,Pt:()=>n.datastore.serializer.databaseId}),ur(n).start(),n.da._a()}function Ml(n){return bn(n)&&!ur(n).F_()&&n.Pa.size>0}function bn(n){return $(n).Ta.size===0}function zm(n){n.Aa=void 0}function Cw(n){return g(this,null,function*(){n.da.set("Online")})}function kw(n){return g(this,null,function*(){n.Pa.forEach((e,t)=>{Vl(n,e)})})}function Dw(n,e){return g(this,null,function*(){zm(n),Ml(n)?(n.da.ca(e),Ll(n)):n.da.set("Unknown")})}function Nw(n,e,t){return g(this,null,function*(){if(n.da.set("Online"),e instanceof js&&e.state===2&&e.cause)try{yield function(i,s){return g(this,null,function*(){let a=s.cause;for(let c of s.targetIds)i.Pa.has(c)&&(yield i.remoteSyncer.rejectListen(c,a),i.Pa.delete(c),i.Aa.removeTarget(c))})}(n,e)}catch(r){V(An,"Failed to remove targets %s: %s ",e.targetIds.join(","),r),yield Zs(n,r)}else if(e instanceof Wn?n.Aa.Xe(e):e instanceof $s?n.Aa.ot(e):n.Aa.nt(e),!t.isEqual(j.min()))try{let r=yield qm(n.localStore);t.compareTo(r)>=0&&(yield function(s,a){let c=s.Aa.It(a);return c.targetChanges.forEach((l,d)=>{if(l.resumeToken.approximateByteSize()>0){let p=s.Pa.get(d);p&&s.Pa.set(d,p.withResumeToken(l.resumeToken,a))}}),c.targetMismatches.forEach((l,d)=>{let p=s.Pa.get(l);if(!p)return;s.Pa.set(l,p.withResumeToken(Se.EMPTY_BYTE_STRING,p.snapshotVersion)),jm(s,l);let _=new ci(p.target,l,d,p.sequenceNumber);Vl(s,_)}),s.remoteSyncer.applyRemoteEvent(c)}(n,t))}catch(r){V(An,"Failed to raise snapshot:",r),yield Zs(n,r)}})}function Zs(n,e,t){return g(this,null,function*(){if(!cr(e))throw e;n.Ta.add(1),yield gi(n),n.da.set("Offline"),t||(t=()=>qm(n.localStore)),n.asyncQueue.enqueueRetryable(()=>g(null,null,function*(){V(An,"Retrying IndexedDB access"),yield t(),n.Ta.delete(1),yield Eo(n)}))})}function Wm(n,e){return e().catch(t=>Zs(n,t,e))}function To(n){return g(this,null,function*(){let e=$(n),t=Xt(e),r=e.ha.length>0?e.ha[e.ha.length-1].batchId:wl;for(;Ow(e);)try{let i=yield bw(e.localStore,r);if(i===null){e.ha.length===0&&t.N_();break}r=i.batchId,xw(e,i)}catch(i){yield Zs(e,i)}Gm(e)&&Hm(e)})}function Ow(n){return bn(n)&&n.ha.length<10}function xw(n,e){n.ha.push(e);let t=Xt(n);t.M_()&&t.Y_&&t.Z_(e.mutations)}function Gm(n){return bn(n)&&!Xt(n).F_()&&n.ha.length>0}function Hm(n){Xt(n).start()}function Vw(n){return g(this,null,function*(){Xt(n).ta()})}function Lw(n){return g(this,null,function*(){let e=Xt(n);for(let t of n.ha)e.Z_(t.mutations)})}function Mw(n,e,t){return g(this,null,function*(){let r=n.ha.shift(),i=cu.from(r,e,t);yield Wm(n,()=>n.remoteSyncer.applySuccessfulWrite(i)),yield To(n)})}function Fw(n,e){return g(this,null,function*(){e&&Xt(n).Y_&&(yield function(r,i){return g(this,null,function*(){if(function(a){return ZT(a)&&a!==P.ABORTED}(i.code)){let s=r.ha.shift();Xt(r).O_(),yield Wm(r,()=>r.remoteSyncer.rejectFailedWrite(s.batchId,i)),yield To(r)}})}(n,e)),Gm(n)&&Hm(n)})}function Pp(n,e){return g(this,null,function*(){let t=$(n);t.asyncQueue.verifyOperationInProgress(),V(An,"RemoteStore received new credentials");let r=bn(t);t.Ta.add(3),yield gi(t),r&&t.da.set("Unknown"),yield t.remoteSyncer.handleCredentialChange(e),t.Ta.delete(3),yield Eo(t)})}function Uw(n,e){return g(this,null,function*(){let t=$(n);e?(t.Ta.delete(2),yield Eo(t)):e||(t.Ta.add(2),yield gi(t),t.da.set("Unknown"))})}function ur(n){return n.Ra||(n.Ra=function(t,r,i){let s=$(t);return s.ra(),new Hu(r,s.connection,s.authCredentials,s.appCheckCredentials,s.serializer,i)}(n.datastore,n.asyncQueue,{e_:Cw.bind(null,n),n_:kw.bind(null,n),i_:Dw.bind(null,n),j_:Nw.bind(null,n)}),n.Ia.push(e=>g(null,null,function*(){e?(n.Ra.O_(),Ml(n)?Ll(n):n.da.set("Unknown")):(yield n.Ra.stop(),zm(n))}))),n.Ra}function Xt(n){return n.Va||(n.Va=function(t,r,i){let s=$(t);return s.ra(),new Ku(r,s.connection,s.authCredentials,s.appCheckCredentials,s.serializer,i)}(n.datastore,n.asyncQueue,{e_:()=>Promise.resolve(),n_:Vw.bind(null,n),i_:Fw.bind(null,n),X_:Lw.bind(null,n),ea:Mw.bind(null,n)}),n.Ia.push(e=>g(null,null,function*(){e?(n.Va.O_(),yield To(n)):(yield n.Va.stop(),n.ha.length>0&&(V(An,`Stopping write stream with ${n.ha.length} pending writes`),n.ha=[]))}))),n.Va}var Zu=class n{constructor(e,t,r,i,s){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=r,this.op=i,this.removalCallback=s,this.deferred=new We,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(a=>{})}get promise(){return this.deferred.promise}static createAndSchedule(e,t,r,i,s){let a=Date.now()+r,c=new n(e,t,a,i,s);return c.start(r),c}start(e){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){this.timerHandle!==null&&(this.clearTimeout(),this.deferred.reject(new x(P.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>this.timerHandle!==null?(this.clearTimeout(),this.op().then(e=>this.deferred.resolve(e))):Promise.resolve())}clearTimeout(){this.timerHandle!==null&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}};function Fl(n,e){if(bt("AsyncQueue",`${e}: ${n}`),cr(n))return new x(P.UNAVAILABLE,`${e}: ${n}`);throw n}var eo=class n{static emptySet(e){return new n(e.comparator)}constructor(e){this.comparator=e?(t,r)=>e(t,r)||M.comparator(t.key,r.key):(t,r)=>M.comparator(t.key,r.key),this.keyedMap=Jr(),this.sortedSet=new oe(this.comparator)}has(e){return this.keyedMap.get(e)!=null}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){let t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(e){this.sortedSet.inorderTraversal((t,r)=>(e(t),!1))}add(e){let t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){let t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof n)||this.size!==e.size)return!1;let t=this.sortedSet.getIterator(),r=e.sortedSet.getIterator();for(;t.hasNext();){let i=t.getNext().key,s=r.getNext().key;if(!i.isEqual(s))return!1}return!0}toString(){let e=[];return this.forEach(t=>{e.push(t.toString())}),e.length===0?"DocumentSet ()":`DocumentSet (
  `+e.join(`  
`)+`
)`}copy(e,t){let r=new n;return r.comparator=this.comparator,r.keyedMap=e,r.sortedSet=t,r}};var to=class{constructor(){this.ma=new oe(M.comparator)}track(e){let t=e.doc.key,r=this.ma.get(t);r?e.type!==0&&r.type===3?this.ma=this.ma.insert(t,e):e.type===3&&r.type!==1?this.ma=this.ma.insert(t,{type:r.type,doc:e.doc}):e.type===2&&r.type===2?this.ma=this.ma.insert(t,{type:2,doc:e.doc}):e.type===2&&r.type===0?this.ma=this.ma.insert(t,{type:0,doc:e.doc}):e.type===1&&r.type===0?this.ma=this.ma.remove(t):e.type===1&&r.type===2?this.ma=this.ma.insert(t,{type:1,doc:r.doc}):e.type===0&&r.type===1?this.ma=this.ma.insert(t,{type:2,doc:e.doc}):U(63341,{Vt:e,fa:r}):this.ma=this.ma.insert(t,e)}ga(){let e=[];return this.ma.inorderTraversal((t,r)=>{e.push(r)}),e}},nr=class n{constructor(e,t,r,i,s,a,c,l,d){this.query=e,this.docs=t,this.oldDocs=r,this.docChanges=i,this.mutatedKeys=s,this.fromCache=a,this.syncStateChanged=c,this.excludesMetadataChanges=l,this.hasCachedResults=d}static fromInitialDocuments(e,t,r,i,s){let a=[];return t.forEach(c=>{a.push({type:0,doc:c})}),new n(e,t,eo.emptySet(t),a,r,i,!0,!1,s)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&yo(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;let t=this.docChanges,r=e.docChanges;if(t.length!==r.length)return!1;for(let i=0;i<t.length;i++)if(t[i].type!==r[i].type||!t[i].doc.isEqual(r[i].doc))return!1;return!0}};var el=class{constructor(){this.pa=void 0,this.ya=[]}wa(){return this.ya.some(e=>e.Sa())}},tl=class{constructor(){this.queries=Cp(),this.onlineState="Unknown",this.ba=new Set}terminate(){(function(t,r){let i=$(t),s=i.queries;i.queries=Cp(),s.forEach((a,c)=>{for(let l of c.ya)l.onError(r)})})(this,new x(P.ABORTED,"Firestore shutting down"))}};function Cp(){return new Pt(n=>Im(n),yo)}function Km(n,e){return g(this,null,function*(){let t=$(n),r=3,i=e.query,s=t.queries.get(i);s?!s.wa()&&e.Sa()&&(r=2):(s=new el,r=e.Sa()?0:1);try{switch(r){case 0:s.pa=yield t.onListen(i,!0);break;case 1:s.pa=yield t.onListen(i,!1);break;case 2:yield t.onFirstRemoteStoreListen(i)}}catch(a){let c=Fl(a,`Initialization of query '${qn(e.query)}' failed`);return void e.onError(c)}t.queries.set(i,s),s.ya.push(e),e.Da(t.onlineState),s.pa&&e.va(s.pa)&&Ul(t)})}function Qm(n,e){return g(this,null,function*(){let t=$(n),r=e.query,i=3,s=t.queries.get(r);if(s){let a=s.ya.indexOf(e);a>=0&&(s.ya.splice(a,1),s.ya.length===0?i=e.Sa()?0:1:!s.wa()&&e.Sa()&&(i=2))}switch(i){case 0:return t.queries.delete(r),t.onUnlisten(r,!0);case 1:return t.queries.delete(r),t.onUnlisten(r,!1);case 2:return t.onLastRemoteStoreUnlisten(r);default:return}})}function Bw(n,e){let t=$(n),r=!1;for(let i of e){let s=i.query,a=t.queries.get(s);if(a){for(let c of a.ya)c.va(i)&&(r=!0);a.pa=i}}r&&Ul(t)}function qw(n,e,t){let r=$(n),i=r.queries.get(e);if(i)for(let s of i.ya)s.onError(t);r.queries.delete(e)}function Ul(n){n.ba.forEach(e=>{e.next()})}var nl,kp;(kp=nl||(nl={})).Ca="default",kp.Cache="cache";var no=class{constructor(e,t,r){this.query=e,this.Fa=t,this.Ma=!1,this.xa=null,this.onlineState="Unknown",this.options=r||{}}va(e){if(!this.options.includeMetadataChanges){let r=[];for(let i of e.docChanges)i.type!==3&&r.push(i);e=new nr(e.query,e.docs,e.oldDocs,r,e.mutatedKeys,e.fromCache,e.syncStateChanged,!0,e.hasCachedResults)}let t=!1;return this.Ma?this.Oa(e)&&(this.Fa.next(e),t=!0):this.Na(e,this.onlineState)&&(this.Ba(e),t=!0),this.xa=e,t}onError(e){this.Fa.error(e)}Da(e){this.onlineState=e;let t=!1;return this.xa&&!this.Ma&&this.Na(this.xa,e)&&(this.Ba(this.xa),t=!0),t}Na(e,t){if(!e.fromCache||!this.Sa())return!0;let r=t!=="Offline";return(!this.options.La||!r)&&(!e.docs.isEmpty()||e.hasCachedResults||t==="Offline")}Oa(e){if(e.docChanges.length>0)return!0;let t=this.xa&&this.xa.hasPendingWrites!==e.hasPendingWrites;return!(!e.syncStateChanged&&!t)&&this.options.includeMetadataChanges===!0}Ba(e){e=nr.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.Ma=!0,this.Fa.next(e)}Sa(){return this.options.source!==nl.Cache}};var ro=class{constructor(e){this.key=e}},io=class{constructor(e){this.key=e}},rl=class{constructor(e,t){this.query=e,this.Ga=t,this.za=null,this.hasCachedResults=!1,this.current=!1,this.ja=H(),this.mutatedKeys=H(),this.Ha=vm(e),this.Ja=new eo(this.Ha)}get Ya(){return this.Ga}Za(e,t){let r=t?t.Xa:new to,i=t?t.Ja:this.Ja,s=t?t.mutatedKeys:this.mutatedKeys,a=i,c=!1,l=this.query.limitType==="F"&&i.size===this.query.limit?i.last():null,d=this.query.limitType==="L"&&i.size===this.query.limit?i.first():null;if(e.inorderTraversal((p,_)=>{let I=i.get(p),R=Io(this.query,_)?_:null,k=!!I&&this.mutatedKeys.has(I.key),O=!!R&&(R.hasLocalMutations||this.mutatedKeys.has(R.key)&&R.hasCommittedMutations),D=!1;I&&R?I.data.isEqual(R.data)?k!==O&&(r.track({type:3,doc:R}),D=!0):this.eu(I,R)||(r.track({type:2,doc:R}),D=!0,(l&&this.Ha(R,l)>0||d&&this.Ha(R,d)<0)&&(c=!0)):!I&&R?(r.track({type:0,doc:R}),D=!0):I&&!R&&(r.track({type:1,doc:I}),D=!0,(l||d)&&(c=!0)),D&&(R?(a=a.add(R),s=O?s.add(p):s.delete(p)):(a=a.delete(p),s=s.delete(p)))}),this.query.limit!==null)for(;a.size>this.query.limit;){let p=this.query.limitType==="F"?a.last():a.first();a=a.delete(p.key),s=s.delete(p.key),r.track({type:1,doc:p})}return{Ja:a,Xa:r,Cs:c,mutatedKeys:s}}eu(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,r,i){let s=this.Ja;this.Ja=e.Ja,this.mutatedKeys=e.mutatedKeys;let a=e.Xa.ga();a.sort((p,_)=>function(R,k){let O=D=>{switch(D){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return U(20277,{Vt:D})}};return O(R)-O(k)}(p.type,_.type)||this.Ha(p.doc,_.doc)),this.tu(r),i=i!=null&&i;let c=t&&!i?this.nu():[],l=this.ja.size===0&&this.current&&!i?1:0,d=l!==this.za;return this.za=l,a.length!==0||d?{snapshot:new nr(this.query,e.Ja,s,a,e.mutatedKeys,l===0,d,!1,!!r&&r.resumeToken.approximateByteSize()>0),ru:c}:{ru:c}}Da(e){return this.current&&e==="Offline"?(this.current=!1,this.applyChanges({Ja:this.Ja,Xa:new to,mutatedKeys:this.mutatedKeys,Cs:!1},!1)):{ru:[]}}iu(e){return!this.Ga.has(e)&&!!this.Ja.has(e)&&!this.Ja.get(e).hasLocalMutations}tu(e){e&&(e.addedDocuments.forEach(t=>this.Ga=this.Ga.add(t)),e.modifiedDocuments.forEach(t=>{}),e.removedDocuments.forEach(t=>this.Ga=this.Ga.delete(t)),this.current=e.current)}nu(){if(!this.current)return[];let e=this.ja;this.ja=H(),this.Ja.forEach(r=>{this.iu(r.key)&&(this.ja=this.ja.add(r.key))});let t=[];return e.forEach(r=>{this.ja.has(r)||t.push(new io(r))}),this.ja.forEach(r=>{e.has(r)||t.push(new ro(r))}),t}su(e){this.Ga=e.$s,this.ja=H();let t=this.Za(e.documents);return this.applyChanges(t,!0)}ou(){return nr.fromInitialDocuments(this.query,this.Ja,this.mutatedKeys,this.za===0,this.hasCachedResults)}},Bl="SyncEngine",il=class{constructor(e,t,r){this.query=e,this.targetId=t,this.view=r}},sl=class{constructor(e){this.key=e,this._u=!1}},ol=class{constructor(e,t,r,i,s,a){this.localStore=e,this.remoteStore=t,this.eventManager=r,this.sharedClientState=i,this.currentUser=s,this.maxConcurrentLimboResolutions=a,this.au={},this.uu=new Pt(c=>Im(c),yo),this.cu=new Map,this.lu=new Set,this.hu=new oe(M.comparator),this.Pu=new Map,this.Tu=new li,this.Iu={},this.Eu=new Map,this.du=ui.lr(),this.onlineState="Unknown",this.Au=void 0}get isPrimaryClient(){return this.Au===!0}};function $w(n,e,t=!0){return g(this,null,function*(){let r=tg(n),i,s=r.uu.get(e);return s?(r.sharedClientState.addLocalQueryTarget(s.targetId),i=s.view.ou()):i=yield Ym(r,e,t,!0),i})}function jw(n,e){return g(this,null,function*(){let t=tg(n);yield Ym(t,e,!0,!1)})}function Ym(n,e,t,r){return g(this,null,function*(){let i=yield Rw(n.localStore,ot(e)),s=i.targetId,a=n.sharedClientState.addLocalQueryTarget(s,t),c;return r&&(c=yield zw(n,e,s,a==="current",i.resumeToken)),n.isPrimaryClient&&t&&$m(n.remoteStore,i),c})}function zw(n,e,t,r,i){return g(this,null,function*(){n.Ru=(_,I,R)=>function(O,D,q,L){return g(this,null,function*(){let F=D.view.Za(q);F.Cs&&(F=yield bp(O.localStore,D.query,!1).then(({documents:T})=>D.view.Za(T,F)));let z=L&&L.targetChanges.get(D.targetId),ue=L&&L.targetMismatches.get(D.targetId)!=null,Q=D.view.applyChanges(F,O.isPrimaryClient,z,ue);return Np(O,D.targetId,Q.ru),Q.snapshot})}(n,_,I,R);let s=yield bp(n.localStore,e,!0),a=new rl(e,s.$s),c=a.Za(s.documents),l=ai.createSynthesizedTargetChangeForCurrentChange(t,r&&n.onlineState!=="Offline",i),d=a.applyChanges(c,n.isPrimaryClient,l);Np(n,t,d.ru);let p=new il(e,t,a);return n.uu.set(e,p),n.cu.has(t)?n.cu.get(t).push(e):n.cu.set(t,[e]),d.snapshot})}function Ww(n,e,t){return g(this,null,function*(){let r=$(n),i=r.uu.get(e),s=r.cu.get(i.targetId);if(s.length>1)return r.cu.set(i.targetId,s.filter(a=>!yo(a,e))),void r.uu.delete(e);r.isPrimaryClient?(r.sharedClientState.removeLocalQueryTarget(i.targetId),r.sharedClientState.isActiveQueryTarget(i.targetId)||(yield Bu(r.localStore,i.targetId,!1).then(()=>{r.sharedClientState.clearQueryState(i.targetId),t&&xl(r.remoteStore,i.targetId),al(r,i.targetId)}).catch(ar))):(al(r,i.targetId),yield Bu(r.localStore,i.targetId,!0))})}function Gw(n,e){return g(this,null,function*(){let t=$(n),r=t.uu.get(e),i=t.cu.get(r.targetId);t.isPrimaryClient&&i.length===1&&(t.sharedClientState.removeLocalQueryTarget(r.targetId),xl(t.remoteStore,r.targetId))})}function Hw(n,e,t){return g(this,null,function*(){let r=eA(n);try{let i=yield function(a,c){let l=$(a),d=ve.now(),p=c.reduce((R,k)=>R.add(k.key),H()),_,I;return l.persistence.runTransaction("Locally write mutations","readwrite",R=>{let k=Ct(),O=H();return l.Bs.getEntries(R,p).next(D=>{k=D,k.forEach((q,L)=>{L.isValidDocument()||(O=O.add(q))})}).next(()=>l.localDocuments.getOverlayedDocuments(R,k)).next(D=>{_=D;let q=[];for(let L of c){let F=XT(L,_.get(L.key).overlayedDocument);F!=null&&q.push(new lt(L.key,F,dm(F.value.mapValue),at.exists(!0)))}return l.mutationQueue.addMutationBatch(R,d,q,c)}).next(D=>{I=D;let q=D.applyToLocalDocumentSet(_,O);return l.documentOverlayCache.saveOverlays(R,D.batchId,q)})}).then(()=>({batchId:I.batchId,changes:Tm(_)}))}(r.localStore,e);r.sharedClientState.addPendingMutation(i.batchId),function(a,c,l){let d=a.Iu[a.currentUser.toKey()];d||(d=new oe(W)),d=d.insert(c,l),a.Iu[a.currentUser.toKey()]=d}(r,i.batchId,t),yield _i(r,i.changes),yield To(r.remoteStore)}catch(i){let s=Fl(i,"Failed to persist write");t.reject(s)}})}function Jm(n,e){return g(this,null,function*(){let t=$(n);try{let r=yield ww(t.localStore,e);e.targetChanges.forEach((i,s)=>{let a=t.Pu.get(s);a&&(X(i.addedDocuments.size+i.modifiedDocuments.size+i.removedDocuments.size<=1,22616),i.addedDocuments.size>0?a._u=!0:i.modifiedDocuments.size>0?X(a._u,14607):i.removedDocuments.size>0&&(X(a._u,42227),a._u=!1))}),yield _i(t,r,e)}catch(r){yield ar(r)}})}function Dp(n,e,t){let r=$(n);if(r.isPrimaryClient&&t===0||!r.isPrimaryClient&&t===1){let i=[];r.uu.forEach((s,a)=>{let c=a.view.Da(e);c.snapshot&&i.push(c.snapshot)}),function(a,c){let l=$(a);l.onlineState=c;let d=!1;l.queries.forEach((p,_)=>{for(let I of _.ya)I.Da(c)&&(d=!0)}),d&&Ul(l)}(r.eventManager,e),i.length&&r.au.j_(i),r.onlineState=e,r.isPrimaryClient&&r.sharedClientState.setOnlineState(e)}}function Kw(n,e,t){return g(this,null,function*(){let r=$(n);r.sharedClientState.updateQueryState(e,"rejected",t);let i=r.Pu.get(e),s=i&&i.key;if(s){let a=new oe(M.comparator);a=a.insert(s,Ge.newNoDocument(s,j.min()));let c=H().add(s),l=new qs(j.min(),new Map,new oe(W),a,c);yield Jm(r,l),r.hu=r.hu.remove(s),r.Pu.delete(e),ql(r)}else yield Bu(r.localStore,e,!1).then(()=>al(r,e,t)).catch(ar)})}function Qw(n,e){return g(this,null,function*(){let t=$(n),r=e.batch.batchId;try{let i=yield Tw(t.localStore,e);Zm(t,r,null),Xm(t,r),t.sharedClientState.updateMutationState(r,"acknowledged"),yield _i(t,i)}catch(i){yield ar(i)}})}function Yw(n,e,t){return g(this,null,function*(){let r=$(n);try{let i=yield function(a,c){let l=$(a);return l.persistence.runTransaction("Reject batch","readwrite-primary",d=>{let p;return l.mutationQueue.lookupMutationBatch(d,c).next(_=>(X(_!==null,37113),p=_.keys(),l.mutationQueue.removeMutationBatch(d,_))).next(()=>l.mutationQueue.performConsistencyCheck(d)).next(()=>l.documentOverlayCache.removeOverlaysForBatchId(d,p,c)).next(()=>l.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(d,p)).next(()=>l.localDocuments.getDocuments(d,p))})}(r.localStore,e);Zm(r,e,t),Xm(r,e),r.sharedClientState.updateMutationState(e,"rejected",t),yield _i(r,i)}catch(i){yield ar(i)}})}function Xm(n,e){(n.Eu.get(e)||[]).forEach(t=>{t.resolve()}),n.Eu.delete(e)}function Zm(n,e,t){let r=$(n),i=r.Iu[r.currentUser.toKey()];if(i){let s=i.get(e);s&&(t?s.reject(t):s.resolve(),i=i.remove(e)),r.Iu[r.currentUser.toKey()]=i}}function al(n,e,t=null){n.sharedClientState.removeLocalQueryTarget(e);for(let r of n.cu.get(e))n.uu.delete(r),t&&n.au.Vu(r,t);n.cu.delete(e),n.isPrimaryClient&&n.Tu.Hr(e).forEach(r=>{n.Tu.containsKey(r)||eg(n,r)})}function eg(n,e){n.lu.delete(e.path.canonicalString());let t=n.hu.get(e);t!==null&&(xl(n.remoteStore,t),n.hu=n.hu.remove(e),n.Pu.delete(t),ql(n))}function Np(n,e,t){for(let r of t)r instanceof ro?(n.Tu.addReference(r.key,e),Jw(n,r)):r instanceof io?(V(Bl,"Document no longer in limbo: "+r.key),n.Tu.removeReference(r.key,e),n.Tu.containsKey(r.key)||eg(n,r.key)):U(19791,{mu:r})}function Jw(n,e){let t=e.key,r=t.path.canonicalString();n.hu.get(t)||n.lu.has(r)||(V(Bl,"New document in limbo: "+t),n.lu.add(r),ql(n))}function ql(n){for(;n.lu.size>0&&n.hu.size<n.maxConcurrentLimboResolutions;){let e=n.lu.values().next().value;n.lu.delete(e);let t=new M(ie.fromString(e)),r=n.du.next();n.Pu.set(r,new sl(t)),n.hu=n.hu.insert(t,r),$m(n.remoteStore,new ci(ot(kl(t.path)),r,"TargetPurposeLimboResolution",Tl.le))}}function _i(n,e,t){return g(this,null,function*(){let r=$(n),i=[],s=[],a=[];r.uu.isEmpty()||(r.uu.forEach((c,l)=>{a.push(r.Ru(l,e,t).then(d=>{var p;if((d||t)&&r.isPrimaryClient){let _=d?!d.fromCache:(p=t==null?void 0:t.targetChanges.get(l.targetId))===null||p===void 0?void 0:p.current;r.sharedClientState.updateQueryState(l.targetId,_?"current":"not-current")}if(d){i.push(d);let _=Lu.Rs(l.targetId,d);s.push(_)}}))}),yield Promise.all(a),r.au.j_(i),yield function(l,d){return g(this,null,function*(){let p=$(l);try{yield p.persistence.runTransaction("notifyLocalViewChanges","readwrite",_=>C.forEach(d,I=>C.forEach(I.ds,R=>p.persistence.referenceDelegate.addReference(_,I.targetId,R)).next(()=>C.forEach(I.As,R=>p.persistence.referenceDelegate.removeReference(_,I.targetId,R)))))}catch(_){if(!cr(_))throw _;V(Ol,"Failed to update sequence numbers: "+_)}for(let _ of d){let I=_.targetId;if(!_.fromCache){let R=p.xs.get(I),k=R.snapshotVersion,O=R.withLastLimboFreeSnapshotVersion(k);p.xs=p.xs.insert(I,O)}}})}(r.localStore,s))})}function Xw(n,e){return g(this,null,function*(){let t=$(n);if(!t.currentUser.isEqual(e)){V(Bl,"User change. New user:",e.toKey());let r=yield Bm(t.localStore,e);t.currentUser=e,function(s,a){s.Eu.forEach(c=>{c.forEach(l=>{l.reject(new x(P.CANCELLED,a))})}),s.Eu.clear()}(t,"'waitForPendingWrites' promise is rejected due to a user change."),t.sharedClientState.handleUserChange(e,r.removedBatchIds,r.addedBatchIds),yield _i(t,r.ks)}})}function Zw(n,e){let t=$(n),r=t.Pu.get(e);if(r&&r._u)return H().add(r.key);{let i=H(),s=t.cu.get(e);if(!s)return i;for(let a of s){let c=t.uu.get(a);i=i.unionWith(c.view.Ya)}return i}}function tg(n){let e=$(n);return e.remoteStore.remoteSyncer.applyRemoteEvent=Jm.bind(null,e),e.remoteStore.remoteSyncer.getRemoteKeysForTarget=Zw.bind(null,e),e.remoteStore.remoteSyncer.rejectListen=Kw.bind(null,e),e.au.j_=Bw.bind(null,e.eventManager),e.au.Vu=qw.bind(null,e.eventManager),e}function eA(n){let e=$(n);return e.remoteStore.remoteSyncer.applySuccessfulWrite=Qw.bind(null,e),e.remoteStore.remoteSyncer.rejectFailedWrite=Yw.bind(null,e),e}var ng=(()=>{class n{constructor(){this.kind="memory",this.synchronizeTabs=!1}initialize(t){return g(this,null,function*(){this.serializer=vo(t.databaseInfo.databaseId),this.sharedClientState=this.pu(t),this.persistence=this.yu(t),yield this.persistence.start(),this.localStore=this.wu(t),this.gcScheduler=this.Su(t,this.localStore),this.indexBackfillerScheduler=this.bu(t,this.localStore)})}Su(t,r){return null}bu(t,r){return null}wu(t){return Ew(this.persistence,new Fu,t.initialUser,this.serializer)}yu(t){return new Hs(Vu.fi,this.serializer)}pu(t){return new qu}terminate(){return g(this,null,function*(){var t,r;(t=this.gcScheduler)===null||t===void 0||t.stop(),(r=this.indexBackfillerScheduler)===null||r===void 0||r.stop(),this.sharedClientState.shutdown(),yield this.persistence.shutdown()})}}return n.provider={build:()=>new n},n})(),cl=class extends ng{constructor(e){super(),this.cacheSizeBytes=e}Su(e,t){X(this.persistence.referenceDelegate instanceof Ks,46915);let r=this.persistence.referenceDelegate.garbageCollector;return new Tu(r,e.asyncQueue,t)}yu(e){let t=this.cacheSizeBytes!==void 0?Fe.withCacheSize(this.cacheSizeBytes):Fe.DEFAULT;return new Hs(r=>Ks.fi(r,t),this.serializer)}};var tA=(()=>{class n{initialize(t,r){return g(this,null,function*(){this.localStore||(this.localStore=t.localStore,this.sharedClientState=t.sharedClientState,this.datastore=this.createDatastore(r),this.remoteStore=this.createRemoteStore(r),this.eventManager=this.createEventManager(r),this.syncEngine=this.createSyncEngine(r,!t.synchronizeTabs),this.sharedClientState.onlineStateHandler=i=>Dp(this.syncEngine,i,1),this.remoteStore.remoteSyncer.handleCredentialChange=Xw.bind(null,this.syncEngine),yield Uw(this.remoteStore,this.syncEngine.isPrimaryClient))})}createEventManager(t){return function(){return new tl}()}createDatastore(t){let r=vo(t.databaseInfo.databaseId),i=function(a){return new Gu(a)}(t.databaseInfo);return function(a,c,l,d){return new Yu(a,c,l,d)}(t.authCredentials,t.appCheckCredentials,i,r)}createRemoteStore(t){return function(i,s,a,c,l){return new Xu(i,s,a,c,l)}(this.localStore,this.datastore,t.asyncQueue,r=>Dp(this.syncEngine,r,0),function(){return Ys.C()?new Ys:new $u}())}createSyncEngine(t,r){return function(s,a,c,l,d,p,_){let I=new ol(s,a,c,l,d,p);return _&&(I.Au=!0),I}(this.localStore,this.remoteStore,this.eventManager,this.sharedClientState,t.initialUser,t.maxConcurrentLimboResolutions,r)}terminate(){return g(this,null,function*(){var t,r;yield function(s){return g(this,null,function*(){let a=$(s);V(An,"RemoteStore shutting down."),a.Ta.add(5),yield gi(a),a.Ea.shutdown(),a.da.set("Unknown")})}(this.remoteStore),(t=this.datastore)===null||t===void 0||t.terminate(),(r=this.eventManager)===null||r===void 0||r.terminate()})}}return n.provider={build:()=>new n},n})();var so=class{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.vu(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.vu(this.observer.error,e):bt("Uncaught Error in snapshot listener:",e.toString()))}Cu(){this.muted=!0}vu(e,t){setTimeout(()=>{this.muted||e(t)},0)}};var Zt="FirestoreClient",ul=class{constructor(e,t,r,i,s){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=r,this.databaseInfo=i,this.user=pe.UNAUTHENTICATED,this.clientId=Os.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=s,this.authCredentials.start(r,a=>g(this,null,function*(){V(Zt,"Received user=",a.uid),yield this.authCredentialListener(a),this.user=a})),this.appCheckCredentials.start(r,a=>(V(Zt,"Received new app check token=",a),this.appCheckCredentialListener(a,this.user)))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();let e=new We;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(()=>g(this,null,function*(){try{this._onlineComponents&&(yield this._onlineComponents.terminate()),this._offlineComponents&&(yield this._offlineComponents.terminate()),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),e.resolve()}catch(t){let r=Fl(t,"Failed to shutdown persistence");e.reject(r)}})),e.promise}};function xc(n,e){return g(this,null,function*(){n.asyncQueue.verifyOperationInProgress(),V(Zt,"Initializing OfflineComponentProvider");let t=n.configuration;yield e.initialize(t);let r=t.initialUser;n.setCredentialChangeListener(i=>g(null,null,function*(){r.isEqual(i)||(yield Bm(e.localStore,i),r=i)})),e.persistence.setDatabaseDeletedListener(()=>n.terminate()),n._offlineComponents=e})}function Op(n,e){return g(this,null,function*(){n.asyncQueue.verifyOperationInProgress();let t=yield nA(n);V(Zt,"Initializing OnlineComponentProvider"),yield e.initialize(t,n.configuration),n.setCredentialChangeListener(r=>Pp(e.remoteStore,r)),n.setAppCheckTokenChangeListener((r,i)=>Pp(e.remoteStore,i)),n._onlineComponents=e})}function nA(n){return g(this,null,function*(){if(!n._offlineComponents)if(n._uninitializedComponentsProvider){V(Zt,"Using user provided OfflineComponentProvider");try{yield xc(n,n._uninitializedComponentsProvider._offline)}catch(e){let t=e;if(!function(i){return i.name==="FirebaseError"?i.code===P.FAILED_PRECONDITION||i.code===P.UNIMPLEMENTED:!(typeof DOMException<"u"&&i instanceof DOMException)||i.code===22||i.code===20||i.code===11}(t))throw t;Hn("Error using user provided cache. Falling back to memory cache: "+t),yield xc(n,new ng)}}else V(Zt,"Using default OfflineComponentProvider"),yield xc(n,new cl(void 0));return n._offlineComponents})}function rg(n){return g(this,null,function*(){return n._onlineComponents||(n._uninitializedComponentsProvider?(V(Zt,"Using user provided OnlineComponentProvider"),yield Op(n,n._uninitializedComponentsProvider._online)):(V(Zt,"Using default OnlineComponentProvider"),yield Op(n,new tA))),n._onlineComponents})}function rA(n){return rg(n).then(e=>e.syncEngine)}function ig(n){return g(this,null,function*(){let e=yield rg(n),t=e.eventManager;return t.onListen=$w.bind(null,e.syncEngine),t.onUnlisten=Ww.bind(null,e.syncEngine),t.onFirstRemoteStoreListen=jw.bind(null,e.syncEngine),t.onLastRemoteStoreUnlisten=Gw.bind(null,e.syncEngine),t})}function iA(n,e,t={}){let r=new We;return n.asyncQueue.enqueueAndForget(()=>g(null,null,function*(){return function(s,a,c,l,d){let p=new so({next:I=>{p.Cu(),a.enqueueAndForget(()=>Qm(s,_));let R=I.docs.has(c);!R&&I.fromCache?d.reject(new x(P.UNAVAILABLE,"Failed to get document because the client is offline.")):R&&I.fromCache&&l&&l.source==="server"?d.reject(new x(P.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):d.resolve(I)},error:I=>d.reject(I)}),_=new no(kl(c.path),p,{includeMetadataChanges:!0,La:!0});return Km(s,_)}(yield ig(n),n.asyncQueue,e,t,r)})),r.promise}function sA(n,e,t={}){let r=new We;return n.asyncQueue.enqueueAndForget(()=>g(null,null,function*(){return function(s,a,c,l,d){let p=new so({next:I=>{p.Cu(),a.enqueueAndForget(()=>Qm(s,_)),I.fromCache&&l.source==="server"?d.reject(new x(P.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):d.resolve(I)},error:I=>d.reject(I)}),_=new no(c,p,{includeMetadataChanges:!0,La:!0});return Km(s,_)}(yield ig(n),n.asyncQueue,e,t,r)})),r.promise}function sg(n){let e={};return n.timeoutSeconds!==void 0&&(e.timeoutSeconds=n.timeoutSeconds),e}var xp=new Map;function og(n,e,t){if(!t)throw new x(P.INVALID_ARGUMENT,`Function ${n}() cannot be called with an empty ${e}.`)}function oA(n,e,t,r){if(e===!0&&r===!0)throw new x(P.INVALID_ARGUMENT,`${n} and ${t} cannot be used together.`)}function Vp(n){if(!M.isDocumentKey(n))throw new x(P.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${n} has ${n.length}.`)}function Lp(n){if(M.isDocumentKey(n))throw new x(P.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${n} has ${n.length}.`)}function wo(n){if(n===void 0)return"undefined";if(n===null)return"null";if(typeof n=="string")return n.length>20&&(n=`${n.substring(0,20)}...`),JSON.stringify(n);if(typeof n=="number"||typeof n=="boolean")return""+n;if(typeof n=="object"){if(n instanceof Array)return"an array";{let e=function(r){return r.constructor?r.constructor.name:null}(n);return e?`a custom ${e} object`:"an object"}}return typeof n=="function"?"a function":U(12329,{type:typeof n})}function Ke(n,e){if("_delegate"in n&&(n=n._delegate),!(n instanceof e)){if(e.name===n.constructor.name)throw new x(P.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{let t=wo(n);throw new x(P.INVALID_ARGUMENT,`Expected type '${e.name}', but it was: ${t}`)}}return n}function aA(n,e){if(e<=0)throw new x(P.INVALID_ARGUMENT,`Function ${n}() requires a positive number, but it was: ${e}.`)}var ag="firestore.googleapis.com",Mp=!0,oo=class{constructor(e){var t,r;if(e.host===void 0){if(e.ssl!==void 0)throw new x(P.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=ag,this.ssl=Mp}else this.host=e.host,this.ssl=(t=e.ssl)!==null&&t!==void 0?t:Mp;if(this.isUsingEmulator=e.emulatorOptions!==void 0,this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,e.cacheSizeBytes===void 0)this.cacheSizeBytes=Um;else{if(e.cacheSizeBytes!==-1&&e.cacheSizeBytes<yw)throw new x(P.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}oA("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:e.experimentalAutoDetectLongPolling===void 0?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=sg((r=e.experimentalLongPollingOptions)!==null&&r!==void 0?r:{}),function(s){if(s.timeoutSeconds!==void 0){if(isNaN(s.timeoutSeconds))throw new x(P.INVALID_ARGUMENT,`invalid long polling timeout: ${s.timeoutSeconds} (must not be NaN)`);if(s.timeoutSeconds<5)throw new x(P.INVALID_ARGUMENT,`invalid long polling timeout: ${s.timeoutSeconds} (minimum allowed value is 5)`);if(s.timeoutSeconds>30)throw new x(P.INVALID_ARGUMENT,`invalid long polling timeout: ${s.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!e.useFetchStreams}isEqual(e){return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&function(r,i){return r.timeoutSeconds===i.timeoutSeconds}(this.experimentalLongPollingOptions,e.experimentalLongPollingOptions)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams}},rr=class{constructor(e,t,r,i){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=r,this._app=i,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new oo({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(!this._app)throw new x(P.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return this._terminateTask!=="notTerminated"}_setSettings(e){if(this._settingsFrozen)throw new x(P.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new oo(e),this._emulatorOptions=e.emulatorOptions||{},e.credentials!==void 0&&(this._authCredentials=function(r){if(!r)return new Vc;switch(r.type){case"firstParty":return new Uc(r.sessionIndex||"0",r.iamToken||null,r.authTokenFactory||null);case"provider":return r.client;default:throw new x(P.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}}(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return this._terminateTask==="notTerminated"&&(this._terminateTask=this._terminate()),this._terminateTask}_restart(){return g(this,null,function*(){this._terminateTask==="notTerminated"?yield this._terminate():this._terminateTask="notTerminated"})}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function(t){let r=xp.get(t);r&&(V("ComponentProvider","Removing Datastore"),xp.delete(t),r.terminate())}(this),Promise.resolve()}};function cg(n,e,t,r={}){var i;n=Ke(n,rr);let s=Je(e),a=n._getSettings(),c=Object.assign(Object.assign({},a),{emulatorOptions:n._getEmulatorOptions()}),l=`${e}:${t}`;s&&(kn(`https://${l}`),Dn("Firestore",!0)),a.host!==ag&&a.host!==l&&Hn("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");let d=Object.assign(Object.assign({},a),{host:l,ssl:s,emulatorOptions:r});if(!Xe(d,c)&&(n._setSettings(d),r.mockUserToken)){let p,_;if(typeof r.mockUserToken=="string")p=r.mockUserToken,_=pe.MOCK_USER;else{p=Yi(r.mockUserToken,(i=n._app)===null||i===void 0?void 0:i.options.projectId);let I=r.mockUserToken.sub||r.mockUserToken.user_id;if(!I)throw new x(P.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");_=new pe(I)}n._authCredentials=new Lc(new Ds(p,_))}}var en=class n{constructor(e,t,r){this.converter=t,this._query=r,this.type="query",this.firestore=e}withConverter(e){return new n(this.firestore,e,this._query)}},De=class n{constructor(e,t,r){this.converter=t,this._key=r,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new Qt(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new n(this.firestore,e,this._key)}},Qt=class n extends en{constructor(e,t,r){super(e,t,kl(r)),this._path=r,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){let e=this._path.popLast();return e.isEmpty()?null:new De(this.firestore,null,new M(e))}withConverter(e){return new n(this.firestore,e,this._path)}};function ug(n,e,...t){if(n=ee(n),og("collection","path",e),n instanceof rr){let r=ie.fromString(e,...t);return Lp(r),new Qt(n,null,r)}{if(!(n instanceof De||n instanceof Qt))throw new x(P.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let r=n._path.child(ie.fromString(e,...t));return Lp(r),new Qt(n.firestore,null,r)}}function $l(n,e,...t){if(n=ee(n),arguments.length===1&&(e=Os.newId()),og("doc","path",e),n instanceof rr){let r=ie.fromString(e,...t);return Vp(r),new De(n,null,new M(r))}{if(!(n instanceof De||n instanceof Qt))throw new x(P.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let r=n._path.child(ie.fromString(e,...t));return Vp(r),new De(n.firestore,n instanceof Qt?n.converter:null,new M(r))}}var Fp="AsyncQueue",ao=class{constructor(e=Promise.resolve()){this.zu=[],this.ju=!1,this.Hu=[],this.Ju=null,this.Yu=!1,this.Zu=!1,this.Xu=[],this.C_=new Js(this,"async_queue_retry"),this.ec=()=>{let r=Oc();r&&V(Fp,"Visibility state changed to "+r.visibilityState),this.C_.p_()},this.tc=e;let t=Oc();t&&typeof t.addEventListener=="function"&&t.addEventListener("visibilitychange",this.ec)}get isShuttingDown(){return this.ju}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.nc(),this.rc(e)}enterRestrictedMode(e){if(!this.ju){this.ju=!0,this.Zu=e||!1;let t=Oc();t&&typeof t.removeEventListener=="function"&&t.removeEventListener("visibilitychange",this.ec)}}enqueue(e){if(this.nc(),this.ju)return new Promise(()=>{});let t=new We;return this.rc(()=>this.ju&&this.Zu?Promise.resolve():(e().then(t.resolve,t.reject),t.promise)).then(()=>t.promise)}enqueueRetryable(e){this.enqueueAndForget(()=>(this.zu.push(e),this.sc()))}sc(){return g(this,null,function*(){if(this.zu.length!==0){try{yield this.zu[0](),this.zu.shift(),this.C_.reset()}catch(e){if(!cr(e))throw e;V(Fp,"Operation failed with retryable error: "+e)}this.zu.length>0&&this.C_.f_(()=>this.sc())}})}rc(e){let t=this.tc.then(()=>(this.Yu=!0,e().catch(r=>{throw this.Ju=r,this.Yu=!1,bt("INTERNAL UNHANDLED ERROR: ",Up(r)),r}).then(r=>(this.Yu=!1,r))));return this.tc=t,t}enqueueAfterDelay(e,t,r){this.nc(),this.Xu.indexOf(e)>-1&&(t=0);let i=Zu.createAndSchedule(this,e,t,r,s=>this.oc(s));return this.Hu.push(i),i}nc(){this.Ju&&U(47125,{_c:Up(this.Ju)})}verifyOperationInProgress(){}ac(){return g(this,null,function*(){let e;do e=this.tc,yield e;while(e!==this.tc)})}uc(e){for(let t of this.Hu)if(t.timerId===e)return!0;return!1}cc(e){return this.ac().then(()=>{this.Hu.sort((t,r)=>t.targetTimeMs-r.targetTimeMs);for(let t of this.Hu)if(t.skipDelay(),e!=="all"&&t.timerId===e)break;return this.ac()})}lc(e){this.Xu.push(e)}oc(e){let t=this.Hu.indexOf(e);this.Hu.splice(t,1)}};function Up(n){let e=n.message||"";return n.stack&&(e=n.stack.includes(n.message)?n.stack:n.message+`
`+n.stack),e}var kt=class extends rr{constructor(e,t,r,i){super(e,t,r,i),this.type="firestore",this._queue=new ao,this._persistenceKey=(i==null?void 0:i.name)||"[DEFAULT]"}_terminate(){return g(this,null,function*(){if(this._firestoreClient){let e=this._firestoreClient.terminate();this._queue=new ao(e),this._firestoreClient=void 0,yield e}})}};function lg(n,e){let t=typeof n=="object"?n:_t(),r=typeof n=="string"?n:e||Ms,i=Bt(t,"firestore").getImmediate({identifier:r});if(!i._initialized){let s=Qi("firestore");s&&cg(i,...s)}return i}function jl(n){if(n._terminated)throw new x(P.FAILED_PRECONDITION,"The client has already been terminated.");return n._firestoreClient||cA(n),n._firestoreClient}function cA(n){var e,t,r;let i=n._freezeSettings(),s=function(c,l,d,p){return new zc(c,l,d,p.host,p.ssl,p.experimentalForceLongPolling,p.experimentalAutoDetectLongPolling,sg(p.experimentalLongPollingOptions),p.useFetchStreams,p.isUsingEmulator)}(n._databaseId,((e=n._app)===null||e===void 0?void 0:e.options.appId)||"",n._persistenceKey,i);n._componentsProvider||!((t=i.localCache)===null||t===void 0)&&t._offlineComponentProvider&&(!((r=i.localCache)===null||r===void 0)&&r._onlineComponentProvider)&&(n._componentsProvider={_offline:i.localCache._offlineComponentProvider,_online:i.localCache._onlineComponentProvider}),n._firestoreClient=new ul(n._authCredentials,n._appCheckCredentials,n._queue,s,n._componentsProvider&&function(c){let l=c==null?void 0:c._online.build();return{_offline:c==null?void 0:c._offline.build(l),_online:l}}(n._componentsProvider))}var hi=class n{constructor(e){this._byteString=e}static fromBase64String(e){try{return new n(Se.fromBase64String(e))}catch(t){throw new x(P.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+t)}}static fromUint8Array(e){return new n(Se.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}};var ir=class{constructor(...e){for(let t=0;t<e.length;++t)if(e[t].length===0)throw new x(P.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new Ve(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}};var sr=class{constructor(e){this._methodName=e}};var di=class{constructor(e,t){if(!isFinite(e)||e<-90||e>90)throw new x(P.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||t>180)throw new x(P.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}toJSON(){return{latitude:this._lat,longitude:this._long}}_compareTo(e){return W(this._lat,e._lat)||W(this._long,e._long)}};var fi=class{constructor(e){this._values=(e||[]).map(t=>t)}toArray(){return this._values.map(e=>e)}isEqual(e){return function(r,i){if(r.length!==i.length)return!1;for(let s=0;s<r.length;++s)if(r[s]!==i[s])return!1;return!0}(this._values,e._values)}};var uA=/^__.*__$/,ll=class{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return this.fieldMask!==null?new lt(e,this.data,this.fieldMask,t,this.fieldTransforms):new wn(e,this.data,t,this.fieldTransforms)}},co=class{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return new lt(e,this.data,this.fieldMask,t,this.fieldTransforms)}};function hg(n){switch(n){case 0:case 2:case 1:return!0;case 3:case 4:return!1;default:throw U(40011,{hc:n})}}var hl=class n{constructor(e,t,r,i,s,a){this.settings=e,this.databaseId=t,this.serializer=r,this.ignoreUndefinedProperties=i,s===void 0&&this.Pc(),this.fieldTransforms=s||[],this.fieldMask=a||[]}get path(){return this.settings.path}get hc(){return this.settings.hc}Tc(e){return new n(Object.assign(Object.assign({},this.settings),e),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}Ic(e){var t;let r=(t=this.path)===null||t===void 0?void 0:t.child(e),i=this.Tc({path:r,Ec:!1});return i.dc(e),i}Ac(e){var t;let r=(t=this.path)===null||t===void 0?void 0:t.child(e),i=this.Tc({path:r,Ec:!1});return i.Pc(),i}Rc(e){return this.Tc({path:void 0,Ec:!0})}Vc(e){return lo(e,this.settings.methodName,this.settings.mc||!1,this.path,this.settings.fc)}contains(e){return this.fieldMask.find(t=>e.isPrefixOf(t))!==void 0||this.fieldTransforms.find(t=>e.isPrefixOf(t.field))!==void 0}Pc(){if(this.path)for(let e=0;e<this.path.length;e++)this.dc(this.path.get(e))}dc(e){if(e.length===0)throw this.Vc("Document fields must not be empty");if(hg(this.hc)&&uA.test(e))throw this.Vc('Document fields cannot begin and end with "__"')}},dl=class{constructor(e,t,r){this.databaseId=e,this.ignoreUndefinedProperties=t,this.serializer=r||vo(e)}gc(e,t,r,i=!1){return new hl({hc:e,methodName:t,fc:r,path:Ve.emptyPath(),Ec:!1,mc:i},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}};function Ao(n){let e=n._freezeSettings(),t=vo(n._databaseId);return new dl(n._databaseId,!!e.ignoreUndefinedProperties,t)}function dg(n,e,t,r,i,s={}){let a=n.gc(s.merge||s.mergeFields?2:0,e,t,i);zl("Data must be an object, but it was:",a,r);let c=fg(r,a),l,d;if(s.merge)l=new Ue(a.fieldMask),d=a.fieldTransforms;else if(s.mergeFields){let p=[];for(let _ of s.mergeFields){let I=pl(e,_,t);if(!a.contains(I))throw new x(P.INVALID_ARGUMENT,`Field '${I}' is specified in your field mask but missing from your input data.`);mg(p,I)||p.push(I)}l=new Ue(p),d=a.fieldTransforms.filter(_=>l.covers(_.field))}else l=null,d=a.fieldTransforms;return new ll(new xe(c),l,d)}var uo=class n extends sr{_toFieldTransform(e){if(e.hc!==2)throw e.hc===1?e.Vc(`${this._methodName}() can only appear at the top level of your update data`):e.Vc(`${this._methodName}() cannot be used with set() unless you pass {merge:true}`);return e.fieldMask.push(e.path),null}isEqual(e){return e instanceof n}};var fl=class n extends sr{_toFieldTransform(e){return new iu(e.path,new vn)}isEqual(e){return e instanceof n}};function lA(n,e,t,r){let i=n.gc(1,e,t);zl("Data must be an object, but it was:",i,r);let s=[],a=xe.empty();tn(r,(l,d)=>{let p=Wl(e,l,t);d=ee(d);let _=i.Ac(p);if(d instanceof uo)s.push(p);else{let I=yi(d,_);I!=null&&(s.push(p),a.set(p,I))}});let c=new Ue(s);return new co(a,c,i.fieldTransforms)}function hA(n,e,t,r,i,s){let a=n.gc(1,e,t),c=[pl(e,r,t)],l=[i];if(s.length%2!=0)throw new x(P.INVALID_ARGUMENT,`Function ${e}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let I=0;I<s.length;I+=2)c.push(pl(e,s[I])),l.push(s[I+1]);let d=[],p=xe.empty();for(let I=c.length-1;I>=0;--I)if(!mg(d,c[I])){let R=c[I],k=l[I];k=ee(k);let O=a.Ac(R);if(k instanceof uo)d.push(R);else{let D=yi(k,O);D!=null&&(d.push(R),p.set(R,D))}}let _=new Ue(d);return new co(p,_,a.fieldTransforms)}function dA(n,e,t,r=!1){return yi(t,n.gc(r?4:3,e))}function yi(n,e){if(pg(n=ee(n)))return zl("Unsupported field value:",e,n),fg(n,e);if(n instanceof sr)return function(r,i){if(!hg(i.hc))throw i.Vc(`${r._methodName}() can only be used with update() and set()`);if(!i.path)throw i.Vc(`${r._methodName}() is not currently supported inside arrays`);let s=r._toFieldTransform(i);s&&i.fieldTransforms.push(s)}(n,e),null;if(n===void 0&&e.ignoreUndefinedProperties)return null;if(e.path&&e.fieldMask.push(e.path),n instanceof Array){if(e.settings.Ec&&e.hc!==4)throw e.Vc("Nested arrays are not supported");return function(r,i){let s=[],a=0;for(let c of r){let l=yi(c,i.Rc(a));l==null&&(l={nullValue:"NULL_VALUE"}),s.push(l),a++}return{arrayValue:{values:s}}}(n,e)}return function(r,i){if((r=ee(r))===null)return{nullValue:"NULL_VALUE"};if(typeof r=="number")return HT(i.serializer,r);if(typeof r=="boolean")return{booleanValue:r};if(typeof r=="string")return{stringValue:r};if(r instanceof Date){let s=ve.fromDate(r);return{timestampValue:Ws(i.serializer,s)}}if(r instanceof ve){let s=new ve(r.seconds,1e3*Math.floor(r.nanoseconds/1e3));return{timestampValue:Ws(i.serializer,s)}}if(r instanceof di)return{geoPointValue:{latitude:r.latitude,longitude:r.longitude}};if(r instanceof hi)return{bytesValue:Nm(i.serializer,r._byteString)};if(r instanceof De){let s=i.databaseId,a=r.firestore._databaseId;if(!a.isEqual(s))throw i.Vc(`Document reference is for database ${a.projectId}/${a.database} but should be for database ${s.projectId}/${s.database}`);return{referenceValue:Nl(r.firestore._databaseId||i.databaseId,r._key.path)}}if(r instanceof fi)return function(a,c){return{mapValue:{fields:{[bl]:{stringValue:Rl},[Qn]:{arrayValue:{values:a.toArray().map(d=>{if(typeof d!="number")throw c.Vc("VectorValues must only contain numeric values.");return Dl(c.serializer,d)})}}}}}}(r,i);throw i.Vc(`Unsupported field value: ${wo(r)}`)}(n,e)}function fg(n,e){let t={};return im(n)?e.path&&e.path.length>0&&e.fieldMask.push(e.path):tn(n,(r,i)=>{let s=yi(i,e.Ic(r));s!=null&&(t[r]=s)}),{mapValue:{fields:t}}}function pg(n){return!(typeof n!="object"||n===null||n instanceof Array||n instanceof Date||n instanceof ve||n instanceof di||n instanceof hi||n instanceof De||n instanceof sr||n instanceof fi)}function zl(n,e,t){if(!pg(t)||!function(i){return typeof i=="object"&&i!==null&&(Object.getPrototypeOf(i)===Object.prototype||Object.getPrototypeOf(i)===null)}(t)){let r=wo(t);throw r==="an object"?e.Vc(n+" a custom object"):e.Vc(n+" "+r)}}function pl(n,e,t){if((e=ee(e))instanceof ir)return e._internalPath;if(typeof e=="string")return Wl(n,e);throw lo("Field path arguments must be of type string or ",n,!1,void 0,t)}var fA=new RegExp("[~\\*/\\[\\]]");function Wl(n,e,t){if(e.search(fA)>=0)throw lo(`Invalid field path (${e}). Paths must not contain '~', '*', '/', '[', or ']'`,n,!1,void 0,t);try{return new ir(...e.split("."))._internalPath}catch{throw lo(`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,n,!1,void 0,t)}}function lo(n,e,t,r,i){let s=r&&!r.isEmpty(),a=i!==void 0,c=`Function ${e}() called with invalid data`;t&&(c+=" (via `toFirestore()`)"),c+=". ";let l="";return(s||a)&&(l+=" (found",s&&(l+=` in field ${r}`),a&&(l+=` in document ${i}`),l+=")"),new x(P.INVALID_ARGUMENT,c+n+l)}function mg(n,e){return n.some(t=>t.isEqual(e))}var ho=class{constructor(e,t,r,i,s){this._firestore=e,this._userDataWriter=t,this._key=r,this._document=i,this._converter=s}get id(){return this._key.path.lastSegment()}get ref(){return new De(this._firestore,this._converter,this._key)}exists(){return this._document!==null}data(){if(this._document){if(this._converter){let e=new ml(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(e)}return this._userDataWriter.convertValue(this._document.data.value)}}get(e){if(this._document){let t=this._document.data.field(bo("DocumentSnapshot.get",e));if(t!==null)return this._userDataWriter.convertValue(t)}}},ml=class extends ho{data(){return super.data()}};function bo(n,e){return typeof e=="string"?Wl(n,e):e instanceof ir?e._internalPath:e._delegate._internalPath}function pA(n){if(n.limitType==="L"&&n.explicitOrderBy.length===0)throw new x(P.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}var pi=class{},mi=class extends pi{};function gg(n,e,...t){let r=[];e instanceof pi&&r.push(e),r=r.concat(t),function(s){let a=s.filter(l=>l instanceof gl).length,c=s.filter(l=>l instanceof fo).length;if(a>1||a>0&&c>0)throw new x(P.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.")}(r);for(let i of r)n=i._apply(n);return n}var fo=class n extends mi{constructor(e,t,r){super(),this._field=e,this._op=t,this._value=r,this.type="where"}static _create(e,t,r){return new n(e,t,r)}_apply(e){let t=this._parse(e);return vg(e._query,t),new en(e.firestore,e.converter,ru(e._query,t))}_parse(e){let t=Ao(e.firestore);return function(s,a,c,l,d,p,_){let I;if(d.isKeyField()){if(p==="array-contains"||p==="array-contains-any")throw new x(P.INVALID_ARGUMENT,`Invalid Query. You can't perform '${p}' queries on documentId().`);if(p==="in"||p==="not-in"){qp(_,p);let k=[];for(let O of _)k.push(Bp(l,s,O));I={arrayValue:{values:k}}}else I=Bp(l,s,_)}else p!=="in"&&p!=="not-in"&&p!=="array-contains-any"||qp(_,p),I=dA(c,a,_,p==="in"||p==="not-in");return de.create(d,p,I)}(e._query,"where",t,e.firestore._databaseId,this._field,this._op,this._value)}};function _g(n,e,t){let r=e,i=bo("where",n);return fo._create(i,r,t)}var gl=class n extends pi{constructor(e,t){super(),this.type=e,this._queryConstraints=t}static _create(e,t){return new n(e,t)}_parse(e){let t=this._queryConstraints.map(r=>r._parse(e)).filter(r=>r.getFilters().length>0);return t.length===1?t[0]:He.create(t,this._getOperator())}_apply(e){let t=this._parse(e);return t.getFilters().length===0?e:(function(i,s){let a=i,c=s.getFlattenedFilters();for(let l of c)vg(a,l),a=ru(a,l)}(e._query,t),new en(e.firestore,e.converter,ru(e._query,t)))}_getQueryConstraints(){return this._queryConstraints}_getOperator(){return this.type==="and"?"and":"or"}};var _l=class n extends mi{constructor(e,t){super(),this._field=e,this._direction=t,this.type="orderBy"}static _create(e,t){return new n(e,t)}_apply(e){let t=function(i,s,a){if(i.startAt!==null)throw new x(P.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(i.endAt!==null)throw new x(P.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new In(s,a)}(e._query,this._field,this._direction);return new en(e.firestore,e.converter,function(i,s){let a=i.explicitOrderBy.concat([s]);return new Jt(i.path,i.collectionGroup,a,i.filters.slice(),i.limit,i.limitType,i.startAt,i.endAt)}(e._query,t))}};function yg(n,e="asc"){let t=e,r=bo("orderBy",n);return _l._create(r,t)}var yl=class n extends mi{constructor(e,t,r){super(),this.type=e,this._limit=t,this._limitType=r}static _create(e,t,r){return new n(e,t,r)}_apply(e){return new en(e.firestore,e.converter,Bs(e._query,this._limit,this._limitType))}};function Ig(n){return aA("limit",n),yl._create("limit",n,"F")}function Bp(n,e,t){if(typeof(t=ee(t))=="string"){if(t==="")throw new x(P.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!ym(e)&&t.indexOf("/")!==-1)throw new x(P.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${t}' contains a '/' character.`);let r=e.path.child(ie.fromString(t));if(!M.isDocumentKey(r))throw new x(P.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${r}' is not because it has an odd number of segments (${r.length}).`);return op(n,new M(r))}if(t instanceof De)return op(n,t._key);throw new x(P.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${wo(t)}.`)}function qp(n,e){if(!Array.isArray(n)||n.length===0)throw new x(P.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${e.toString()}' filters.`)}function vg(n,e){let t=function(i,s){for(let a of i)for(let c of a.getFlattenedFilters())if(s.indexOf(c.op)>=0)return c.op;return null}(n.filters,function(i){switch(i){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}}(e.op));if(t!==null)throw t===e.op?new x(P.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${e.op.toString()}' filter.`):new x(P.INVALID_ARGUMENT,`Invalid query. You cannot use '${e.op.toString()}' filters with '${t.toString()}' filters.`)}var Il=class{convertValue(e,t="none"){switch(Yt(e)){case 0:return null;case 1:return e.booleanValue;case 2:return re(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(St(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw U(62114,{value:e})}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,t="none"){let r={};return tn(e,(i,s)=>{r[i]=this.convertValue(s,t)}),r}convertVectorValue(e){var t,r,i;let s=(i=(r=(t=e.fields)===null||t===void 0?void 0:t[Qn].arrayValue)===null||r===void 0?void 0:r.values)===null||i===void 0?void 0:i.map(a=>re(a.doubleValue));return new fi(s)}convertGeoPoint(e){return new di(re(e.latitude),re(e.longitude))}convertArray(e,t){return(e.values||[]).map(r=>this.convertValue(r,t))}convertServerTimestamp(e,t){switch(t){case"previous":let r=_o(e);return r==null?null:this.convertValue(r,t);case"estimate":return this.convertTimestamp(ii(e));default:return null}}convertTimestamp(e){let t=Rt(e);return new ve(t.seconds,t.nanos)}convertDocumentKey(e,t){let r=ie.fromString(e);X(Fm(r),9688,{name:e});let i=new Fs(r.get(1),r.get(3)),s=new M(r.popFirst(5));return i.isEqual(t)||bt(`Document ${s} contains a document reference within a different database (${i.projectId}/${i.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),s}};function Eg(n,e,t){let r;return r=n?t&&(t.merge||t.mergeFields)?n.toFirestore(e,t):n.toFirestore(e):e,r}var gn=class{constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}},po=class extends ho{constructor(e,t,r,i,s,a){super(e,t,r,i,a),this._firestore=e,this._firestoreImpl=e,this.metadata=s}exists(){return super.exists()}data(e={}){if(this._document){if(this._converter){let t=new Gn(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null);return this._converter.fromFirestore(t,e)}return this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}}get(e,t={}){if(this._document){let r=this._document.data.field(bo("DocumentSnapshot.get",e));if(r!==null)return this._userDataWriter.convertValue(r,t.serverTimestamps)}}},Gn=class extends po{data(e={}){return super.data(e)}},vl=class{constructor(e,t,r,i){this._firestore=e,this._userDataWriter=t,this._snapshot=i,this.metadata=new gn(i.hasPendingWrites,i.fromCache),this.query=r}get docs(){let e=[];return this.forEach(t=>e.push(t)),e}get size(){return this._snapshot.docs.size}get empty(){return this.size===0}forEach(e,t){this._snapshot.docs.forEach(r=>{e.call(t,new Gn(this._firestore,this._userDataWriter,r.key,r,new gn(this._snapshot.mutatedKeys.has(r.key),this._snapshot.fromCache),this.query.converter))})}docChanges(e={}){let t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new x(P.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=function(i,s){if(i._snapshot.oldDocs.isEmpty()){let a=0;return i._snapshot.docChanges.map(c=>{let l=new Gn(i._firestore,i._userDataWriter,c.doc.key,c.doc,new gn(i._snapshot.mutatedKeys.has(c.doc.key),i._snapshot.fromCache),i.query.converter);return c.doc,{type:"added",doc:l,oldIndex:-1,newIndex:a++}})}{let a=i._snapshot.oldDocs;return i._snapshot.docChanges.filter(c=>s||c.type!==3).map(c=>{let l=new Gn(i._firestore,i._userDataWriter,c.doc.key,c.doc,new gn(i._snapshot.mutatedKeys.has(c.doc.key),i._snapshot.fromCache),i.query.converter),d=-1,p=-1;return c.type!==0&&(d=a.indexOf(c.doc.key),a=a.delete(c.doc.key)),c.type!==1&&(a=a.add(c.doc),p=a.indexOf(c.doc.key)),{type:mA(c.type),doc:l,oldIndex:d,newIndex:p}})}}(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}};function mA(n){switch(n){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return U(61501,{type:n})}}function Tg(n){n=Ke(n,De);let e=Ke(n.firestore,kt);return iA(jl(e),n._key).then(t=>gA(e,n,t))}var mo=class extends Il{constructor(e){super(),this.firestore=e}convertBytes(e){return new hi(e)}convertReference(e){let t=this.convertDocumentKey(e,this.firestore._databaseId);return new De(this.firestore,null,t)}};function wg(n){n=Ke(n,en);let e=Ke(n.firestore,kt),t=jl(e),r=new mo(e);return pA(n._query),sA(t,n._query).then(i=>new vl(e,r,n,i))}function Ag(n,e,t){n=Ke(n,De);let r=Ke(n.firestore,kt),i=Eg(n.converter,e,t);return Ro(r,[dg(Ao(r),"setDoc",n._key,i,n.converter!==null,t).toMutation(n._key,at.none())])}function bg(n,e,t,...r){n=Ke(n,De);let i=Ke(n.firestore,kt),s=Ao(i),a;return a=typeof(e=ee(e))=="string"||e instanceof ir?hA(s,"updateDoc",n._key,e,t,r):lA(s,"updateDoc",n._key,e),Ro(i,[a.toMutation(n._key,at.exists(!0))])}function Rg(n){return Ro(Ke(n.firestore,kt),[new oi(n._key,at.none())])}function Sg(n,e){let t=Ke(n.firestore,kt),r=$l(n),i=Eg(n.converter,e);return Ro(t,[dg(Ao(n.firestore),"addDoc",r._key,i,n.converter!==null,{}).toMutation(r._key,at.exists(!1))]).then(()=>r)}function Ro(n,e){return function(r,i){let s=new We;return r.asyncQueue.enqueueAndForget(()=>g(null,null,function*(){return Hw(yield rA(r),i,s)})),s.promise}(jl(n),e)}function gA(n,e,t){let r=t.docs.get(e._key),i=new mo(n);return new po(n,i,e._key,r,new gn(t.hasPendingWrites,t.fromCache),e.converter)}function CS(){return new fl("serverTimestamp")}(function(e,t=!0){(function(i){or=i})(tt),Oe(new ye("firestore",(r,{instanceIdentifier:i,options:s})=>{let a=r.getProvider("app").getImmediate(),c=new kt(new Mc(r.getProvider("auth-internal")),new Bc(a,r.getProvider("app-check-internal")),function(d,p){if(!Object.prototype.hasOwnProperty.apply(d.options,["projectId"]))throw new x(P.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new Fs(d.options.projectId,p)}(a,i),a);return s=Object.assign({useFetchStreams:t},s),c._setSettings(s),c},"PUBLIC").setMultipleInstances(!0)),te(Yf,Jf,e),te(Yf,Jf,"esm2017")})();var Ii=class{constructor(e){return e}},Pg="firestore",Gl=class{constructor(){return qt(Pg)}};var Hl=new Mt("angularfire2.firestore-instances");function _A(n,e){let t=ln(Pg,n,e);return t&&new Ii(t)}function yA(n){return(e,t)=>{let r=e.runOutsideAngular(()=>n(t));return new Ii(r)}}var IA={provide:Gl,deps:[[new _e,Hl]]},vA={provide:Ii,useFactory:_A,deps:[[new _e,Hl],$e]};function NP(n,...e){return te("angularfire",nt.full,"fst"),Ft([vA,IA,{provide:Hl,useFactory:yA(n),multi:!0,deps:[Le,Ye,rt,yt,[new _e,fn],[new _e,jt],...e]}])}var OP=J(Sg,!0,2);var xP=J(ug,!0,2);var VP=J(Rg,!0,2);var LP=J($l,!0,2);var MP=J(Tg,!0);var FP=J(wg,!0);var UP=J(lg,!0);var BP=J(Ig,!0,2);var qP=J(yg,!0,2),$P=J(gg,!0,2);var jP=J(Ag,!0,2);var zP=J(bg,!0,2);var WP=J(_g,!0,2);var Vg="firebasestorage.googleapis.com",Lg="storageBucket",EA=2*60*1e3,TA=10*60*1e3;var ae=class n extends ke{constructor(e,t,r=0){super(Kl(e),`Firebase Storage: ${t} (${Kl(e)})`),this.status_=r,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,n.prototype)}get status(){return this.status_}set status(e){this.status_=e}_codeEquals(e){return Kl(e)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(e){this.customData.serverResponse=e,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}},ce=function(n){return n.UNKNOWN="unknown",n.OBJECT_NOT_FOUND="object-not-found",n.BUCKET_NOT_FOUND="bucket-not-found",n.PROJECT_NOT_FOUND="project-not-found",n.QUOTA_EXCEEDED="quota-exceeded",n.UNAUTHENTICATED="unauthenticated",n.UNAUTHORIZED="unauthorized",n.UNAUTHORIZED_APP="unauthorized-app",n.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",n.INVALID_CHECKSUM="invalid-checksum",n.CANCELED="canceled",n.INVALID_EVENT_NAME="invalid-event-name",n.INVALID_URL="invalid-url",n.INVALID_DEFAULT_BUCKET="invalid-default-bucket",n.NO_DEFAULT_BUCKET="no-default-bucket",n.CANNOT_SLICE_BLOB="cannot-slice-blob",n.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",n.NO_DOWNLOAD_URL="no-download-url",n.INVALID_ARGUMENT="invalid-argument",n.INVALID_ARGUMENT_COUNT="invalid-argument-count",n.APP_DELETED="app-deleted",n.INVALID_ROOT_OPERATION="invalid-root-operation",n.INVALID_FORMAT="invalid-format",n.INTERNAL_ERROR="internal-error",n.UNSUPPORTED_ENVIRONMENT="unsupported-environment",n}(ce||{});function Kl(n){return"storage/"+n}function eh(){let n="An unknown error occurred, please check the error payload for server response.";return new ae(ce.UNKNOWN,n)}function wA(n){return new ae(ce.OBJECT_NOT_FOUND,"Object '"+n+"' does not exist.")}function AA(n){return new ae(ce.QUOTA_EXCEEDED,"Quota for bucket '"+n+"' exceeded, please view quota on https://firebase.google.com/pricing/.")}function bA(){let n="User is not authenticated, please authenticate using Firebase Authentication and try again.";return new ae(ce.UNAUTHENTICATED,n)}function RA(){return new ae(ce.UNAUTHORIZED_APP,"This app does not have permission to access Firebase Storage on this project.")}function SA(n){return new ae(ce.UNAUTHORIZED,"User does not have permission to access '"+n+"'.")}function PA(){return new ae(ce.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function CA(){return new ae(ce.CANCELED,"User canceled the upload/download.")}function kA(n){return new ae(ce.INVALID_URL,"Invalid URL '"+n+"'.")}function DA(n){return new ae(ce.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+n+"'.")}function NA(){return new ae(ce.NO_DEFAULT_BUCKET,"No default bucket found. Did you set the '"+Lg+"' property when initializing the app?")}function OA(){return new ae(ce.CANNOT_SLICE_BLOB,"Cannot slice blob for upload. Please retry the upload.")}function xA(){return new ae(ce.NO_DOWNLOAD_URL,"The given file does not have any download URLs.")}function VA(n){return new ae(ce.UNSUPPORTED_ENVIRONMENT,`${n} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`)}function Ql(n){return new ae(ce.INVALID_ARGUMENT,n)}function Mg(){return new ae(ce.APP_DELETED,"The Firebase app was deleted.")}function LA(n){return new ae(ce.INVALID_ROOT_OPERATION,"The operation '"+n+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}function Ei(n,e){return new ae(ce.INVALID_FORMAT,"String does not match format '"+n+"': "+e)}function vi(n){throw new ae(ce.INTERNAL_ERROR,"Internal error: "+n)}var Qe=class n{constructor(e,t){this.bucket=e,this.path_=t}get path(){return this.path_}get isRoot(){return this.path.length===0}fullServerUrl(){let e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o/"+e(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(e,t){let r;try{r=n.makeFromUrl(e,t)}catch{return new n(e,"")}if(r.path==="")return r;throw DA(e)}static makeFromUrl(e,t){let r=null,i="([A-Za-z0-9.\\-_]+)";function s(z){z.path.charAt(z.path.length-1)==="/"&&(z.path_=z.path_.slice(0,-1))}let a="(/(.*))?$",c=new RegExp("^gs://"+i+a,"i"),l={bucket:1,path:3};function d(z){z.path_=decodeURIComponent(z.path)}let p="v[A-Za-z0-9_]+",_=t.replace(/[.]/g,"\\."),I="(/([^?#]*).*)?$",R=new RegExp(`^https?://${_}/${p}/b/${i}/o${I}`,"i"),k={bucket:1,path:3},O=t===Vg?"(?:storage.googleapis.com|storage.cloud.google.com)":t,D="([^?#]*)",q=new RegExp(`^https?://${O}/${i}/${D}`,"i"),F=[{regex:c,indices:l,postModify:s},{regex:R,indices:k,postModify:d},{regex:q,indices:{bucket:1,path:2},postModify:d}];for(let z=0;z<F.length;z++){let ue=F[z],Q=ue.regex.exec(e);if(Q){let T=Q[ue.indices.bucket],m=Q[ue.indices.path];m||(m=""),r=new n(T,m),ue.postModify(r);break}}if(r==null)throw kA(e);return r}},Yl=class{constructor(e){this.promise_=Promise.reject(e)}getPromise(){return this.promise_}cancel(e=!1){}};function MA(n,e,t){let r=1,i=null,s=null,a=!1,c=0;function l(){return c===2}let d=!1;function p(...D){d||(d=!0,e.apply(null,D))}function _(D){i=setTimeout(()=>{i=null,n(R,l())},D)}function I(){s&&clearTimeout(s)}function R(D,...q){if(d){I();return}if(D){I(),p.call(null,D,...q);return}if(l()||a){I(),p.call(null,D,...q);return}r<64&&(r*=2);let F;c===1?(c=2,F=0):F=(r+Math.random())*1e3,_(F)}let k=!1;function O(D){k||(k=!0,I(),!d&&(i!==null?(D||(c=2),clearTimeout(i),_(0)):D||(c=1)))}return _(0),s=setTimeout(()=>{a=!0,O(!0)},t),O}function FA(n){n(!1)}function UA(n){return n!==void 0}function BA(n){return typeof n=="object"&&!Array.isArray(n)}function th(n){return typeof n=="string"||n instanceof String}function Cg(n){return nh()&&n instanceof Blob}function nh(){return typeof Blob<"u"}function kg(n,e,t,r){if(r<e)throw Ql(`Invalid value for '${n}'. Expected ${e} or greater.`);if(r>t)throw Ql(`Invalid value for '${n}'. Expected ${t} or less.`)}function ko(n,e,t){let r=e;return t==null&&(r=`https://${e}`),`${t}://${r}/v0${n}`}function Fg(n){let e=encodeURIComponent,t="?";for(let r in n)if(n.hasOwnProperty(r)){let i=e(r)+"="+e(n[r]);t=t+i+"&"}return t=t.slice(0,-1),t}var hr=function(n){return n[n.NO_ERROR=0]="NO_ERROR",n[n.NETWORK_ERROR=1]="NETWORK_ERROR",n[n.ABORT=2]="ABORT",n}(hr||{});function qA(n,e){let t=n>=500&&n<600,i=[408,429].indexOf(n)!==-1,s=e.indexOf(n)!==-1;return t||i||s}var Jl=class{constructor(e,t,r,i,s,a,c,l,d,p,_,I=!0,R=!1){this.url_=e,this.method_=t,this.headers_=r,this.body_=i,this.successCodes_=s,this.additionalRetryCodes_=a,this.callback_=c,this.errorCallback_=l,this.timeout_=d,this.progressCallback_=p,this.connectionFactory_=_,this.retry=I,this.isUsingEmulator=R,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((k,O)=>{this.resolve_=k,this.reject_=O,this.start_()})}start_(){let e=(r,i)=>{if(i){r(!1,new lr(!1,null,!0));return}let s=this.connectionFactory_();this.pendingConnection_=s;let a=c=>{let l=c.loaded,d=c.lengthComputable?c.total:-1;this.progressCallback_!==null&&this.progressCallback_(l,d)};this.progressCallback_!==null&&s.addUploadProgressListener(a),s.send(this.url_,this.method_,this.isUsingEmulator,this.body_,this.headers_).then(()=>{this.progressCallback_!==null&&s.removeUploadProgressListener(a),this.pendingConnection_=null;let c=s.getErrorCode()===hr.NO_ERROR,l=s.getStatus();if(!c||qA(l,this.additionalRetryCodes_)&&this.retry){let p=s.getErrorCode()===hr.ABORT;r(!1,new lr(!1,null,p));return}let d=this.successCodes_.indexOf(l)!==-1;r(!0,new lr(d,s))})},t=(r,i)=>{let s=this.resolve_,a=this.reject_,c=i.connection;if(i.wasSuccessCode)try{let l=this.callback_(c,c.getResponse());UA(l)?s(l):s()}catch(l){a(l)}else if(c!==null){let l=eh();l.serverResponse=c.getErrorText(),this.errorCallback_?a(this.errorCallback_(c,l)):a(l)}else if(i.canceled){let l=this.appDelete_?Mg():CA();a(l)}else{let l=PA();a(l)}};this.canceled_?t(!1,new lr(!1,null,!0)):this.backoffId_=MA(e,t,this.timeout_)}getPromise(){return this.promise_}cancel(e){this.canceled_=!0,this.appDelete_=e||!1,this.backoffId_!==null&&FA(this.backoffId_),this.pendingConnection_!==null&&this.pendingConnection_.abort()}},lr=class{constructor(e,t,r){this.wasSuccessCode=e,this.connection=t,this.canceled=!!r}};function $A(n,e){e!==null&&e.length>0&&(n.Authorization="Firebase "+e)}function jA(n,e){n["X-Firebase-Storage-Version"]="webjs/"+(e!=null?e:"AppManager")}function zA(n,e){e&&(n["X-Firebase-GMPID"]=e)}function WA(n,e){e!==null&&(n["X-Firebase-AppCheck"]=e)}function GA(n,e,t,r,i,s,a=!0,c=!1){let l=Fg(n.urlParams),d=n.url+l,p=Object.assign({},n.headers);return zA(p,e),$A(p,t),jA(p,s),WA(p,r),new Jl(d,n.method,p,n.body,n.successCodes,n.additionalRetryCodes,n.handler,n.errorHandler,n.timeout,n.progressCallback,i,a,c)}function HA(){return typeof BlobBuilder<"u"?BlobBuilder:typeof WebKitBlobBuilder<"u"?WebKitBlobBuilder:void 0}function KA(...n){let e=HA();if(e!==void 0){let t=new e;for(let r=0;r<n.length;r++)t.append(n[r]);return t.getBlob()}else{if(nh())return new Blob(n);throw new ae(ce.UNSUPPORTED_ENVIRONMENT,"This browser doesn't seem to support creating Blobs")}}function QA(n,e,t){return n.webkitSlice?n.webkitSlice(e,t):n.mozSlice?n.mozSlice(e,t):n.slice?n.slice(e,t):null}function YA(n){if(typeof atob>"u")throw VA("base-64");return atob(n)}var ht={RAW:"raw",BASE64:"base64",BASE64URL:"base64url",DATA_URL:"data_url"},Ti=class{constructor(e,t){this.data=e,this.contentType=t||null}};function JA(n,e){switch(n){case ht.RAW:return new Ti(Ug(e));case ht.BASE64:case ht.BASE64URL:return new Ti(Bg(n,e));case ht.DATA_URL:return new Ti(ZA(e),eb(e))}throw eh()}function Ug(n){let e=[];for(let t=0;t<n.length;t++){let r=n.charCodeAt(t);if(r<=127)e.push(r);else if(r<=2047)e.push(192|r>>6,128|r&63);else if((r&64512)===55296)if(!(t<n.length-1&&(n.charCodeAt(t+1)&64512)===56320))e.push(239,191,189);else{let s=r,a=n.charCodeAt(++t);r=65536|(s&1023)<<10|a&1023,e.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|r&63)}else(r&64512)===56320?e.push(239,191,189):e.push(224|r>>12,128|r>>6&63,128|r&63)}return new Uint8Array(e)}function XA(n){let e;try{e=decodeURIComponent(n)}catch{throw Ei(ht.DATA_URL,"Malformed data URL.")}return Ug(e)}function Bg(n,e){switch(n){case ht.BASE64:{let i=e.indexOf("-")!==-1,s=e.indexOf("_")!==-1;if(i||s)throw Ei(n,"Invalid character '"+(i?"-":"_")+"' found: is it base64url encoded?");break}case ht.BASE64URL:{let i=e.indexOf("+")!==-1,s=e.indexOf("/")!==-1;if(i||s)throw Ei(n,"Invalid character '"+(i?"+":"/")+"' found: is it base64 encoded?");e=e.replace(/-/g,"+").replace(/_/g,"/");break}}let t;try{t=YA(e)}catch(i){throw i.message.includes("polyfill")?i:Ei(n,"Invalid character found")}let r=new Uint8Array(t.length);for(let i=0;i<t.length;i++)r[i]=t.charCodeAt(i);return r}var Po=class{constructor(e){this.base64=!1,this.contentType=null;let t=e.match(/^data:([^,]+)?,/);if(t===null)throw Ei(ht.DATA_URL,"Must be formatted 'data:[<mediatype>][;base64],<data>");let r=t[1]||null;r!=null&&(this.base64=tb(r,";base64"),this.contentType=this.base64?r.substring(0,r.length-7):r),this.rest=e.substring(e.indexOf(",")+1)}};function ZA(n){let e=new Po(n);return e.base64?Bg(ht.BASE64,e.rest):XA(e.rest)}function eb(n){return new Po(n).contentType}function tb(n,e){return n.length>=e.length?n.substring(n.length-e.length)===e:!1}var Co=class n{constructor(e,t){let r=0,i="";Cg(e)?(this.data_=e,r=e.size,i=e.type):e instanceof ArrayBuffer?(t?this.data_=new Uint8Array(e):(this.data_=new Uint8Array(e.byteLength),this.data_.set(new Uint8Array(e))),r=this.data_.length):e instanceof Uint8Array&&(t?this.data_=e:(this.data_=new Uint8Array(e.length),this.data_.set(e)),r=e.length),this.size_=r,this.type_=i}size(){return this.size_}type(){return this.type_}slice(e,t){if(Cg(this.data_)){let r=this.data_,i=QA(r,e,t);return i===null?null:new n(i)}else{let r=new Uint8Array(this.data_.buffer,e,t-e);return new n(r,!0)}}static getBlob(...e){if(nh()){let t=e.map(r=>r instanceof n?r.data_:r);return new n(KA.apply(null,t))}else{let t=e.map(a=>th(a)?JA(ht.RAW,a).data:a.data_),r=0;t.forEach(a=>{r+=a.byteLength});let i=new Uint8Array(r),s=0;return t.forEach(a=>{for(let c=0;c<a.length;c++)i[s++]=a[c]}),new n(i,!0)}}uploadData(){return this.data_}};function qg(n){let e;try{e=JSON.parse(n)}catch{return null}return BA(e)?e:null}function nb(n){if(n.length===0)return null;let e=n.lastIndexOf("/");return e===-1?"":n.slice(0,e)}function rb(n,e){let t=e.split("/").filter(r=>r.length>0).join("/");return n.length===0?t:n+"/"+t}function $g(n){let e=n.lastIndexOf("/",n.length-2);return e===-1?n:n.slice(e+1)}function ib(n,e){return e}var Ee=class{constructor(e,t,r,i){this.server=e,this.local=t||e,this.writable=!!r,this.xform=i||ib}},So=null;function sb(n){return!th(n)||n.length<2?n:$g(n)}function jg(){if(So)return So;let n=[];n.push(new Ee("bucket")),n.push(new Ee("generation")),n.push(new Ee("metageneration")),n.push(new Ee("name","fullPath",!0));function e(s,a){return sb(a)}let t=new Ee("name");t.xform=e,n.push(t);function r(s,a){return a!==void 0?Number(a):a}let i=new Ee("size");return i.xform=r,n.push(i),n.push(new Ee("timeCreated")),n.push(new Ee("updated")),n.push(new Ee("md5Hash",null,!0)),n.push(new Ee("cacheControl",null,!0)),n.push(new Ee("contentDisposition",null,!0)),n.push(new Ee("contentEncoding",null,!0)),n.push(new Ee("contentLanguage",null,!0)),n.push(new Ee("contentType",null,!0)),n.push(new Ee("metadata","customMetadata",!0)),So=n,So}function ob(n,e){function t(){let r=n.bucket,i=n.fullPath,s=new Qe(r,i);return e._makeStorageReference(s)}Object.defineProperty(n,"ref",{get:t})}function ab(n,e,t){let r={};r.type="file";let i=t.length;for(let s=0;s<i;s++){let a=t[s];r[a.local]=a.xform(r,e[a.server])}return ob(r,n),r}function zg(n,e,t){let r=qg(e);return r===null?null:ab(n,r,t)}function cb(n,e,t,r){let i=qg(e);if(i===null||!th(i.downloadTokens))return null;let s=i.downloadTokens;if(s.length===0)return null;let a=encodeURIComponent;return s.split(",").map(d=>{let p=n.bucket,_=n.fullPath,I="/b/"+a(p)+"/o/"+a(_),R=ko(I,t,r),k=Fg({alt:"media",token:d});return R+k})[0]}function ub(n,e){let t={},r=e.length;for(let i=0;i<r;i++){let s=e[i];s.writable&&(t[s.server]=n[s.local])}return JSON.stringify(t)}var wi=class{constructor(e,t,r,i){this.url=e,this.method=t,this.handler=r,this.timeout=i,this.urlParams={},this.headers={},this.body=null,this.errorHandler=null,this.progressCallback=null,this.successCodes=[200],this.additionalRetryCodes=[]}};function Wg(n){if(!n)throw eh()}function lb(n,e){function t(r,i){let s=zg(n,i,e);return Wg(s!==null),s}return t}function hb(n,e){function t(r,i){let s=zg(n,i,e);return Wg(s!==null),cb(s,i,n.host,n._protocol)}return t}function Gg(n){function e(t,r){let i;return t.getStatus()===401?t.getErrorText().includes("Firebase App Check token is invalid")?i=RA():i=bA():t.getStatus()===402?i=AA(n.bucket):t.getStatus()===403?i=SA(n.path):i=r,i.status=t.getStatus(),i.serverResponse=r.serverResponse,i}return e}function Hg(n){let e=Gg(n);function t(r,i){let s=e(r,i);return r.getStatus()===404&&(s=wA(n.path)),s.serverResponse=i.serverResponse,s}return t}function db(n,e,t){let r=e.fullServerUrl(),i=ko(r,n.host,n._protocol),s="GET",a=n.maxOperationRetryTime,c=new wi(i,s,hb(n,t),a);return c.errorHandler=Hg(e),c}function fb(n,e){let t=e.fullServerUrl(),r=ko(t,n.host,n._protocol),i="DELETE",s=n.maxOperationRetryTime;function a(l,d){}let c=new wi(r,i,a,s);return c.successCodes=[200,204],c.errorHandler=Hg(e),c}function pb(n,e){return n&&n.contentType||e&&e.type()||"application/octet-stream"}function mb(n,e,t){let r=Object.assign({},t);return r.fullPath=n.path,r.size=e.size(),r.contentType||(r.contentType=pb(null,e)),r}function gb(n,e,t,r,i){let s=e.bucketOnlyServerUrl(),a={"X-Goog-Upload-Protocol":"multipart"};function c(){let F="";for(let z=0;z<2;z++)F=F+Math.random().toString().slice(2);return F}let l=c();a["Content-Type"]="multipart/related; boundary="+l;let d=mb(e,r,i),p=ub(d,t),_="--"+l+`\r
Content-Type: application/json; charset=utf-8\r
\r
`+p+`\r
--`+l+`\r
Content-Type: `+d.contentType+`\r
\r
`,I=`\r
--`+l+"--",R=Co.getBlob(_,r,I);if(R===null)throw OA();let k={name:d.fullPath},O=ko(s,n.host,n._protocol),D="POST",q=n.maxUploadRetryTime,L=new wi(O,D,lb(n,t),q);return L.urlParams=k,L.headers=a,L.body=R.uploadData(),L.errorHandler=Gg(e),L}var XP=256*1024;var Dg=null,Xl=class{constructor(){this.sent_=!1,this.xhr_=new XMLHttpRequest,this.initXhr(),this.errorCode_=hr.NO_ERROR,this.sendPromise_=new Promise(e=>{this.xhr_.addEventListener("abort",()=>{this.errorCode_=hr.ABORT,e()}),this.xhr_.addEventListener("error",()=>{this.errorCode_=hr.NETWORK_ERROR,e()}),this.xhr_.addEventListener("load",()=>{e()})})}send(e,t,r,i,s){if(this.sent_)throw vi("cannot .send() more than once");if(Je(e)&&r&&(this.xhr_.withCredentials=!0),this.sent_=!0,this.xhr_.open(t,e,!0),s!==void 0)for(let a in s)s.hasOwnProperty(a)&&this.xhr_.setRequestHeader(a,s[a].toString());return i!==void 0?this.xhr_.send(i):this.xhr_.send(),this.sendPromise_}getErrorCode(){if(!this.sent_)throw vi("cannot .getErrorCode() before sending");return this.errorCode_}getStatus(){if(!this.sent_)throw vi("cannot .getStatus() before sending");try{return this.xhr_.status}catch{return-1}}getResponse(){if(!this.sent_)throw vi("cannot .getResponse() before sending");return this.xhr_.response}getErrorText(){if(!this.sent_)throw vi("cannot .getErrorText() before sending");return this.xhr_.statusText}abort(){this.xhr_.abort()}getResponseHeader(e){return this.xhr_.getResponseHeader(e)}addUploadProgressListener(e){this.xhr_.upload!=null&&this.xhr_.upload.addEventListener("progress",e)}removeUploadProgressListener(e){this.xhr_.upload!=null&&this.xhr_.upload.removeEventListener("progress",e)}},Zl=class extends Xl{initXhr(){this.xhr_.responseType="text"}};function rh(){return Dg?Dg():new Zl}var dr=class n{constructor(e,t){this._service=e,t instanceof Qe?this._location=t:this._location=Qe.makeFromUrl(t,e.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(e,t){return new n(e,t)}get root(){let e=new Qe(this._location.bucket,"");return this._newRef(this._service,e)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return $g(this._location.path)}get storage(){return this._service}get parent(){let e=nb(this._location.path);if(e===null)return null;let t=new Qe(this._location.bucket,e);return new n(this._service,t)}_throwIfRoot(e){if(this._location.path==="")throw LA(e)}};function _b(n,e,t){n._throwIfRoot("uploadBytes");let r=gb(n.storage,n._location,jg(),new Co(e,!0),t);return n.storage.makeRequestWithTokens(r,rh).then(i=>({metadata:i,ref:n}))}function yb(n){n._throwIfRoot("getDownloadURL");let e=db(n.storage,n._location,jg());return n.storage.makeRequestWithTokens(e,rh).then(t=>{if(t===null)throw xA();return t})}function Ib(n){n._throwIfRoot("deleteObject");let e=fb(n.storage,n._location);return n.storage.makeRequestWithTokens(e,rh)}function vb(n,e){let t=rb(n._location.path,e),r=new Qe(n._location.bucket,t);return new dr(n.storage,r)}function Eb(n){return/^[A-Za-z]+:\/\//.test(n)}function Tb(n,e){return new dr(n,e)}function Kg(n,e){if(n instanceof Ai){let t=n;if(t._bucket==null)throw NA();let r=new dr(t,t._bucket);return e!=null?Kg(r,e):r}else return e!==void 0?vb(n,e):n}function wb(n,e){if(e&&Eb(e)){if(n instanceof Ai)return Tb(n,e);throw Ql("To use ref(service, url), the first argument must be a Storage instance.")}else return Kg(n,e)}function Ng(n,e){let t=e==null?void 0:e[Lg];return t==null?null:Qe.makeFromBucketSpec(t,n)}function Ab(n,e,t,r={}){n.host=`${e}:${t}`;let i=Je(e);i&&(kn(`https://${n.host}`),Dn("Storage",!0)),n._isUsingEmulator=!0,n._protocol=i?"https":"http";let{mockUserToken:s}=r;s&&(n._overrideAuthToken=typeof s=="string"?s:Yi(s,n.app.options.projectId))}var Ai=class{constructor(e,t,r,i,s,a=!1){this.app=e,this._authProvider=t,this._appCheckProvider=r,this._url=i,this._firebaseVersion=s,this._isUsingEmulator=a,this._bucket=null,this._host=Vg,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=EA,this._maxUploadRetryTime=TA,this._requests=new Set,i!=null?this._bucket=Qe.makeFromBucketSpec(i,this._host):this._bucket=Ng(this._host,this.app.options)}get host(){return this._host}set host(e){this._host=e,this._url!=null?this._bucket=Qe.makeFromBucketSpec(this._url,e):this._bucket=Ng(e,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(e){kg("time",0,Number.POSITIVE_INFINITY,e),this._maxUploadRetryTime=e}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(e){kg("time",0,Number.POSITIVE_INFINITY,e),this._maxOperationRetryTime=e}_getAuthToken(){return g(this,null,function*(){if(this._overrideAuthToken)return this._overrideAuthToken;let e=this._authProvider.getImmediate({optional:!0});if(e){let t=yield e.getToken();if(t!==null)return t.accessToken}return null})}_getAppCheckToken(){return g(this,null,function*(){if(Ie(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let e=this._appCheckProvider.getImmediate({optional:!0});return e?(yield e.getToken()).token:null})}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(e=>e.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(e){return new dr(this,e)}_makeRequest(e,t,r,i,s=!0){if(this._deleted)return new Yl(Mg());{let a=GA(e,this._appId,r,i,t,this._firebaseVersion,s,this._isUsingEmulator);return this._requests.add(a),a.getPromise().then(()=>this._requests.delete(a),()=>this._requests.delete(a)),a}}makeRequestWithTokens(e,t){return g(this,null,function*(){let[r,i]=yield Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(e,t,r,i).getPromise()})}},Og="@firebase/storage",xg="0.13.12";var Qg="storage";function Yg(n,e,t){return n=ee(n),_b(n,e,t)}function Jg(n){return n=ee(n),yb(n)}function Xg(n){return n=ee(n),Ib(n)}function Zg(n,e){return n=ee(n),wb(n,e)}function e_(n=_t(),e){n=ee(n);let r=Bt(n,Qg).getImmediate({identifier:e}),i=Qi("storage");return i&&t_(r,...i),r}function t_(n,e,t,r={}){Ab(n,e,t,r)}function bb(n,{instanceIdentifier:e}){let t=n.getProvider("app").getImmediate(),r=n.getProvider("auth-internal"),i=n.getProvider("app-check-internal");return new Ai(t,r,i,e,tt)}function Rb(){Oe(new ye(Qg,bb,"PUBLIC").setMultipleInstances(!0)),te(Og,xg,""),te(Og,xg,"esm2017")}Rb();var bi=class{constructor(e){return e}},n_="storage",ih=class{constructor(){return qt(n_)}};var sh=new Mt("angularfire2.storage-instances");function Sb(n,e){let t=ln(n_,n,e);return t&&new bi(t)}function Pb(n){return(e,t)=>{let r=e.runOutsideAngular(()=>n(t));return new bi(r)}}var Cb={provide:ih,deps:[[new _e,sh]]},kb={provide:bi,useFactory:Sb,deps:[[new _e,sh],$e]};function vC(n,...e){return te("angularfire",nt.full,"gcs"),Ft([kb,Cb,{provide:sh,useFactory:Pb(n),multi:!0,deps:[Le,Ye,rt,yt,[new _e,fn],[new _e,jt],...e]}])}var EC=J(Xg,!0,2);var TC=J(Jg,!0);var wC=J(e_,!0);var AC=J(Zg,!0,2);var bC=J(Yg,!0);export{Qi as a,Je as b,kn as c,Dn as d,ke as e,ee as f,ye as g,Oe as h,Bt as i,Ie as j,_t as k,te as l,nt as m,ln as n,qt as o,rt as p,J as q,$e as r,yt as s,F0 as t,U0 as u,jt as v,dv as w,Qr as x,fn as y,nS as z,rS as A,iS as B,sS as C,oS as D,aS as E,cS as F,ve as G,CS as H,Ii as I,NP as J,OP as K,xP as L,VP as M,LP as N,MP as O,FP as P,UP as Q,BP as R,qP as S,$P as T,jP as U,zP as V,WP as W,bi as X,vC as Y,EC as Z,TC as _,wC as $,AC as aa,bC as ba};
