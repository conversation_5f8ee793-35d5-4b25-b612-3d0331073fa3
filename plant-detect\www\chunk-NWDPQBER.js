import{a as v,k as C}from"./chunk-AWIL3PGF.js";import{C as c,E as l,G as a,I as t,J as o,K as m,Ka as s,La as d,O as i,Qa as u,Ua as f,ga as p,hb as x,ib as I,qb as g}from"./chunk-AZEIYKMX.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import"./chunk-OLRFWS6T.js";var D=(()=>{let e=class e{constructor(){v({construct:C})}};e.\u0275fac=function(n){return new(n||e)},e.\u0275cmp=l({type:e,selectors:[["app-results"]],decls:14,vars:2,consts:[[3,"translucent"],[3,"fullscreen"],[2,"padding","20px"],[2,"text-align","center","padding","40px 20px"],["name","construct","size","large","color","primary"]],template:function(n,S){n&1&&(t(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),i(3,"Results"),o()()(),t(4,"ion-content",1)(5,"div",2)(6,"ion-card")(7,"ion-card-content")(8,"div",3),m(9,"ion-icon",4),t(10,"h2"),i(11,"Coming Soon"),o(),t(12,"p"),i(13,"Results page is under development."),o()()()()()()),n&2&&(a("translucent",!0),c(4),a("fullscreen",!0))},dependencies:[p,u,f,x,I,s,d,g],encapsulation:2});let r=e;return r})();export{D as ResultsPage};
