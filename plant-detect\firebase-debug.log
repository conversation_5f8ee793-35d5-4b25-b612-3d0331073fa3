[debug] [2025-05-29T15:09:31.378Z] ----------------------------------------------------------------------
[debug] [2025-05-29T15:09:31.380Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy
[debug] [2025-05-29T15:09:31.381Z] CLI Version:   14.4.0
[debug] [2025-05-29T15:09:31.381Z] Platform:      win32
[debug] [2025-05-29T15:09:31.381Z] Node Version:  v22.14.0
[debug] [2025-05-29T15:09:31.381Z] Time:          Thu May 29 2025 20:39:31 GMT+0530 (India Standard Time)
[debug] [2025-05-29T15:09:31.381Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-29T15:09:31.503Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-29T15:09:31.503Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-29T15:09:31.504Z] [iam] checking project plant-detect-edaf6 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update","firebaserules.releases.create","firebaserules.releases.update","firebaserules.rulesets.create"]
[debug] [2025-05-29T15:09:31.504Z] Checked if tokens are valid: false, expires at: 1748532238329
[debug] [2025-05-29T15:09:31.504Z] Checked if tokens are valid: false, expires at: 1748532238329
[debug] [2025-05-29T15:09:31.505Z] > refreshing access token with scopes: []
[debug] [2025-05-29T15:09:31.506Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-29T15:09:31.506Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-29T15:09:31.739Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-29T15:09:31.739Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-29T15:09:31.744Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions [none]
[debug] [2025-05-29T15:09:31.744Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:31.745Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update","firebaserules.releases.create","firebaserules.releases.update","firebaserules.rulesets.create"]}
[debug] [2025-05-29T15:09:32.975Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions 200
[debug] [2025-05-29T15:09:32.975Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update","firebaserules.releases.create","firebaserules.releases.update","firebaserules.rulesets.create"]}
[debug] [2025-05-29T15:09:32.976Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:32.976Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:32.976Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-05-29T15:09:32.976Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-05-29T15:09:34.355Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions 404
[debug] [2025-05-29T15:09:34.355Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions {"error":{"code":404,"message":"Unknown service account","status":"NOT_FOUND"}}
[debug] [2025-05-29T15:09:34.355Z] [functions] service account IAM check errored, deploy may fail: Request to https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions had HTTP Error: 404, Unknown service account {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":404,"message":"Unknown service account","status":"NOT_FOUND"}},"response":{"statusCode":404}},"exit":1,"message":"Request to https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions had HTTP Error: 404, Unknown service account","status":404}
[debug] [2025-05-29T15:09:34.356Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:34.356Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:34.356Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6 [none]
[debug] [2025-05-29T15:09:34.796Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6 200
[debug] [2025-05-29T15:09:34.796Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6 {"projectId":"plant-detect-edaf6","projectNumber":"*************","displayName":"Plant Detect","name":"projects/plant-detect-edaf6","resources":{"hostingSite":"plant-detect-edaf6"},"state":"ACTIVE","etag":"1_15fbb1a7-d9c5-4998-a469-dbef24d12ffb"}
[info] 
[info] === Deploying to 'plant-detect-edaf6'...
[info] 
[info] i  deploying storage, firestore, functions, hosting 
[info] Running command: npm --prefix "$RESOURCE_DIR" run build
[info] +  functions: Finished running predeploy script. 
[info] i  storage: ensuring required API firebasestorage.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:38.968Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:38.968Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:38.968Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebasestorage.googleapis.com [none]
[debug] [2025-05-29T15:09:38.968Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebasestorage.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:40.397Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebasestorage.googleapis.com 200
[debug] [2025-05-29T15:09:40.397Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebasestorage.googleapis.com [omitted]
[info] +  storage: required API firebasestorage.googleapis.com is enabled 
[debug] [2025-05-29T15:09:40.397Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:40.398Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:40.398Z] >>> [apiv2][query] GET https://firebasestorage.googleapis.com/v1alpha/projects/plant-detect-edaf6/defaultBucket [none]
[debug] [2025-05-29T15:09:41.021Z] <<< [apiv2][status] GET https://firebasestorage.googleapis.com/v1alpha/projects/plant-detect-edaf6/defaultBucket 200
[debug] [2025-05-29T15:09:41.021Z] <<< [apiv2][body] GET https://firebasestorage.googleapis.com/v1alpha/projects/plant-detect-edaf6/defaultBucket {"name":"projects/plant-detect-edaf6/defaultBucket","location":"US-CENTRAL1","bucket":{"name":"projects/plant-detect-edaf6/buckets/plant-detect-edaf6.firebasestorage.app"},"storageClass":"REGIONAL"}
[info] i  firebase.storage: checking storage.rules for compilation errors... 
[debug] [2025-05-29T15:09:41.024Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:41.024Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:41.024Z] >>> [apiv2][query] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test [none]
[debug] [2025-05-29T15:09:41.025Z] >>> [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test [omitted]
[debug] [2025-05-29T15:09:41.683Z] <<< [apiv2][status] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test 200
[debug] [2025-05-29T15:09:41.683Z] <<< [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test {}
[info] +  firebase.storage: rules file storage.rules compiled successfully 
[info] i  firestore: reading indexes from firestore.indexes.json... 
[info] i  cloud.firestore: checking firestore.rules for compilation errors... 
[debug] [2025-05-29T15:09:41.692Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:41.692Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:41.692Z] >>> [apiv2][query] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test [none]
[debug] [2025-05-29T15:09:41.692Z] >>> [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test [omitted]
[debug] [2025-05-29T15:09:42.942Z] <<< [apiv2][status] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test 200
[debug] [2025-05-29T15:09:42.942Z] <<< [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test {}
[info] +  cloud.firestore: rules file firestore.rules compiled successfully 
[debug] [2025-05-29T15:09:42.943Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:42.943Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:42.943Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com [none]
[debug] [2025-05-29T15:09:42.943Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:43.288Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com 200
[debug] [2025-05-29T15:09:43.288Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com [omitted]
[debug] [2025-05-29T15:09:43.289Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:43.289Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:43.289Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 [none]
[debug] [2025-05-29T15:09:44.488Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 200
[debug] [2025-05-29T15:09:44.488Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 {"projectNumber":"*************","projectId":"plant-detect-edaf6","lifecycleState":"ACTIVE","name":"Plant Detect","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-29T12:37:06.209571Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:44.492Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:44.492Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:44.492Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:44.492Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:44.493Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:44.493Z] Checked if tokens are valid: true, expires at: *************
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:44.494Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:44.494Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:44.494Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudfunctions.googleapis.com [none]
[debug] [2025-05-29T15:09:44.494Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudfunctions.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:44.495Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/runtimeconfig.googleapis.com [none]
[debug] [2025-05-29T15:09:44.495Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/runtimeconfig.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:44.497Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudbuild.googleapis.com [none]
[debug] [2025-05-29T15:09:44.497Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudbuild.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:44.499Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/artifactregistry.googleapis.com [none]
[debug] [2025-05-29T15:09:44.499Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/artifactregistry.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:44.846Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudfunctions.googleapis.com 200
[debug] [2025-05-29T15:09:44.847Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudfunctions.googleapis.com [omitted]
[info] +  functions: required API cloudfunctions.googleapis.com is enabled 
[debug] [2025-05-29T15:09:45.905Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/runtimeconfig.googleapis.com 200
[debug] [2025-05-29T15:09:45.905Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/runtimeconfig.googleapis.com [omitted]
[debug] [2025-05-29T15:09:45.927Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/artifactregistry.googleapis.com 200
[debug] [2025-05-29T15:09:45.927Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/artifactregistry.googleapis.com [omitted]
[info] +  artifactregistry: required API artifactregistry.googleapis.com is enabled 
[debug] [2025-05-29T15:09:45.994Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudbuild.googleapis.com 200
[debug] [2025-05-29T15:09:45.994Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudbuild.googleapis.com [omitted]
[info] +  functions: required API cloudbuild.googleapis.com is enabled 
[debug] [2025-05-29T15:09:45.995Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:45.995Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:45.996Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6/adminSdkConfig [none]
[debug] [2025-05-29T15:09:46.957Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6/adminSdkConfig 200
[debug] [2025-05-29T15:09:46.957Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6/adminSdkConfig {"projectId":"plant-detect-edaf6","storageBucket":"plant-detect-edaf6.firebasestorage.app"}
[debug] [2025-05-29T15:09:46.959Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:46.959Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:46.959Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/plant-detect-edaf6/configs [none]
[debug] [2025-05-29T15:09:47.455Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/plant-detect-edaf6/configs 200
[debug] [2025-05-29T15:09:47.455Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/plant-detect-edaf6/configs {}
[debug] [2025-05-29T15:09:47.458Z] Validating nodejs source
[debug] [2025-05-29T15:09:48.889Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "cors": "^2.8.5",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "form-data": "^4.0.2",
    "node-fetch": "^2.7.0"
  },
  "devDependencies": {
    "@types/node-fetch": "^2.6.12",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^4.9.0"
  },
  "private": true
}
[debug] [2025-05-29T15:09:48.890Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[debug] [2025-05-29T15:09:48.891Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-05-29T15:09:48.899Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\plant_detect\plant-detect\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8509

[debug] [2025-05-29T15:09:49.503Z] Got response from /__/functions.yaml {"endpoints":{"healthCheck":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"healthCheck"},"identifyPlant":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"identifyPlant"},"getUserStats":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"getUserStats"},"getGlobalStats":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"getGlobalStats"},"cleanupOldLogs":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"cleanupOldLogs"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] i  extensions: ensuring required API firebaseextensions.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:53.539Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:53.539Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:53.539Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebaseextensions.googleapis.com [none]
[debug] [2025-05-29T15:09:53.539Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebaseextensions.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:54.157Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebaseextensions.googleapis.com 200
[debug] [2025-05-29T15:09:54.157Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebaseextensions.googleapis.com [omitted]
[info] +  extensions: required API firebaseextensions.googleapis.com is enabled 
[debug] [2025-05-29T15:09:54.158Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-29T15:09:54.158Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-29T15:09:54.158Z] [iam] checking project plant-detect-edaf6 for permissions ["firebase.projects.get","firebaseextensions.instances.list"]
[debug] [2025-05-29T15:09:54.158Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:54.158Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:54.158Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions [none]
[debug] [2025-05-29T15:09:54.158Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:09:54.158Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-05-29T15:09:55.353Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions 200
[debug] [2025-05-29T15:09:55.353Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-05-29T15:09:55.354Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:55.354Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:55.354Z] >>> [apiv2][query] GET https://firebaseextensions.googleapis.com/v1beta/projects/plant-detect-edaf6/instances pageSize=100&pageToken=
[debug] [2025-05-29T15:09:56.698Z] <<< [apiv2][status] GET https://firebaseextensions.googleapis.com/v1beta/projects/plant-detect-edaf6/instances 200
[debug] [2025-05-29T15:09:56.698Z] <<< [apiv2][body] GET https://firebaseextensions.googleapis.com/v1beta/projects/plant-detect-edaf6/instances {}
[info] i  functions: preparing functions directory for uploading... 
[info] i  functions: packaged C:\Users\<USER>\Desktop\plant_detect\plant-detect\functions (73.37 KB) for uploading 
[debug] [2025-05-29T15:09:56.735Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:56.735Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:56.735Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/projects/plant-detect-edaf6/locations/-/functions [none]
[debug] [2025-05-29T15:09:57.574Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/projects/plant-detect-edaf6/locations/-/functions 200
[debug] [2025-05-29T15:09:57.574Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/projects/plant-detect-edaf6/locations/-/functions {}
[debug] [2025-05-29T15:09:57.574Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:57.574Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:57.574Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/plant-detect-edaf6/locations/-/functions filter=environment%3D%22GEN_2%22
[debug] [2025-05-29T15:09:58.162Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/plant-detect-edaf6/locations/-/functions 200
[debug] [2025-05-29T15:09:58.162Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/plant-detect-edaf6/locations/-/functions {"functions":[{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/cleanupOldLogs","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"cleanupOldLogs","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"cleanupOldLogs/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"cleanupOldLogs/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/cleanupoldlogs","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/cleanupOldLogs","FUNCTION_TARGET":"cleanupOldLogs","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://cleanupoldlogs-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"cleanupoldlogs-00007-koy","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:40.561600288Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/cleanupOldLogs","createTime":"2025-05-29T13:56:37.755967045Z","satisfiesPzi":true},{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/getGlobalStats","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"getGlobalStats","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"getGlobalStats/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"getGlobalStats/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/getglobalstats","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/getGlobalStats","FUNCTION_TARGET":"getGlobalStats","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://getglobalstats-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"getglobalstats-00007-sil","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:43.539187500Z","labels":{"firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef","deployment-tool":"cli-firebase","deployment-callable":"true"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/getGlobalStats","createTime":"2025-05-29T13:56:37.749685984Z","satisfiesPzi":true},{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/identifyPlant","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"identifyPlant","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"identifyPlant/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"identifyPlant/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/identifyplant","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/identifyPlant","FUNCTION_TARGET":"identifyPlant","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://identifyplant-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"identifyplant-00007-pir","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:40.446036223Z","labels":{"deployment-callable":"true","firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef","deployment-tool":"cli-firebase"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/identifyPlant","createTime":"2025-05-29T13:56:37.621062090Z","satisfiesPzi":true},{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/healthCheck","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"healthCheck","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"healthCheck/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"healthCheck/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/healthcheck","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/healthCheck","FUNCTION_TARGET":"healthCheck","LOG_EXECUTION_ID":"true"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://healthcheck-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"healthcheck-00007-tuw","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:41.273951099Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/healthCheck","createTime":"2025-05-29T13:54:08.670160908Z","satisfiesPzi":true},{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/getUserStats","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"getUserStats","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"getUserStats/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"getUserStats/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/getuserstats","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/getUserStats","FUNCTION_TARGET":"getUserStats","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://getuserstats-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"getuserstats-00007-cis","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:40.294222078Z","labels":{"firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef","deployment-tool":"cli-firebase","deployment-callable":"true"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/getUserStats","createTime":"2025-05-29T13:56:37.817385033Z","satisfiesPzi":true}]}
[info] i  functions: ensuring required API run.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:58.166Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:58.166Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API eventarc.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:58.166Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:58.166Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API pubsub.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:58.166Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:58.167Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API storage.googleapis.com is enabled... 
[debug] [2025-05-29T15:09:58.167Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:58.167Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:09:58.167Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com [none]
[debug] [2025-05-29T15:09:58.167Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com x-goog-quota-user=projects/*************
[debug] [2025-05-29T15:09:58.167Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/*************/services/eventarc.googleapis.com [none]
[debug] [2025-05-29T15:09:58.167Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/*************/services/eventarc.googleapis.com x-goog-quota-user=projects/*************
[debug] [2025-05-29T15:09:58.168Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/*************/services/pubsub.googleapis.com [none]
[debug] [2025-05-29T15:09:58.168Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/*************/services/pubsub.googleapis.com x-goog-quota-user=projects/*************
[debug] [2025-05-29T15:09:58.169Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/*************/services/storage.googleapis.com [none]
[debug] [2025-05-29T15:09:58.169Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/*************/services/storage.googleapis.com x-goog-quota-user=projects/*************
[debug] [2025-05-29T15:10:00.362Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com 503
[debug] [2025-05-29T15:10:00.362Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com [omitted]
[error] 
[error] Error: Request to https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com had HTTP Error: 503, Visibility check was unavailable. Please retry the request and contact support if the problem persists
[debug] [2025-05-29T15:10:00.527Z] Error Context: {
  "body": {
    "error": {
      "code": 503,
      "message": "Visibility check was unavailable. Please retry the request and contact support if the problem persists",
      "status": "UNAVAILABLE"
    }
  },
  "response": {
    "statusCode": 503
  }
}
[debug] [2025-05-29T15:10:00.682Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/*************/services/pubsub.googleapis.com 200
[debug] [2025-05-29T15:10:00.682Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/*************/services/pubsub.googleapis.com [omitted]
[info] +  functions: required API pubsub.googleapis.com is enabled 
[debug] [2025-05-29T15:10:00.720Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/*************/services/storage.googleapis.com 200
[debug] [2025-05-29T15:10:00.720Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/*************/services/storage.googleapis.com [omitted]
[info] +  functions: required API storage.googleapis.com is enabled 
