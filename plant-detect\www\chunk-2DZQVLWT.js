import{a as U,b as $,c as x,d as M,e as V,f as y,g as j,h as G,i as H,j as J,k as q,l as N,m as B,n as W,o as X,p as Y,q as I,r as z,s as K,v as Z,y as Q}from"./chunk-YUFJ6257.js";import{n as F,q as A,r as _,x as P,z as L}from"./chunk-DVLCYDDQ.js";import{g as d}from"./chunk-OLRFWS6T.js";var le="type.googleapis.com/google.protobuf.Int64Value",fe="type.googleapis.com/google.protobuf.UInt64Value";function re(e,t){let n={};for(let r in e)e.hasOwnProperty(r)&&(n[r]=t(e[r]));return n}function k(e){if(e==null)return null;if(e instanceof Number&&(e=e.valueOf()),typeof e=="number"&&isFinite(e)||e===!0||e===!1||Object.prototype.toString.call(e)==="[object String]")return e;if(e instanceof Date)return e.toISOString();if(Array.isArray(e))return e.map(t=>k(t));if(typeof e=="function"||typeof e=="object")return re(e,t=>k(t));throw new Error("Data cannot be encoded in JSON: "+e)}function g(e){if(e==null)return e;if(e["@type"])switch(e["@type"]){case le:case fe:{let t=Number(e.value);if(isNaN(t))throw new Error("Data cannot be decoded from JSON: "+e);return t}default:throw new Error("Data cannot be decoded from JSON: "+e)}return Array.isArray(e)?e.map(t=>g(t)):typeof e=="function"||typeof e=="object"?re(e,t=>g(t)):e}var S="functions";var ee={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"},f=class e extends V{constructor(t,n,r){super(`${S}/${t}`,n||""),this.details=r,Object.setPrototypeOf(this,e.prototype)}};function de(e){if(e>=200&&e<300)return"ok";switch(e){case 0:return"internal";case 400:return"invalid-argument";case 401:return"unauthenticated";case 403:return"permission-denied";case 404:return"not-found";case 409:return"aborted";case 429:return"resource-exhausted";case 499:return"cancelled";case 500:return"internal";case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline-exceeded"}return"unknown"}function v(e,t){let n=de(e),r=n,o;try{let i=t&&t.error;if(i){let a=i.status;if(typeof a=="string"){if(!ee[a])return new f("internal","internal");n=ee[a],r=a}let s=i.message;typeof s=="string"&&(r=s),o=i.details,o!==void 0&&(o=g(o))}}catch{}return n==="ok"?null:new f(n,r,o)}var C=class{constructor(t,n,r,o){this.app=t,this.auth=null,this.messaging=null,this.appCheck=null,this.serverAppAppCheckToken=null,J(t)&&t.settings.appCheckToken&&(this.serverAppAppCheckToken=t.settings.appCheckToken),this.auth=n.getImmediate({optional:!0}),this.messaging=r.getImmediate({optional:!0}),this.auth||n.get().then(i=>this.auth=i,()=>{}),this.messaging||r.get().then(i=>this.messaging=i,()=>{}),this.appCheck||o==null||o.get().then(i=>this.appCheck=i,()=>{})}getAuthToken(){return d(this,null,function*(){if(this.auth)try{let t=yield this.auth.getToken();return t==null?void 0:t.accessToken}catch{return}})}getMessagingToken(){return d(this,null,function*(){if(!(!this.messaging||!("Notification"in self)||Notification.permission!=="granted"))try{return yield this.messaging.getToken()}catch{return}})}getAppCheckToken(t){return d(this,null,function*(){if(this.serverAppAppCheckToken)return this.serverAppAppCheckToken;if(this.appCheck){let n=t?yield this.appCheck.getLimitedUseToken():yield this.appCheck.getToken();return n.error?null:n.token}return null})}getContext(t){return d(this,null,function*(){let n=yield this.getAuthToken(),r=yield this.getMessagingToken(),o=yield this.getAppCheckToken(t);return{authToken:n,messagingToken:r,appCheckToken:o}})}};var b="us-central1",pe=/^data: (.*?)(?:\n|$)/;function me(e){let t=null;return{promise:new Promise((n,r)=>{t=setTimeout(()=>{r(new f("deadline-exceeded","deadline-exceeded"))},e)}),cancel:()=>{t&&clearTimeout(t)}}}var O=class{constructor(t,n,r,o,i=b,a=(...s)=>fetch(...s)){this.app=t,this.fetchImpl=a,this.emulatorOrigin=null,this.contextProvider=new C(t,n,r,o),this.cancelAllRequests=new Promise(s=>{this.deleteService=()=>Promise.resolve(s())});try{let s=new URL(i);this.customDomain=s.origin+(s.pathname==="/"?"":s.pathname),this.region=b}catch{this.customDomain=null,this.region=i}}_delete(){return this.deleteService()}_url(t){let n=this.app.options.projectId;return this.emulatorOrigin!==null?`${this.emulatorOrigin}/${n}/${this.region}/${t}`:this.customDomain!==null?`${this.customDomain}/${t}`:`https://${this.region}-${n}.cloudfunctions.net/${t}`}};function he(e,t,n){let r=$(t);e.emulatorOrigin=`http${r?"s":""}://${t}:${n}`,r&&(x(e.emulatorOrigin),M("Functions",!0))}function ge(e,t,n){let r=o=>Ne(e,t,o,n||{});return r.stream=(o,i)=>Te(e,t,o,i),r}function Ae(e,t,n,r){return d(this,null,function*(){n["Content-Type"]="application/json";let o;try{o=yield r(e,{method:"POST",body:JSON.stringify(t),headers:n})}catch{return{status:0,json:null}}let i=null;try{i=yield o.json()}catch{}return{status:o.status,json:i}})}function oe(e,t){return d(this,null,function*(){let n={},r=yield e.contextProvider.getContext(t.limitedUseAppCheckTokens);return r.authToken&&(n.Authorization="Bearer "+r.authToken),r.messagingToken&&(n["Firebase-Instance-ID-Token"]=r.messagingToken),r.appCheckToken!==null&&(n["X-Firebase-AppCheck"]=r.appCheckToken),n})}function Ne(e,t,n,r){let o=e._url(t);return Ee(e,o,n,r)}function Ee(e,t,n,r){return d(this,null,function*(){n=k(n);let o={data:n},i=yield oe(e,r),a=r.timeout||7e4,s=me(a),l=yield Promise.race([Ae(t,o,i,e.fetchImpl),s.promise,e.cancelAllRequests]);if(s.cancel(),!l)throw new f("cancelled","Firebase Functions instance was deleted.");let u=v(l.status,l.json);if(u)throw u;if(!l.json)throw new f("internal","Response is not valid JSON object.");let c=l.json.data;if(typeof c>"u"&&(c=l.json.result),typeof c>"u")throw new f("internal","Response is missing data field.");return{data:g(c)}})}function Te(e,t,n,r){let o=e._url(t);return we(e,o,n,r||{})}function we(e,t,n,r){return d(this,null,function*(){var o;n=k(n);let i={data:n},a=yield oe(e,r);a["Content-Type"]="application/json",a.Accept="text/event-stream";let s;try{s=yield e.fetchImpl(t,{method:"POST",body:JSON.stringify(i),headers:a,signal:r==null?void 0:r.signal})}catch(p){if(p instanceof Error&&p.name==="AbortError"){let w=new f("cancelled","Request was cancelled.");return{data:Promise.reject(w),stream:{[Symbol.asyncIterator](){return{next(){return Promise.reject(w)}}}}}}let T=v(0,null);return{data:Promise.reject(T),stream:{[Symbol.asyncIterator](){return{next(){return Promise.reject(T)}}}}}}let l,u,c=new Promise((p,T)=>{l=p,u=T});(o=r==null?void 0:r.signal)===null||o===void 0||o.addEventListener("abort",()=>{let p=new f("cancelled","Request was cancelled.");u(p)});let m=s.body.getReader(),h=ye(m,l,u,r==null?void 0:r.signal);return{stream:{[Symbol.asyncIterator](){let p=h.getReader();return{next(){return d(this,null,function*(){let{value:w,done:ue}=yield p.read();return{value:w,done:ue}})},return(){return d(this,null,function*(){return yield p.cancel(),{done:!0,value:void 0}})}}}},data:c}})}function ye(e,t,n,r){let o=(a,s)=>{let l=a.match(pe);if(!l)return;let u=l[1];try{let c=JSON.parse(u);if("result"in c){t(g(c.result));return}if("message"in c){s.enqueue(g(c.message));return}if("error"in c){let m=v(0,c);s.error(m),n(m);return}}catch(c){if(c instanceof f){s.error(c),n(c);return}}},i=new TextDecoder;return new ReadableStream({start(a){let s="";return l();function l(){return d(this,null,function*(){if(r!=null&&r.aborted){let u=new f("cancelled","Request was cancelled");return a.error(u),n(u),Promise.resolve()}try{let{value:u,done:c}=yield e.read();if(c){s.trim()&&o(s.trim(),a),a.close();return}if(r!=null&&r.aborted){let h=new f("cancelled","Request was cancelled");a.error(h),n(h),yield e.cancel();return}s+=i.decode(u,{stream:!0});let m=s.split(`
`);s=m.pop()||"";for(let h of m)h.trim()&&o(h.trim(),a);return l()}catch(u){let c=u instanceof f?u:v(0,null);a.error(c),n(c)}})}},cancel(){return e.cancel()}})}var te="@firebase/functions",ne="0.12.8";var ke="auth-internal",ve="app-check-internal",Ie="messaging-internal";function Ce(e){let t=(n,{instanceIdentifier:r})=>{let o=n.getProvider("app").getImmediate(),i=n.getProvider(ke),a=n.getProvider(Ie),s=n.getProvider(ve);return new O(o,i,a,s,r)};G(new j(S,t,"PUBLIC").setMultipleInstances(!0)),N(te,ne,e),N(te,ne,"esm2017")}function se(e=q(),t=b){let r=H(y(e),S).getImmediate({identifier:t}),o=U("functions");return o&&ie(r,...o),r}function ie(e,t,n){he(y(e),t,n)}function ae(e,t,n){return ge(y(e),t,n)}Ce();var E=class{constructor(t){return t}},ce="functions",D=class{constructor(){return X(ce)}};var R=new F("angularfire2.functions-instances");function be(e,t){let n=W(ce,e,t);return n&&new E(n)}function Oe(e){return(t,n)=>{let r=t.runOutsideAngular(()=>e(n));return new E(r)}}var Se={provide:D,deps:[[new A,R]]},De={provide:E,useFactory:be,deps:[[new A,R],z]};function Xe(e,...t){return N("angularfire",B.full,"fn"),_([De,Se,{provide:R,useFactory:Oe(e),multi:!0,deps:[L,P,Y,K,[new A,Q],[new A,Z],...t]}])}var Ye=I(se,!0),ze=I(ae,!0);export{E as a,Xe as b,Ye as c,ze as d};
