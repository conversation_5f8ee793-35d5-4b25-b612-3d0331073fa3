import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, from, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Functions, httpsCallable } from '@angular/fire/functions';
import {
  IdentificationRequest,
  IdentificationResult,
  PlantNetResponse,
  SpeciesInfo,
  ImageAnalysis,
  IdentificationHistory,
  LoadingState
} from '../../models';
import { environment } from '../../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class IdentificationService {
  private readonly PLANTNET_API_URL = environment.production
    ? 'https://my-api.plantnet.org/v1/identify'
    : '/api/plantnet/v1/identify';
  private readonly SUPPORTED_PROJECTS = ['k-world-flora', 'weurope', 'useful', 'australia'];

  private loadingStateSubject = new BehaviorSubject<LoadingState>({
    loading: false,
    error: null,
    lastUpdated: null
  });

  public loadingState$ = this.loadingStateSubject.asObservable();

  private identificationHistorySubject = new BehaviorSubject<IdentificationHistory[]>([]);
  public identificationHistory$ = this.identificationHistorySubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private functions: Functions
  ) {
    this.loadIdentificationHistory();
  }

  async identifyPlant(request: IdentificationRequest): Promise<IdentificationResult> {
    this.setLoadingState(true, null);

    try {
      // Validate request
      this.validateIdentificationRequest(request);

      let result: IdentificationResult;

      if (environment.demoMode) {
        // Use demo mode for testing without API calls
        result = await this.generateDemoResult(request);
      } else {
        // Use real PlantNet API
        result = await this.callPlantNetAPI(request);
      }

      // Save to history
      await this.saveToHistory(request, result);

      this.setLoadingState(false, null);
      return result;

    } catch (error: any) {
      const errorMessage = this.getErrorMessage(error);
      this.setLoadingState(false, errorMessage);
      throw new Error(errorMessage);
    }
  }

  private async callPlantNetAPI(request: IdentificationRequest): Promise<IdentificationResult> {
    try {
      // Convert image to base64
      const imageBase64 = await this.convertImageToBase64(request.imageFile);

      // Prepare Firebase Functions request
      const functionData = {
        imageBase64,
        plantPart: request.plantPart,
        floraRegion: request.floraRegion,
        location: request.location && request.modifiers?.useLocationContext ? request.location : null
      };

      // Call Firebase Function
      const identifyPlantFunction = httpsCallable(this.functions, 'identifyPlant');
      const functionResponse = await identifyPlantFunction(functionData);

      const responseData = functionResponse.data as any;
      if (!responseData || !responseData.success) {
        throw new Error('No response from identification service');
      }

      const plantNetResponse = responseData.data as PlantNetResponse;

      // Process and enhance the response
      return await this.processIdentificationResponse(plantNetResponse, request);
    } catch (error: any) {
      console.error('Firebase Functions error:', error);
      throw new Error(`Identification failed: ${error.message || 'Unknown error'}`);
    }
  }

  private async generateDemoResult(request: IdentificationRequest): Promise<IdentificationResult> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const demoSpecies = this.getDemoSpeciesByPlantPart(request.plantPart);
    const imageAnalysis = await this.analyzeImage(request.imageFile);

    const result: IdentificationResult = {
      id: this.generateId(),
      species: demoSpecies,
      processingTime: 2000,
      imageAnalysis,
      suggestions: this.generateSuggestions(demoSpecies, imageAnalysis),
      metadata: {
        apiProvider: 'PlantNet (Demo Mode)',
        modelVersion: 'demo-1.0',
        processingRegion: 'demo',
        requestId: this.generateId(),
        cached: false,
        floraRegionUsed: request.floraRegion,
        locationContextUsed: !!request.location && !!request.modifiers?.useLocationContext
      },
      createdAt: new Date()
    };

    return result;
  }

  private validateIdentificationRequest(request: IdentificationRequest): void {
    if (!request.imageFile) {
      throw new Error('Image file is required');
    }

    if (!request.plantPart) {
      throw new Error('Plant part selection is required');
    }

    if (!request.floraRegion) {
      throw new Error('Flora region selection is required');
    }

    // Validate image file
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (request.imageFile.size > maxSize) {
      throw new Error('Image file is too large. Maximum size is 10MB.');
    }

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(request.imageFile.type)) {
      throw new Error('Invalid image format. Please use JPEG, PNG, or WebP.');
    }
  }

  private async convertImageToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result.split(',')[1]); // Remove data:image/jpeg;base64, prefix
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  private getProjectFromFloraRegion(floraRegion: string): string {
    const regionProjectMap: { [key: string]: string } = {
      'global': 'k-world-flora',
      'europe': 'weurope',
      'north-america': 'useful',
      'australia': 'australia',
      'asia': 'k-world-flora',
      'tropical': 'k-world-flora'
    };

    return regionProjectMap[floraRegion] || 'k-world-flora';
  }

  private async processIdentificationResponse(
    plantNetResponse: PlantNetResponse,
    request: IdentificationRequest
  ): Promise<IdentificationResult> {
    const startTime = Date.now();

    const species: SpeciesInfo[] = plantNetResponse.results.map(result => {
      // Get the best common name or use scientific name
      const commonName = result.species.commonNames && result.species.commonNames.length > 0
        ? result.species.commonNames[0]
        : result.species.scientificNameWithoutAuthor;

      return {
        name: commonName,
        scientificName: result.species.scientificNameWithoutAuthor,
        confidence: Math.round(result.score * 100),
        taxonomy: {
          kingdom: 'Plantae',
          phylum: 'Tracheophyta',
          class: 'Magnoliopsida',
          order: 'Unknown',
          family: result.species.family?.scientificNameWithoutAuthor || 'Unknown',
          genus: result.species.genus?.scientificNameWithoutAuthor || 'Unknown',
          species: result.species.scientificNameWithoutAuthor
        },
        commonNames: result.species.commonNames || [],
        description: this.generateSpeciesDescription(result),
        habitat: this.generateHabitatInfo(result),
        conservationStatus: 'Unknown'
      };
    });

    // Analyze image (simplified version)
    const imageAnalysis: ImageAnalysis = await this.analyzeImage(request.imageFile);

    const processingTime = Date.now() - startTime;

    const result: IdentificationResult = {
      id: this.generateId(),
      species,
      processingTime,
      imageAnalysis,
      suggestions: this.generateSuggestions(species, imageAnalysis),
      metadata: {
        apiProvider: 'PlantNet',
        modelVersion: plantNetResponse.version || '1.0',
        processingRegion: 'global',
        requestId: this.generateId(),
        cached: false,
        floraRegionUsed: request.floraRegion,
        locationContextUsed: !!request.location && !!request.modifiers?.useLocationContext
      },
      createdAt: new Date()
    };

    return result;
  }

  private generateSpeciesDescription(result: any): string {
    // Generate a basic description based on available data
    const family = result.species.family?.scientificNameWithoutAuthor;
    const genus = result.species.genus?.scientificNameWithoutAuthor;

    if (family && genus) {
      return `A plant species belonging to the ${family} family, genus ${genus}.`;
    } else if (family) {
      return `A plant species from the ${family} family.`;
    }
    return 'Plant species information available.';
  }

  private generateHabitatInfo(result: any): string {
    // This could be enhanced with additional data sources
    const family = result.species.family?.scientificNameWithoutAuthor;

    // Basic habitat info based on common plant families
    const habitatMap: { [key: string]: string } = {
      'Rosaceae': 'Temperate regions, gardens, woodlands',
      'Asteraceae': 'Diverse habitats worldwide',
      'Fabaceae': 'Various habitats, often nitrogen-fixing',
      'Poaceae': 'Grasslands, meadows, cultivated areas',
      'Lamiaceae': 'Mediterranean climates, herb gardens',
      'Brassicaceae': 'Temperate regions, disturbed soils'
    };

    return habitatMap[family || ''] || 'Various natural habitats';
  }

  private getDemoSpeciesByPlantPart(plantPart: string): SpeciesInfo[] {
    const demoData: { [key: string]: SpeciesInfo[] } = {
      leaf: [
        {
          name: 'Common Oak',
          scientificName: 'Quercus robur',
          confidence: 92,
          taxonomy: {
            kingdom: 'Plantae',
            phylum: 'Tracheophyta',
            class: 'Magnoliopsida',
            order: 'Fagales',
            family: 'Fagaceae',
            genus: 'Quercus',
            species: 'Quercus robur'
          },
          commonNames: ['English Oak', 'Pedunculate Oak'],
          description: 'A large deciduous tree native to Europe, known for its distinctive lobed leaves and acorns.',
          habitat: 'Temperate forests, parks, and woodlands',
          conservationStatus: 'Least Concern'
        },
        {
          name: 'Silver Maple',
          scientificName: 'Acer saccharinum',
          confidence: 78,
          taxonomy: {
            kingdom: 'Plantae',
            phylum: 'Tracheophyta',
            class: 'Magnoliopsida',
            order: 'Sapindales',
            family: 'Sapindaceae',
            genus: 'Acer',
            species: 'Acer saccharinum'
          },
          commonNames: ['Silver Maple', 'Soft Maple'],
          description: 'A fast-growing deciduous tree with distinctive silver-backed leaves.',
          habitat: 'Wetlands, floodplains, and urban areas',
          conservationStatus: 'Least Concern'
        }
      ],
      flower: [
        {
          name: 'Common Daisy',
          scientificName: 'Bellis perennis',
          confidence: 89,
          taxonomy: {
            kingdom: 'Plantae',
            phylum: 'Tracheophyta',
            class: 'Magnoliopsida',
            order: 'Asterales',
            family: 'Asteraceae',
            genus: 'Bellis',
            species: 'Bellis perennis'
          },
          commonNames: ['English Daisy', 'Lawn Daisy'],
          description: 'A small perennial flowering plant with white petals and yellow center.',
          habitat: 'Grasslands, lawns, and meadows',
          conservationStatus: 'Least Concern'
        },
        {
          name: 'Wild Rose',
          scientificName: 'Rosa canina',
          confidence: 85,
          taxonomy: {
            kingdom: 'Plantae',
            phylum: 'Tracheophyta',
            class: 'Magnoliopsida',
            order: 'Rosales',
            family: 'Rosaceae',
            genus: 'Rosa',
            species: 'Rosa canina'
          },
          commonNames: ['Dog Rose', 'Wild Briar'],
          description: 'A climbing wild rose with pink or white flowers and red hips.',
          habitat: 'Hedgerows, woodland edges, and scrubland',
          conservationStatus: 'Least Concern'
        }
      ],
      fruit: [
        {
          name: 'Apple',
          scientificName: 'Malus domestica',
          confidence: 94,
          taxonomy: {
            kingdom: 'Plantae',
            phylum: 'Tracheophyta',
            class: 'Magnoliopsida',
            order: 'Rosales',
            family: 'Rosaceae',
            genus: 'Malus',
            species: 'Malus domestica'
          },
          commonNames: ['Domestic Apple', 'Orchard Apple'],
          description: 'The common apple fruit from cultivated apple trees.',
          habitat: 'Orchards, gardens, and cultivated areas',
          conservationStatus: 'Cultivated'
        }
      ],
      bark: [
        {
          name: 'White Birch',
          scientificName: 'Betula papyrifera',
          confidence: 87,
          taxonomy: {
            kingdom: 'Plantae',
            phylum: 'Tracheophyta',
            class: 'Magnoliopsida',
            order: 'Fagales',
            family: 'Betulaceae',
            genus: 'Betula',
            species: 'Betula papyrifera'
          },
          commonNames: ['Paper Birch', 'Canoe Birch'],
          description: 'A deciduous tree known for its distinctive white, papery bark.',
          habitat: 'Northern forests and woodlands',
          conservationStatus: 'Least Concern'
        }
      ],
      habit: [
        {
          name: 'Common Lavender',
          scientificName: 'Lavandula angustifolia',
          confidence: 91,
          taxonomy: {
            kingdom: 'Plantae',
            phylum: 'Tracheophyta',
            class: 'Magnoliopsida',
            order: 'Lamiales',
            family: 'Lamiaceae',
            genus: 'Lavandula',
            species: 'Lavandula angustifolia'
          },
          commonNames: ['English Lavender', 'True Lavender'],
          description: 'An aromatic shrub with purple flower spikes and silvery foliage.',
          habitat: 'Mediterranean climates, gardens, and hillsides',
          conservationStatus: 'Least Concern'
        }
      ],
      other: [
        {
          name: 'Unknown Plant',
          scientificName: 'Plantae species',
          confidence: 45,
          taxonomy: {
            kingdom: 'Plantae',
            phylum: 'Unknown',
            class: 'Unknown',
            order: 'Unknown',
            family: 'Unknown',
            genus: 'Unknown',
            species: 'Unknown'
          },
          commonNames: ['Unidentified Plant'],
          description: 'Plant identification requires more specific features.',
          habitat: 'Various habitats',
          conservationStatus: 'Unknown'
        }
      ]
    };

    return demoData[plantPart] || demoData['other'];
  }

  private async analyzeImage(imageFile: File): Promise<ImageAnalysis> {
    // This is a simplified version. In a real app, you might use additional APIs
    // or client-side image processing libraries for more detailed analysis

    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const analysis: ImageAnalysis = {
          quality: {
            overall: 85,
            sharpness: 80,
            lighting: 90,
            contrast: 85,
            noise: 10,
            resolution: img.width * img.height > 1000000 ? 95 : 70
          },
          plantPartDetected: ['leaf'], // Simplified detection
          features: [
            {
              type: 'leaf_shape',
              confidence: 0.8,
              description: 'Oval-shaped leaves detected'
            }
          ],
          colors: {
            dominant: ['#228B22', '#32CD32', '#006400'],
            palette: [
              { hex: '#228B22', rgb: [34, 139, 34], percentage: 45 },
              { hex: '#32CD32', rgb: [50, 205, 50], percentage: 30 },
              { hex: '#006400', rgb: [0, 100, 0], percentage: 25 }
            ],
            distribution: {
              green: 70,
              brown: 15,
              red: 5,
              yellow: 5,
              purple: 2,
              white: 2,
              other: 1
            }
          },
          composition: {
            plantCoverage: 75,
            backgroundType: 'natural',
            multipleSpecies: false,
            partialView: false
          }
        };
        resolve(analysis);
      };

      img.src = URL.createObjectURL(imageFile);
    });
  }

  private generateSuggestions(species: SpeciesInfo[], imageAnalysis: ImageAnalysis): any[] {
    const suggestions = [];

    // Image quality suggestions
    if (imageAnalysis.quality.overall < 70) {
      suggestions.push({
        type: 'image_quality',
        message: 'Consider taking a clearer photo for better identification accuracy',
        priority: 'high',
        actionable: true
      });
    }

    // Confidence suggestions
    if (species.length > 0 && species[0].confidence < 70) {
      suggestions.push({
        type: 'additional_photos',
        message: 'Try taking photos of different plant parts (flowers, leaves, bark) for better accuracy',
        priority: 'medium',
        actionable: true
      });
    }

    return suggestions;
  }

  private async saveToHistory(request: IdentificationRequest, result: IdentificationResult): Promise<void> {
    const user = this.authService.getCurrentUser();
    if (!user) return;

    const historyItem: IdentificationHistory = {
      id: this.generateId(),
      userId: user.uid,
      request,
      result,
      savedAsObservation: false,
      createdAt: new Date()
    };

    const currentHistory = this.identificationHistorySubject.value;
    const updatedHistory = [historyItem, ...currentHistory].slice(0, 50); // Keep last 50 items

    this.identificationHistorySubject.next(updatedHistory);

    // Save to local storage
    localStorage.setItem('identification_history', JSON.stringify(updatedHistory));
  }

  private loadIdentificationHistory(): void {
    try {
      const stored = localStorage.getItem('identification_history');
      if (stored) {
        const history = JSON.parse(stored) as IdentificationHistory[];
        this.identificationHistorySubject.next(history);
      }
    } catch (error) {
      console.error('Error loading identification history:', error);
    }
  }

  public clearHistory(): void {
    this.identificationHistorySubject.next([]);
    localStorage.removeItem('identification_history');
  }

  public getHistoryItem(id: string): IdentificationHistory | null {
    const history = this.identificationHistorySubject.value;
    return history.find(item => item.id === id) || null;
  }

  private setLoadingState(loading: boolean, error: string | null): void {
    this.loadingStateSubject.next({
      loading,
      error,
      lastUpdated: loading ? null : new Date()
    });
  }

  private getErrorMessage(error: any): string {
    console.error('PlantNet API Error:', error);

    if (error.status === 429) {
      return 'Rate limit exceeded. You have reached the daily limit for plant identifications. Please try again tomorrow.';
    } else if (error.status === 401 || error.status === 403) {
      return 'Invalid API key or access denied. Please check your PlantNet API configuration.';
    } else if (error.status === 400) {
      return 'Invalid request. Please check your image format and try again.';
    } else if (error.status === 404) {
      return 'PlantNet service not found. Please try again later.';
    } else if (error.status === 500) {
      return 'PlantNet server error. Please try again later.';
    } else if (error.status === 0) {
      return 'Network error. Please check your internet connection and try again.';
    } else if (error.error?.message) {
      return `PlantNet API Error: ${error.error.message}`;
    } else if (error.message) {
      return error.message;
    } else {
      return 'An unexpected error occurred during plant identification. Please try again.';
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
