{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"cors": "^2.8.5", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "form-data": "^4.0.2", "node-fetch": "^2.7.0"}, "devDependencies": {"@types/node-fetch": "^2.6.12", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}