import{a as g,b as f}from"./chunk-UUEAGYFQ.js";import{b as c,m as v,o as b}from"./chunk-AZEIYKMX.js";import{a as u,b as h,g as i}from"./chunk-OLRFWS6T.js";var m=(()=>{let o=class o{constructor(s,e){this.firebaseService=s,this.authService=e,this.userObservationsSubject=new c([]),this.publicObservationsSubject=new c([]),this.loadingStateSubject=new c({loading:!1,error:null,lastUpdated:null}),this.userObservations$=this.userObservationsSubject.asObservable(),this.publicObservations$=this.publicObservationsSubject.asObservable(),this.loadingState$=this.loadingStateSubject.asObservable(),this.currentFilter={},this.lastLoadedCount=20,this.authService.user$.subscribe(t=>{t?this.loadUserObservations():this.userObservationsSubject.next([])}),this.loadPublicObservations()}createObservation(s){return i(this,null,function*(){try{this.setLoadingState(!0,null);let e=yield this.firebaseService.createObservation(s);return yield this.loadUserObservations(),yield this.updateUserStats("create"),this.setLoadingState(!1,null),e}catch(e){throw this.setLoadingState(!1,e.message),e}})}getObservation(s){return i(this,null,function*(){try{return yield this.firebaseService.getObservation(s)}catch(e){return console.error("Error getting observation:",e),null}})}updateObservation(s,e){return i(this,null,function*(){try{this.setLoadingState(!0,null);let t=h(u({},e),{updatedAt:this.firebaseService.getTimestamp()});yield this.firebaseService.updateObservation(s,t),yield this.loadUserObservations(),e.isPublic!==void 0&&(yield this.loadPublicObservations()),this.setLoadingState(!1,null)}catch(t){throw this.setLoadingState(!1,t.message),t}})}deleteObservation(s){return i(this,null,function*(){var e;try{this.setLoadingState(!0,null);let t=yield this.firebaseService.getObservation(s);if(yield this.firebaseService.deleteObservation(s),(e=t==null?void 0:t.image)!=null&&e.url)try{yield this.firebaseService.deleteImage(t.image.url)}catch(a){console.warn("Could not delete image:",a)}yield this.loadUserObservations(),yield this.loadPublicObservations(),yield this.updateUserStats("delete"),this.setLoadingState(!1,null)}catch(t){throw this.setLoadingState(!1,t.message),t}})}loadUserObservations(s=20){return i(this,null,function*(){let e=this.authService.getCurrentUser();if(e)try{this.setLoadingState(!0,null);let t=yield this.firebaseService.getUserObservations(e.uid,s);this.userObservationsSubject.next(t),this.lastLoadedCount=t.length,this.setLoadingState(!1,null)}catch(t){this.setLoadingState(!1,t.message)}})}loadPublicObservations(s=20){return i(this,null,function*(){try{let e=yield this.firebaseService.getPublicObservations(s);this.publicObservationsSubject.next(e)}catch(e){console.error("Error loading public observations:",e)}})}loadMoreUserObservations(){return i(this,null,function*(){let e=this.userObservationsSubject.value.length+20;yield this.loadUserObservations(e)})}loadMorePublicObservations(){return i(this,null,function*(){let e=this.publicObservationsSubject.value.length+20;yield this.loadPublicObservations(e)})}searchObservations(s){return i(this,null,function*(){try{this.currentFilter=s;let e=[];s.userId?e=yield this.firebaseService.getUserObservations(s.userId,100):e=yield this.firebaseService.getPublicObservations(100);let t=this.applyFilters(e,s);return{observations:t.slice(0,20),total:t.length,hasMore:t.length>20,nextCursor:t.length>20?"20":void 0}}catch(e){throw new Error(`Search failed: ${e.message}`)}})}applyFilters(s,e){return s.filter(t=>{if(e.floraRegion&&t.floraRegion!==e.floraRegion||e.plantPart&&t.plantPart!==e.plantPart)return!1;if(e.dateRange){let a=t.createdAt instanceof Date?t.createdAt:t.createdAt.toDate();if(a<e.dateRange.start||a>e.dateRange.end)return!1}return!(e.location&&this.calculateDistance(t.location.coordinates,e.location.center)>e.location.radius||e.species&&!(t.species.name.toLowerCase().includes(e.species.toLowerCase())||t.species.scientificName.toLowerCase().includes(e.species.toLowerCase()))||e.minConfidence&&t.species.confidence<e.minConfidence||e.isPublic!==void 0&&t.isPublic!==e.isPublic)})}calculateDistance(s,e){let a=this.toRadians(e.lat-s.lat),n=this.toRadians(e.lng-s.lng),r=Math.sin(a/2)*Math.sin(a/2)+Math.cos(this.toRadians(s.lat))*Math.cos(this.toRadians(e.lat))*Math.sin(n/2)*Math.sin(n/2);return 6371*(2*Math.atan2(Math.sqrt(r),Math.sqrt(1-r)))}toRadians(s){return s*(Math.PI/180)}getObservationStats(s){return i(this,null,function*(){var t;let e=s||((t=this.authService.getCurrentUser())==null?void 0:t.uid);if(!e)return null;try{let a=yield this.firebaseService.getUserObservations(e,1e3);return{total:a.length,public:a.filter(r=>r.isPublic).length,private:a.filter(r=>!r.isPublic).length,byPlantPart:this.groupBy(a,"plantPart"),byFloraRegion:this.groupBy(a,"floraRegion"),averageConfidence:a.reduce((r,d)=>r+d.species.confidence,0)/a.length||0,mostRecentDate:a.length>0?a[0].createdAt:null,speciesCount:new Set(a.map(r=>r.species.scientificName)).size}}catch(a){return console.error("Error calculating observation stats:",a),null}})}groupBy(s,e){return s.reduce((t,a)=>{let n=a[e];return t[n]=(t[n]||0)+1,t},{})}updateUserStats(s){return i(this,null,function*(){let e=this.authService.getCurrentUser();if(e)try{let t=yield this.firebaseService.getUserDocument(e.uid);if(!t)return;let a=s==="create"?1:-1,n={stats:h(u({},t.stats),{totalObservations:Math.max(0,t.stats.totalObservations+a),lastActiveDate:this.firebaseService.getTimestamp()})};yield this.firebaseService.updateUserDocument(e.uid,n)}catch(t){console.error("Error updating user stats:",t)}})}setLoadingState(s,e){this.loadingStateSubject.next({loading:s,error:e,lastUpdated:s?null:new Date})}getUserObservationCount(){return this.userObservationsSubject.value.length}getPublicObservationCount(){return this.publicObservationsSubject.value.length}hasMoreUserObservations(){return this.lastLoadedCount>=20}refreshObservations(){return Promise.all([this.loadUserObservations(),this.loadPublicObservations()]).then(()=>{})}};o.\u0275fac=function(e){return new(e||o)(b(g),b(f))},o.\u0275prov=v({token:o,factory:o.\u0275fac,providedIn:"root"});let l=o;return l})();export{m as a};
