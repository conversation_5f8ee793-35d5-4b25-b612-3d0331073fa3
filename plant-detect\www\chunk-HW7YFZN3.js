import{C as te,G as ne,H as ie,J as oe,N as re,P as ae,a as $,h as q,k as J,q as Q,s as W,v as X,x as Z,y as ee}from"./chunk-AWIL3PGF.js";import{b as K}from"./chunk-UUEAGYFQ.js";import"./chunk-N2M46O22.js";import{C as s,D as C,E as M,F as v,Fa as k,G as c,I as e,J as t,Ja as I,K as l,Ka as T,L as O,La as V,M as g,N as m,O as n,Qa as A,Ua as F,V as S,Wa as U,Xa as L,Ya as N,bb as z,ca as b,ga as y,hb as D,ib as R,mb as j,oa as E,ob as B,qb as G,sa as w,sb as Y,tb as H,v as p,w as d}from"./chunk-AZEIYKMX.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{a as P,b as x,g as f}from"./chunk-OLRFWS6T.js";var le=()=>["/test-config"];function ce(u,_){if(u&1){let o=O();e(0,"ion-card",18)(1,"ion-card-content")(2,"h3",8),l(3,"ion-icon",19),n(4," Preferences "),t(),e(5,"ion-list",20)(6,"ion-item"),l(7,"ion-icon",21),e(8,"ion-label")(9,"h3"),n(10,"Default Flora Region"),t(),e(11,"p"),n(12,"Choose your preferred flora region for identification"),t()(),e(13,"ion-select",22),g("ionChange",function(r){p(o);let i=m();return d(i.updatePreference("defaultFlora",r.detail.value))}),e(14,"ion-select-option",23),n(15,"Global Flora"),t(),e(16,"ion-select-option",24),n(17,"Asian Flora"),t(),e(18,"ion-select-option",25),n(19,"European Flora"),t(),e(20,"ion-select-option",26),n(21,"North American Flora"),t(),e(22,"ion-select-option",27),n(23,"South American Flora"),t(),e(24,"ion-select-option",28),n(25,"African Flora"),t(),e(26,"ion-select-option",29),n(27,"Oceanian Flora"),t()()(),e(28,"ion-item"),l(29,"ion-icon",30),e(30,"ion-label")(31,"h3"),n(32,"Theme"),t(),e(33,"p"),n(34,"Choose your preferred app theme"),t()(),e(35,"ion-select",31),g("ionChange",function(r){p(o);let i=m();return d(i.updatePreference("theme",r.detail.value))}),e(36,"ion-select-option",32),n(37,"Light"),t(),e(38,"ion-select-option",33),n(39,"Dark"),t(),e(40,"ion-select-option",34),n(41,"Auto (System)"),t()()(),e(42,"ion-item"),l(43,"ion-icon",35),e(44,"ion-label")(45,"h3"),n(46,"Language"),t(),e(47,"p"),n(48,"Choose your preferred language"),t()(),e(49,"ion-select",36),g("ionChange",function(r){p(o);let i=m();return d(i.updatePreference("language",r.detail.value))}),e(50,"ion-select-option",37),n(51,"English"),t(),e(52,"ion-select-option",38),n(53,"Espa\xF1ol"),t(),e(54,"ion-select-option",39),n(55,"Fran\xE7ais"),t(),e(56,"ion-select-option",40),n(57,"Deutsch"),t(),e(58,"ion-select-option",41),n(59,"Italiano"),t(),e(60,"ion-select-option",42),n(61,"Portugu\xEAs"),t()()(),e(62,"ion-item"),l(63,"ion-icon",43),e(64,"ion-label")(65,"h3"),n(66,"Units"),t(),e(67,"p"),n(68,"Choose your preferred measurement units"),t()(),e(69,"ion-select",44),g("ionChange",function(r){p(o);let i=m();return d(i.updatePreference("units",r.detail.value))}),e(70,"ion-select-option",45),n(71,"Metric"),t(),e(72,"ion-select-option",46),n(73,"Imperial"),t()()(),e(74,"ion-item"),l(75,"ion-icon",47),e(76,"ion-label")(77,"h3"),n(78,"Public Profile"),t(),e(79,"p"),n(80,"Make your observations visible to other users"),t()(),e(81,"ion-toggle",48),g("ionChange",function(r){p(o);let i=m();return d(i.updatePreference("isPublic",r.detail.checked))}),t()()()()()}if(u&2){let o=m();s(13),c("value",o.preferences.defaultFlora),s(22),c("value",o.preferences.theme),s(14),c("value",o.preferences.language),s(20),c("value",o.preferences.units),s(12),c("checked",o.preferences.isPublic)}}function se(u,_){if(u&1){let o=O();e(0,"ion-card",49)(1,"ion-card-content")(2,"h3",8),l(3,"ion-icon",50),n(4," Notifications "),t(),e(5,"ion-list",51)(6,"ion-item")(7,"ion-label")(8,"h3"),n(9,"New Species Alerts"),t(),e(10,"p"),n(11,"Get notified when new species are added to the database"),t()(),e(12,"ion-toggle",48),g("ionChange",function(r){p(o);let i=m();return d(i.updateNotificationSetting("newSpecies",r.detail.checked))}),t()(),e(13,"ion-item")(14,"ion-label")(15,"h3"),n(16,"Community Updates"),t(),e(17,"p"),n(18,"Receive updates about community activities and features"),t()(),e(19,"ion-toggle",48),g("ionChange",function(r){p(o);let i=m();return d(i.updateNotificationSetting("communityUpdates",r.detail.checked))}),t()(),e(20,"ion-item")(21,"ion-label")(22,"h3"),n(23,"Observation Comments"),t(),e(24,"p"),n(25,"Get notified when someone comments on your observations"),t()(),e(26,"ion-toggle",48),g("ionChange",function(r){p(o);let i=m();return d(i.updateNotificationSetting("observationComments",r.detail.checked))}),t()(),e(27,"ion-item")(28,"ion-label")(29,"h3"),n(30,"System Updates"),t(),e(31,"p"),n(32,"Receive important app updates and maintenance notifications"),t()(),e(33,"ion-toggle",48),g("ionChange",function(r){p(o);let i=m();return d(i.updateNotificationSetting("systemUpdates",r.detail.checked))}),t()()()()()}if(u&2){let o=m();s(12),c("checked",(o.preferences==null||o.preferences.notifications==null?null:o.preferences.notifications.newSpecies)||!1),s(7),c("checked",(o.preferences==null||o.preferences.notifications==null?null:o.preferences.notifications.communityUpdates)||!1),s(7),c("checked",(o.preferences==null||o.preferences.notifications==null?null:o.preferences.notifications.observationComments)||!1),s(7),c("checked",(o.preferences==null||o.preferences.notifications==null?null:o.preferences.notifications.systemUpdates)||!1)}}var xe=(()=>{let _=class _{constructor(a,r,i){this.authService=a,this.toastController=r,this.alertController=i,this.user=null,this.preferences=null,$({construct:J,settings:re,chevronForward:q,notifications:ie,globe:Q,moon:ne,language:Z,shield:ae,help:W,information:X,logOut:te,person:oe,leaf:ee})}ngOnInit(){this.loadUserData()}loadUserData(){this.authService.user$.subscribe(a=>{this.user=a,this.preferences=(a==null?void 0:a.preferences)||null})}updatePreference(a,r){return f(this,null,function*(){if(!(!this.user||!this.preferences))try{let i=x(P({},this.preferences),{[a]:r});yield this.authService.updateUserProfile({preferences:i}),yield(yield this.toastController.create({message:"Settings updated successfully",duration:2e3,position:"bottom",color:"success"})).present()}catch(i){yield(yield this.toastController.create({message:"Failed to update settings: "+i.message,duration:3e3,position:"bottom",color:"danger"})).present()}})}updateNotificationSetting(a,r){return f(this,null,function*(){if(!this.user||!this.preferences)return;let i=x(P({},this.preferences.notifications),{[a]:r});yield this.updatePreference("notifications",i)})}confirmSignOut(){return f(this,null,function*(){yield(yield this.alertController.create({header:"Sign Out",message:"Are you sure you want to sign out?",buttons:[{text:"Cancel",role:"cancel"},{text:"Sign Out",role:"destructive",handler:()=>{this.authService.signOut()}}]})).present()})}showAbout(){return f(this,null,function*(){yield(yield this.alertController.create({header:"About Plant Detect",message:`
        <p><strong>Version:</strong> 1.0.0</p>
        <p><strong>Powered by:</strong> PlantNet API</p>
        <p><strong>Built with:</strong> Ionic & Angular</p>
        <br>
        <p>Plant Detect helps you identify plants using AI-powered image recognition. Take a photo of any plant and get instant identification results with detailed information about the species.</p>
      `,buttons:["OK"]})).present()})}};_.\u0275fac=function(r){return new(r||_)(C(K),C(B),C(j))},_.\u0275cmp=M({type:_,selectors:[["app-settings"]],decls:40,vars:6,consts:[[3,"translucent"],["slot","start"],["defaultHref","/tabs/profile"],[3,"fullscreen"],[1,"settings-content"],["class","preferences-card",4,"ngIf"],["class","notifications-card",4,"ngIf"],[1,"app-settings-card"],[1,"card-title"],["name","settings","color","primary"],[1,"app-settings-list"],["button","",3,"routerLink"],["name","construct","slot","start","color","primary"],["name","chevron-forward","slot","end","color","medium"],["button","",3,"click"],["name","information","slot","start","color","tertiary"],["button","",1,"sign-out-item",3,"click"],["name","log-out","slot","start","color","danger"],[1,"preferences-card"],["name","person","color","primary"],[1,"preferences-list"],["name","leaf","slot","start","color","secondary"],["interface","popover","placeholder","Select Flora",3,"ionChange","value"],["value","global"],["value","asia"],["value","europe"],["value","north-america"],["value","south-america"],["value","africa"],["value","oceania"],["name","moon","slot","start","color","tertiary"],["interface","popover","placeholder","Select Theme",3,"ionChange","value"],["value","light"],["value","dark"],["value","auto"],["name","language","slot","start","color","warning"],["interface","popover","placeholder","Select Language",3,"ionChange","value"],["value","en"],["value","es"],["value","fr"],["value","de"],["value","it"],["value","pt"],["name","globe","slot","start","color","success"],["interface","popover","placeholder","Select Units",3,"ionChange","value"],["value","metric"],["value","imperial"],["name","shield","slot","start","color","medium"],[3,"ionChange","checked"],[1,"notifications-card"],["name","notifications","color","primary"],[1,"notifications-list"]],template:function(r,i){r&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1),l(3,"ion-back-button",2),t(),e(4,"ion-title"),n(5,"Settings"),t()()(),e(6,"ion-content",3)(7,"div",4),v(8,ce,82,5,"ion-card",5)(9,se,34,4,"ion-card",6),e(10,"ion-card",7)(11,"ion-card-content")(12,"h3",8),l(13,"ion-icon",9),n(14," App Settings "),t(),e(15,"ion-list",10)(16,"ion-item",11),l(17,"ion-icon",12),e(18,"ion-label")(19,"h3"),n(20,"Test Configuration"),t(),e(21,"p"),n(22,"Test Firebase and PlantNet API connections"),t()(),l(23,"ion-icon",13),t(),e(24,"ion-item",14),g("click",function(){return i.showAbout()}),l(25,"ion-icon",15),e(26,"ion-label")(27,"h3"),n(28,"About"),t(),e(29,"p"),n(30,"App version and information"),t()(),l(31,"ion-icon",13),t(),e(32,"ion-item",16),g("click",function(){return i.confirmSignOut()}),l(33,"ion-icon",17),e(34,"ion-label")(35,"h3"),n(36,"Sign Out"),t(),e(37,"p"),n(38,"Sign out of your account"),t()(),l(39,"ion-icon",13),t()()()()()()),r&2&&(c("translucent",!0),s(6),c("fullscreen",!0),s(2),c("ngIf",i.preferences),s(),c("ngIf",i.preferences==null?null:i.preferences.notifications),s(7),c("routerLink",S(5,le)))},dependencies:[y,b,w,E,A,F,D,R,T,V,G,N,U,L,H,Y,z,I,k],styles:[".settings-content[_ngcontent-%COMP%]{padding:16px 16px 100px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInLeft{0%{opacity:0;transform:translate(-30px)}to{opacity:1;transform:translate(0)}}.preferences-card[_ngcontent-%COMP%], .notifications-card[_ngcontent-%COMP%], .app-settings-card[_ngcontent-%COMP%]{border-radius:16px;margin-bottom:20px;box-shadow:0 4px 20px #********;transition:all .3s ease}.preferences-card[_ngcontent-%COMP%]:hover, .notifications-card[_ngcontent-%COMP%]:hover, .app-settings-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 32px #0000001f}.preferences-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInLeft .6s ease-out .1s both}.notifications-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInLeft .6s ease-out .2s both}.app-settings-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInLeft .6s ease-out .3s both}.card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 20px;font-size:1.2rem;font-weight:600;color:var(--ion-color-dark)}.card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1.3rem}.preferences-list[_ngcontent-%COMP%], .notifications-list[_ngcontent-%COMP%], .app-settings-list[_ngcontent-%COMP%]{background:transparent}.preferences-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%], .notifications-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%], .app-settings-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background: var(--ion-color-light);--border-radius: 12px;--padding-start: 16px;--padding-end: 16px;margin-bottom:12px;transition:all .3s ease}.preferences-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover, .notifications-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover, .app-settings-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover{--background: var(--ion-color-light-tint);transform:translate(4px)}.preferences-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:last-child, .notifications-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:last-child, .app-settings-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.preferences-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .notifications-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .app-settings-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-weight:600;font-size:1rem;color:var(--ion-text-color)}.preferences-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .notifications-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .app-settings-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.85rem;color:var(--ion-color-medium);line-height:1.4}.preferences-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[slot=start][_ngcontent-%COMP%], .notifications-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[slot=start][_ngcontent-%COMP%], .app-settings-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[slot=start][_ngcontent-%COMP%]{margin-right:12px;font-size:1.2rem}.preferences-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[slot=end][_ngcontent-%COMP%], .notifications-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[slot=end][_ngcontent-%COMP%], .app-settings-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[slot=end][_ngcontent-%COMP%]{font-size:1rem}.sign-out-item[_ngcontent-%COMP%]{--background: rgba(var(--ion-color-danger-rgb), .05) !important}.sign-out-item[_ngcontent-%COMP%]:hover{--background: rgba(var(--ion-color-danger-rgb), .1) !important}.sign-out-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-danger)!important}ion-toggle[_ngcontent-%COMP%]{--background: var(--ion-color-light-shade);--background-checked: var(--ion-color-primary);--handle-background: white;--handle-background-checked: white;--handle-box-shadow: 0 2px 8px rgba(0, 0, 0, .15)}ion-toggle.toggle-checked[_ngcontent-%COMP%]{--background-checked: var(--ion-color-primary)}ion-select[_ngcontent-%COMP%]{--background: var(--ion-color-light);--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;--padding-top: 8px;--padding-bottom: 8px;font-weight:500}ion-select[_ngcontent-%COMP%]::part(text){color:var(--ion-color-primary)}ion-select[_ngcontent-%COMP%]::part(icon){color:var(--ion-color-primary)}ion-item[_ngcontent-%COMP%]:focus-within{--background: var(--ion-color-light-tint);outline:2px solid var(--ion-color-primary);outline-offset:2px;border-radius:12px}ion-toggle[_ngcontent-%COMP%]:focus-visible{outline:2px solid var(--ion-color-primary);outline-offset:4px;border-radius:16px}ion-select[_ngcontent-%COMP%]:focus-visible{outline:2px solid var(--ion-color-primary);outline-offset:2px;border-radius:8px}@media (max-width: 768px){.settings-content[_ngcontent-%COMP%]{padding:12px}.card-title[_ngcontent-%COMP%]{font-size:1.1rem}ion-item[_ngcontent-%COMP%]{--padding-start: 12px;--padding-end: 12px}ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:.95rem}ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.8rem}}@media (prefers-color-scheme: dark){.preferences-card[_ngcontent-%COMP%], .notifications-card[_ngcontent-%COMP%], .app-settings-card[_ngcontent-%COMP%]{background:var(--ion-color-dark);box-shadow:0 4px 20px #0000004d}ion-item[_ngcontent-%COMP%]{--background: var(--ion-color-dark-tint) !important}ion-item[_ngcontent-%COMP%]:hover{--background: var(--ion-color-dark-shade) !important}.sign-out-item[_ngcontent-%COMP%]{--background: rgba(var(--ion-color-danger-rgb), .1) !important}.sign-out-item[_ngcontent-%COMP%]:hover{--background: rgba(var(--ion-color-danger-rgb), .15) !important}ion-toggle[_ngcontent-%COMP%]{--background: var(--ion-color-dark-shade)}ion-select[_ngcontent-%COMP%]{--background: var(--ion-color-dark-tint)}}.settings-loading[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:200px}.settings-loading[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{--color: var(--ion-color-primary)}.settings-error[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;color:var(--ion-color-danger)}.settings-error[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:16px}.settings-error[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-weight:600}.settings-error[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 20px;color:var(--ion-color-medium)}"]});let u=_;return u})();export{xe as SettingsPage};
