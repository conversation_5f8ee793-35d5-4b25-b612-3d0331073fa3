[debug] [2025-05-29T15:16:40.025Z] ----------------------------------------------------------------------
[debug] [2025-05-29T15:16:40.026Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --debug
[debug] [2025-05-29T15:16:40.027Z] CLI Version:   14.4.0
[debug] [2025-05-29T15:16:40.027Z] Platform:      win32
[debug] [2025-05-29T15:16:40.027Z] Node Version:  v22.14.0
[debug] [2025-05-29T15:16:40.027Z] Time:          Thu May 29 2025 20:46:40 GMT+0530 (India Standard Time)
[debug] [2025-05-29T15:16:40.027Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-29T15:16:40.159Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-29T15:16:40.160Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-29T15:16:40.161Z] [iam] checking project plant-detect-edaf6 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update","firebaserules.releases.create","firebaserules.releases.update","firebaserules.rulesets.create"]
[debug] [2025-05-29T15:16:40.162Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:40.162Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:40.163Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions [none]
[debug] [2025-05-29T15:16:40.163Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:16:40.163Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update","firebaserules.releases.create","firebaserules.releases.update","firebaserules.rulesets.create"]}
[debug] [2025-05-29T15:16:41.319Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions 200
[debug] [2025-05-29T15:16:41.319Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get","firebasehosting.sites.update","firebaserules.releases.create","firebaserules.releases.update","firebaserules.rulesets.create"]}
[debug] [2025-05-29T15:16:41.320Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:41.320Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:41.320Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-05-29T15:16:41.320Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-05-29T15:16:42.664Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions 404
[debug] [2025-05-29T15:16:42.664Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions {"error":{"code":404,"message":"Unknown service account","status":"NOT_FOUND"}}
[debug] [2025-05-29T15:16:42.665Z] [functions] service account IAM check errored, deploy may fail: Request to https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions had HTTP Error: 404, Unknown service account {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":404,"message":"Unknown service account","status":"NOT_FOUND"}},"response":{"statusCode":404}},"exit":1,"message":"Request to https://iam.googleapis.com/v1/projects/plant-detect-edaf6/serviceAccounts/<EMAIL>:testIamPermissions had HTTP Error: 404, Unknown service account","status":404}
[debug] [2025-05-29T15:16:42.665Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:42.666Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:42.666Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6 [none]
[debug] [2025-05-29T15:16:43.221Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6 200
[debug] [2025-05-29T15:16:43.222Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6 {"projectId":"plant-detect-edaf6","projectNumber":"*************","displayName":"Plant Detect","name":"projects/plant-detect-edaf6","resources":{"hostingSite":"plant-detect-edaf6"},"state":"ACTIVE","etag":"1_15fbb1a7-d9c5-4998-a469-dbef24d12ffb"}
[info] 
[info] === Deploying to 'plant-detect-edaf6'...
[info] 
[info] i  deploying storage, firestore, functions, hosting 
[info] Running command: npm --prefix "$RESOURCE_DIR" run build
[info] +  functions: Finished running predeploy script. 
[info] i  storage: ensuring required API firebasestorage.googleapis.com is enabled... 
[debug] [2025-05-29T15:16:47.389Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:47.389Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:47.390Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebasestorage.googleapis.com [none]
[debug] [2025-05-29T15:16:47.390Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebasestorage.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:16:48.860Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebasestorage.googleapis.com 200
[debug] [2025-05-29T15:16:48.860Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebasestorage.googleapis.com [omitted]
[info] +  storage: required API firebasestorage.googleapis.com is enabled 
[debug] [2025-05-29T15:16:48.861Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:48.862Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:48.862Z] >>> [apiv2][query] GET https://firebasestorage.googleapis.com/v1alpha/projects/plant-detect-edaf6/defaultBucket [none]
[debug] [2025-05-29T15:16:49.493Z] <<< [apiv2][status] GET https://firebasestorage.googleapis.com/v1alpha/projects/plant-detect-edaf6/defaultBucket 200
[debug] [2025-05-29T15:16:49.494Z] <<< [apiv2][body] GET https://firebasestorage.googleapis.com/v1alpha/projects/plant-detect-edaf6/defaultBucket {"name":"projects/plant-detect-edaf6/defaultBucket","location":"US-CENTRAL1","bucket":{"name":"projects/plant-detect-edaf6/buckets/plant-detect-edaf6.firebasestorage.app"},"storageClass":"REGIONAL"}
[info] i  firebase.storage: checking storage.rules for compilation errors... 
[debug] [2025-05-29T15:16:49.495Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:49.495Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:49.495Z] >>> [apiv2][query] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test [none]
[debug] [2025-05-29T15:16:49.496Z] >>> [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test [omitted]
[debug] [2025-05-29T15:16:50.873Z] <<< [apiv2][status] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test 200
[debug] [2025-05-29T15:16:50.874Z] <<< [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test {}
[info] +  firebase.storage: rules file storage.rules compiled successfully 
[info] i  firestore: reading indexes from firestore.indexes.json... 
[info] i  cloud.firestore: checking firestore.rules for compilation errors... 
[debug] [2025-05-29T15:16:50.875Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:50.875Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:50.875Z] >>> [apiv2][query] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test [none]
[debug] [2025-05-29T15:16:50.875Z] >>> [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test [omitted]
[debug] [2025-05-29T15:16:52.138Z] <<< [apiv2][status] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test 200
[debug] [2025-05-29T15:16:52.139Z] <<< [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6:test {}
[info] +  cloud.firestore: rules file firestore.rules compiled successfully 
[debug] [2025-05-29T15:16:52.142Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:52.143Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:52.143Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com [none]
[debug] [2025-05-29T15:16:52.143Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:16:53.463Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com 200
[debug] [2025-05-29T15:16:53.464Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com [omitted]
[debug] [2025-05-29T15:16:53.464Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:53.464Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:53.464Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 [none]
[debug] [2025-05-29T15:16:54.635Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 200
[debug] [2025-05-29T15:16:54.636Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 {"projectNumber":"*************","projectId":"plant-detect-edaf6","lifecycleState":"ACTIVE","name":"Plant Detect","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-29T12:37:06.209571Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[debug] [2025-05-29T15:16:54.638Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:54.638Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:54.638Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:54.639Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[debug] [2025-05-29T15:16:54.639Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:54.639Z] Checked if tokens are valid: true, expires at: *************
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-05-29T15:16:54.639Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:54.639Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:54.640Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudfunctions.googleapis.com [none]
[debug] [2025-05-29T15:16:54.640Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudfunctions.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:16:54.640Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/runtimeconfig.googleapis.com [none]
[debug] [2025-05-29T15:16:54.640Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/runtimeconfig.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:16:54.642Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudbuild.googleapis.com [none]
[debug] [2025-05-29T15:16:54.642Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudbuild.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:16:54.643Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/artifactregistry.googleapis.com [none]
[debug] [2025-05-29T15:16:54.644Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/artifactregistry.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:16:55.128Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudfunctions.googleapis.com 200
[debug] [2025-05-29T15:16:55.129Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudfunctions.googleapis.com [omitted]
[info] +  functions: required API cloudfunctions.googleapis.com is enabled 
[debug] [2025-05-29T15:16:56.067Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/runtimeconfig.googleapis.com 200
[debug] [2025-05-29T15:16:56.068Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/runtimeconfig.googleapis.com [omitted]
[debug] [2025-05-29T15:16:56.078Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudbuild.googleapis.com 200
[debug] [2025-05-29T15:16:56.079Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudbuild.googleapis.com [omitted]
[info] +  functions: required API cloudbuild.googleapis.com is enabled 
[debug] [2025-05-29T15:16:56.151Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/artifactregistry.googleapis.com 200
[debug] [2025-05-29T15:16:56.152Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/artifactregistry.googleapis.com [omitted]
[info] +  artifactregistry: required API artifactregistry.googleapis.com is enabled 
[debug] [2025-05-29T15:16:56.153Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:56.153Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:56.154Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6/adminSdkConfig [none]
[debug] [2025-05-29T15:16:56.883Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6/adminSdkConfig 200
[debug] [2025-05-29T15:16:56.883Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/plant-detect-edaf6/adminSdkConfig {"projectId":"plant-detect-edaf6","storageBucket":"plant-detect-edaf6.firebasestorage.app"}
[debug] [2025-05-29T15:16:56.884Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:56.884Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:16:56.884Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/plant-detect-edaf6/configs [none]
[debug] [2025-05-29T15:16:57.563Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/plant-detect-edaf6/configs 200
[debug] [2025-05-29T15:16:57.563Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/plant-detect-edaf6/configs {}
[debug] [2025-05-29T15:16:57.564Z] Validating nodejs source
[debug] [2025-05-29T15:16:58.279Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "cors": "^2.8.5",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "form-data": "^4.0.2",
    "node-fetch": "^2.7.0"
  },
  "devDependencies": {
    "@types/node-fetch": "^2.6.12",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^4.9.0"
  },
  "private": true
}
[debug] [2025-05-29T15:16:58.279Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[debug] [2025-05-29T15:16:58.280Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-05-29T15:16:58.289Z] Found firebase-functions binary at 'C:\Users\<USER>\Desktop\plant_detect\plant-detect\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8463

[debug] [2025-05-29T15:16:58.688Z] Got response from /__/functions.yaml {"endpoints":{"healthCheck":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"healthCheck"},"identifyPlant":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"identifyPlant"},"getUserStats":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"getUserStats"},"getGlobalStats":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"getGlobalStats"},"cleanupOldLogs":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"cleanupOldLogs"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] i  extensions: ensuring required API firebaseextensions.googleapis.com is enabled... 
[debug] [2025-05-29T15:17:02.721Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:02.722Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:02.722Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebaseextensions.googleapis.com [none]
[debug] [2025-05-29T15:17:02.722Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebaseextensions.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:17:04.149Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebaseextensions.googleapis.com 200
[debug] [2025-05-29T15:17:04.149Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/firebaseextensions.googleapis.com [omitted]
[info] +  extensions: required API firebaseextensions.googleapis.com is enabled 
[debug] [2025-05-29T15:17:04.150Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-29T15:17:04.150Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-29T15:17:04.151Z] [iam] checking project plant-detect-edaf6 for permissions ["firebase.projects.get","firebaseextensions.instances.list"]
[debug] [2025-05-29T15:17:04.151Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:04.151Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:04.152Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions [none]
[debug] [2025-05-29T15:17:04.152Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:17:04.152Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-05-29T15:17:05.268Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions 200
[debug] [2025-05-29T15:17:05.269Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-05-29T15:17:05.269Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:05.269Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:05.270Z] >>> [apiv2][query] GET https://firebaseextensions.googleapis.com/v1beta/projects/plant-detect-edaf6/instances pageSize=100&pageToken=
[debug] [2025-05-29T15:17:06.610Z] <<< [apiv2][status] GET https://firebaseextensions.googleapis.com/v1beta/projects/plant-detect-edaf6/instances 200
[debug] [2025-05-29T15:17:06.610Z] <<< [apiv2][body] GET https://firebaseextensions.googleapis.com/v1beta/projects/plant-detect-edaf6/instances {}
[info] i  functions: preparing functions directory for uploading... 
[info] i  functions: packaged C:\Users\<USER>\Desktop\plant_detect\plant-detect\functions (73.37 KB) for uploading 
[debug] [2025-05-29T15:17:06.646Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:06.647Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:06.647Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/projects/plant-detect-edaf6/locations/-/functions [none]
[debug] [2025-05-29T15:17:07.459Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/projects/plant-detect-edaf6/locations/-/functions 200
[debug] [2025-05-29T15:17:07.459Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/projects/plant-detect-edaf6/locations/-/functions {}
[debug] [2025-05-29T15:17:07.459Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:07.459Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:07.460Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/plant-detect-edaf6/locations/-/functions filter=environment%3D%22GEN_2%22
[debug] [2025-05-29T15:17:08.971Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/plant-detect-edaf6/locations/-/functions 200
[debug] [2025-05-29T15:17:08.971Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/plant-detect-edaf6/locations/-/functions {"functions":[{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/cleanupOldLogs","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"cleanupOldLogs","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"cleanupOldLogs/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"cleanupOldLogs/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/cleanupoldlogs","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/cleanupOldLogs","FUNCTION_TARGET":"cleanupOldLogs","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://cleanupoldlogs-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"cleanupoldlogs-00007-koy","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:40.561600288Z","labels":{"firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef","deployment-tool":"cli-firebase"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/cleanupOldLogs","createTime":"2025-05-29T13:56:37.755967045Z","satisfiesPzi":true},{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/getGlobalStats","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"getGlobalStats","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"getGlobalStats/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"getGlobalStats/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/getglobalstats","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/getGlobalStats","FUNCTION_TARGET":"getGlobalStats","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://getglobalstats-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"getglobalstats-00007-sil","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:43.539187500Z","labels":{"firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef","deployment-tool":"cli-firebase","deployment-callable":"true"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/getGlobalStats","createTime":"2025-05-29T13:56:37.749685984Z","satisfiesPzi":true},{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/identifyPlant","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"identifyPlant","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"identifyPlant/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"identifyPlant/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/identifyplant","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/identifyPlant","FUNCTION_TARGET":"identifyPlant","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://identifyplant-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"identifyplant-00007-pir","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:40.446036223Z","labels":{"deployment-callable":"true","firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef","deployment-tool":"cli-firebase"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/identifyPlant","createTime":"2025-05-29T13:56:37.621062090Z","satisfiesPzi":true},{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/healthCheck","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"healthCheck","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"healthCheck/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"healthCheck/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/healthcheck","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/healthCheck","FUNCTION_TARGET":"healthCheck","LOG_EXECUTION_ID":"true"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://healthcheck-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"healthcheck-00007-tuw","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:41.273951099Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/healthCheck","createTime":"2025-05-29T13:54:08.670160908Z","satisfiesPzi":true},{"name":"projects/plant-detect-edaf6/locations/us-central1/functions/getUserStats","buildConfig":{"build":"projects/*************/locations/us-central1/builds/bf54e9bb-8e64-4ace-ad89-37666f80b4a0","runtime":"nodejs22","entryPoint":"getUserStats","source":{"storageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"getUserStats/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-*************-us-central1","object":"getUserStats/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/plant-detect-edaf6/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/plant-detect-edaf6/locations/us-central1/services/getuserstats","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"plant-detect-edaf6\",\"storageBucket\":\"plant-detect-edaf6.firebasestorage.app\"}","GCLOUD_PROJECT":"plant-detect-edaf6","EVENTARC_CLOUD_EVENT_SOURCE":"projects/plant-detect-edaf6/locations/us-central1/services/getUserStats","FUNCTION_TARGET":"getUserStats","LOG_EXECUTION_ID":"true","FUNCTION_SIGNATURE_TYPE":"http"},"maxInstanceCount":40,"ingressSettings":"ALLOW_ALL","uri":"https://getuserstats-nmthixu27a-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"getuserstats-00007-cis","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-05-29T14:44:40.294222078Z","labels":{"firebase-functions-hash":"f154fe4d98cf91fcd9f198ca6077d4974b1d02ef","deployment-tool":"cli-firebase","deployment-callable":"true"},"environment":"GEN_2","url":"https://us-central1-plant-detect-edaf6.cloudfunctions.net/getUserStats","createTime":"2025-05-29T13:56:37.817385033Z","satisfiesPzi":true}]}
[info] i  functions: ensuring required API run.googleapis.com is enabled... 
[debug] [2025-05-29T15:17:08.973Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:08.974Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API eventarc.googleapis.com is enabled... 
[debug] [2025-05-29T15:17:08.974Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:08.974Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API pubsub.googleapis.com is enabled... 
[debug] [2025-05-29T15:17:08.974Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:08.974Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API storage.googleapis.com is enabled... 
[debug] [2025-05-29T15:17:08.974Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:08.975Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:08.975Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com [none]
[debug] [2025-05-29T15:17:08.975Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com x-goog-quota-user=projects/*************
[debug] [2025-05-29T15:17:08.975Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/*************/services/eventarc.googleapis.com [none]
[debug] [2025-05-29T15:17:08.975Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/*************/services/eventarc.googleapis.com x-goog-quota-user=projects/*************
[debug] [2025-05-29T15:17:08.977Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/*************/services/pubsub.googleapis.com [none]
[debug] [2025-05-29T15:17:08.977Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/*************/services/pubsub.googleapis.com x-goog-quota-user=projects/*************
[debug] [2025-05-29T15:17:08.978Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/*************/services/storage.googleapis.com [none]
[debug] [2025-05-29T15:17:08.978Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/*************/services/storage.googleapis.com x-goog-quota-user=projects/*************
[debug] [2025-05-29T15:17:10.300Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com 200
[debug] [2025-05-29T15:17:10.300Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/*************/services/run.googleapis.com [omitted]
[info] +  functions: required API run.googleapis.com is enabled 
[debug] [2025-05-29T15:17:10.401Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/*************/services/eventarc.googleapis.com 200
[debug] [2025-05-29T15:17:10.401Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/*************/services/eventarc.googleapis.com [omitted]
[info] +  functions: required API eventarc.googleapis.com is enabled 
[debug] [2025-05-29T15:17:10.424Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/*************/services/storage.googleapis.com 200
[debug] [2025-05-29T15:17:10.424Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/*************/services/storage.googleapis.com [omitted]
[info] +  functions: required API storage.googleapis.com is enabled 
[debug] [2025-05-29T15:17:10.518Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/*************/services/pubsub.googleapis.com 200
[debug] [2025-05-29T15:17:10.519Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/*************/services/pubsub.googleapis.com [omitted]
[info] +  functions: required API pubsub.googleapis.com is enabled 
[info] i  functions: generating the service identity for pubsub.googleapis.com... 
[debug] [2025-05-29T15:17:10.519Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:10.519Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: generating the service identity for eventarc.googleapis.com... 
[debug] [2025-05-29T15:17:10.520Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:10.520Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:10.520Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1beta1/projects/*************/services/pubsub.googleapis.com:generateServiceIdentity [none]
[debug] [2025-05-29T15:17:10.520Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1beta1/projects/*************/services/eventarc.googleapis.com:generateServiceIdentity [none]
[debug] [2025-05-29T15:17:11.003Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1beta1/projects/*************/services/pubsub.googleapis.com:generateServiceIdentity 200
[debug] [2025-05-29T15:17:11.003Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/*************/services/pubsub.googleapis.com:generateServiceIdentity {"name":"operations/finished.DONE_OPERATION","done":true,"response":{"@type":"type.googleapis.com/google.api.serviceusage.v1beta1.ServiceIdentity","email":"<EMAIL>","uniqueId":"105526919494721255565"}}
[debug] [2025-05-29T15:17:12.021Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1beta1/projects/*************/services/eventarc.googleapis.com:generateServiceIdentity 200
[debug] [2025-05-29T15:17:12.021Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/*************/services/eventarc.googleapis.com:generateServiceIdentity {"name":"operations/finished.DONE_OPERATION","done":true,"response":{"@type":"type.googleapis.com/google.api.serviceusage.v1beta1.ServiceIdentity","email":"<EMAIL>","uniqueId":"109283645850072581741"}}
[debug] [2025-05-29T15:17:12.023Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:12.023Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:12.023Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com [none]
[debug] [2025-05-29T15:17:12.023Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com x-goog-quota-user=projects/plant-detect-edaf6
[debug] [2025-05-29T15:17:12.381Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com 200
[debug] [2025-05-29T15:17:12.381Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/plant-detect-edaf6/services/cloudresourcemanager.googleapis.com [omitted]
[debug] [2025-05-29T15:17:12.382Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:12.383Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:12.383Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 [none]
[debug] [2025-05-29T15:17:13.557Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 200
[debug] [2025-05-29T15:17:13.557Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/plant-detect-edaf6 {"projectNumber":"*************","projectId":"plant-detect-edaf6","lifecycleState":"ACTIVE","name":"Plant Detect","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-29T12:37:06.209571Z"}
[debug] [2025-05-29T15:17:13.558Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:13.558Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:13.558Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************* [none]
[debug] [2025-05-29T15:17:13.934Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************* 403
[debug] [2025-05-29T15:17:13.935Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************* {"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************","consumer":"projects/*************","serviceTitle":"Compute Engine API","containerInfo":"*************","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}}
[debug] [2025-05-29T15:17:13.935Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************","consumer":"projects/*************","serviceTitle":"Compute Engine API","containerInfo":"*************","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************* had HTTP Error: 403, Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-05-29T15:17:13.936Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:13.936Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:13.936Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************* [none]
[debug] [2025-05-29T15:17:14.214Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************* 403
[debug] [2025-05-29T15:17:14.215Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************* {"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/*************","service":"compute.googleapis.com","serviceTitle":"Compute Engine API","containerInfo":"*************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}}
[debug] [2025-05-29T15:17:14.215Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/*************","service":"compute.googleapis.com","serviceTitle":"Compute Engine API","containerInfo":"*************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************* had HTTP Error: 403, Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-05-29T15:17:14.216Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:14.216Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:14.217Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************* [none]
[debug] [2025-05-29T15:17:14.489Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************* 403
[debug] [2025-05-29T15:17:14.490Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************* {"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************","serviceTitle":"Compute Engine API","consumer":"projects/*************","containerInfo":"*************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}}
[debug] [2025-05-29T15:17:14.492Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************","serviceTitle":"Compute Engine API","consumer":"projects/*************","containerInfo":"*************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************* had HTTP Error: 403, Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-05-29T15:17:14.494Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:14.519Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:14.566Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************* [none]
[debug] [2025-05-29T15:17:14.870Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************* 403
[debug] [2025-05-29T15:17:14.870Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************* {"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/*************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************","service":"compute.googleapis.com","containerInfo":"*************","serviceTitle":"Compute Engine API"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}}
[debug] [2025-05-29T15:17:14.871Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/*************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************","service":"compute.googleapis.com","containerInfo":"*************","serviceTitle":"Compute Engine API"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************* had HTTP Error: 403, Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-05-29T15:17:14.871Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:14.872Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:14.872Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************* [none]
[debug] [2025-05-29T15:17:15.148Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************* 403
[debug] [2025-05-29T15:17:15.148Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************* {"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"serviceTitle":"Compute Engine API","consumer":"projects/*************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************","service":"compute.googleapis.com","containerInfo":"*************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}}
[debug] [2025-05-29T15:17:15.148Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"serviceTitle":"Compute Engine API","consumer":"projects/*************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************","service":"compute.googleapis.com","containerInfo":"*************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=*************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************* had HTTP Error: 403, Compute Engine API has not been used in project ************* before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************* then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-05-29T15:17:15.151Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:15.151Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:15.151Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/versions [none]
[debug] [2025-05-29T15:17:15.151Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/versions {"status":"CREATED","labels":{"deployment-tool":"cli-firebase"}}
[debug] [2025-05-29T15:17:16.114Z] <<< [apiv2][status] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/versions 200
[debug] [2025-05-29T15:17:16.114Z] <<< [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/versions {"name":"projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c","status":"CREATED","config":{},"labels":{"deployment-tool":"cli-firebase"}}
[debug] [2025-05-29T15:17:16.115Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:16.116Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:16.116Z] >>> [apiv2][query] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases pageSize=10&pageToken=
[debug] [2025-05-29T15:17:16.754Z] <<< [apiv2][status] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases 200
[debug] [2025-05-29T15:17:16.754Z] <<< [apiv2][body] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases {"releases":[{"name":"projects/plant-detect-edaf6/releases/cloud.firestore","rulesetName":"projects/plant-detect-edaf6/rulesets/01a8b6d5-0442-4c62-9f28-72dc18f5e6e4","createTime":"2025-05-29T13:04:03.134370Z","updateTime":"2025-05-29T14:26:04.102766Z"},{"name":"projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app","rulesetName":"projects/plant-detect-edaf6/rulesets/d8d50388-2477-4e59-961e-6d82c3f919b5","createTime":"2025-05-29T13:06:48.572864Z","updateTime":"2025-05-29T13:54:04.612290Z"}]}
[debug] [2025-05-29T15:17:16.765Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:16.766Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:16.766Z] >>> [apiv2][query] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/rulesets/d8d50388-2477-4e59-961e-6d82c3f919b5 [none]
[debug] [2025-05-29T15:17:18.000Z] <<< [apiv2][status] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/rulesets/d8d50388-2477-4e59-961e-6d82c3f919b5 200
[debug] [2025-05-29T15:17:18.001Z] <<< [apiv2][body] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/rulesets/d8d50388-2477-4e59-961e-6d82c3f919b5 [omitted]
[info] i  storage: latest version of storage.rules already up to date, skipping upload... 
[debug] [2025-05-29T15:17:18.002Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:18.002Z] Checked if tokens are valid: true, expires at: *************
[info] i  firestore: deploying indexes... 
[debug] [2025-05-29T15:17:18.003Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:18.003Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:18.003Z] >>> [apiv2][query] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases pageSize=10&pageToken=
[debug] [2025-05-29T15:17:18.004Z] >>> [apiv2][query] GET https://firestore.googleapis.com/v1/projects/plant-detect-edaf6/databases/(default)/collectionGroups/-/indexes [none]
[debug] [2025-05-29T15:17:19.867Z] <<< [apiv2][status] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases 200
[debug] [2025-05-29T15:17:19.867Z] <<< [apiv2][body] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases {"releases":[{"name":"projects/plant-detect-edaf6/releases/cloud.firestore","rulesetName":"projects/plant-detect-edaf6/rulesets/01a8b6d5-0442-4c62-9f28-72dc18f5e6e4","createTime":"2025-05-29T13:04:03.134370Z","updateTime":"2025-05-29T14:26:04.102766Z"},{"name":"projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app","rulesetName":"projects/plant-detect-edaf6/rulesets/d8d50388-2477-4e59-961e-6d82c3f919b5","createTime":"2025-05-29T13:06:48.572864Z","updateTime":"2025-05-29T13:54:04.612290Z"}]}
[debug] [2025-05-29T15:17:19.868Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:19.868Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:19.868Z] >>> [apiv2][query] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/rulesets/01a8b6d5-0442-4c62-9f28-72dc18f5e6e4 [none]
[debug] [2025-05-29T15:17:19.970Z] <<< [apiv2][status] GET https://firestore.googleapis.com/v1/projects/plant-detect-edaf6/databases/(default)/collectionGroups/-/indexes 200
[debug] [2025-05-29T15:17:19.971Z] <<< [apiv2][body] GET https://firestore.googleapis.com/v1/projects/plant-detect-edaf6/databases/(default)/collectionGroups/-/indexes {"indexes":[{"name":"projects/plant-detect-edaf6/databases/(default)/collectionGroups/observations/indexes/CICAgJim14AK","queryScope":"COLLECTION","fields":[{"fieldPath":"floraRegion","order":"ASCENDING"},{"fieldPath":"isPublic","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"},{"fieldPath":"__name__","order":"DESCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/plant-detect-edaf6/databases/(default)/collectionGroups/observations/indexes/CICAgOjXh4EK","queryScope":"COLLECTION","fields":[{"fieldPath":"isPublic","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"},{"fieldPath":"__name__","order":"DESCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/plant-detect-edaf6/databases/(default)/collectionGroups/observations/indexes/CICAgJj7z4EK","queryScope":"COLLECTION","fields":[{"fieldPath":"species.confidence","order":"DESCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"},{"fieldPath":"__name__","order":"DESCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/plant-detect-edaf6/databases/(default)/collectionGroups/observations/indexes/CICAgJjF9oIK","queryScope":"COLLECTION","fields":[{"fieldPath":"plantPart","order":"ASCENDING"},{"fieldPath":"isPublic","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"},{"fieldPath":"__name__","order":"DESCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/plant-detect-edaf6/databases/(default)/collectionGroups/observations/indexes/CICAgJiUpoMK","queryScope":"COLLECTION","fields":[{"fieldPath":"species.scientificName","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"},{"fieldPath":"__name__","order":"DESCENDING"}],"state":"READY","density":"SPARSE_ALL"},{"name":"projects/plant-detect-edaf6/databases/(default)/collectionGroups/observations/indexes/CICAgOi3kJAK","queryScope":"COLLECTION","fields":[{"fieldPath":"userId","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"},{"fieldPath":"__name__","order":"DESCENDING"}],"state":"READY","density":"SPARSE_ALL"}]}
[debug] [2025-05-29T15:17:19.971Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:19.971Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:19.972Z] >>> [apiv2][query] GET https://firestore.googleapis.com/v1/projects/plant-detect-edaf6/databases/(default)/collectionGroups/-/fields?filter=indexConfig.usesAncestorConfig=false OR ttlConfig:* [none]
[debug] [2025-05-29T15:17:20.286Z] <<< [apiv2][status] GET https://firestore.googleapis.com/v1/projects/plant-detect-edaf6/databases/(default)/collectionGroups/-/fields?filter=indexConfig.usesAncestorConfig=false OR ttlConfig:* 200
[debug] [2025-05-29T15:17:20.286Z] <<< [apiv2][body] GET https://firestore.googleapis.com/v1/projects/plant-detect-edaf6/databases/(default)/collectionGroups/-/fields?filter=indexConfig.usesAncestorConfig=false OR ttlConfig:* {"fields":[{"name":"projects/plant-detect-edaf6/databases/(default)/collectionGroups/__default__/fields/*","indexConfig":{"indexes":[{"queryScope":"COLLECTION","fields":[{"fieldPath":"*","order":"ASCENDING"}],"state":"READY"},{"queryScope":"COLLECTION","fields":[{"fieldPath":"*","order":"DESCENDING"}],"state":"READY"},{"queryScope":"COLLECTION","fields":[{"fieldPath":"*","arrayConfig":"CONTAINS"}],"state":"READY"}]}}]}
[debug] [2025-05-29T15:17:20.287Z] Skipping existing index: {"collectionGroup":"observations","queryScope":"COLLECTION","fields":[{"fieldPath":"isPublic","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"}]}
[debug] [2025-05-29T15:17:20.287Z] Skipping existing index: {"collectionGroup":"observations","queryScope":"COLLECTION","fields":[{"fieldPath":"userId","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"}]}
[debug] [2025-05-29T15:17:20.287Z] Skipping existing index: {"collectionGroup":"observations","queryScope":"COLLECTION","fields":[{"fieldPath":"species.scientificName","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"}]}
[debug] [2025-05-29T15:17:20.288Z] Skipping existing index: {"collectionGroup":"observations","queryScope":"COLLECTION","fields":[{"fieldPath":"floraRegion","order":"ASCENDING"},{"fieldPath":"isPublic","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"}]}
[debug] [2025-05-29T15:17:20.288Z] Skipping existing index: {"collectionGroup":"observations","queryScope":"COLLECTION","fields":[{"fieldPath":"plantPart","order":"ASCENDING"},{"fieldPath":"isPublic","order":"ASCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"}]}
[debug] [2025-05-29T15:17:20.288Z] Skipping existing index: {"collectionGroup":"observations","queryScope":"COLLECTION","fields":[{"fieldPath":"species.confidence","order":"DESCENDING"},{"fieldPath":"createdAt","order":"DESCENDING"}]}
[info] +  firestore: deployed indexes in firestore.indexes.json successfully for (default) database 
[debug] [2025-05-29T15:17:21.098Z] <<< [apiv2][status] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/rulesets/01a8b6d5-0442-4c62-9f28-72dc18f5e6e4 200
[debug] [2025-05-29T15:17:21.098Z] <<< [apiv2][body] GET https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/rulesets/01a8b6d5-0442-4c62-9f28-72dc18f5e6e4 [omitted]
[info] i  firestore: latest version of firestore.rules already up to date, skipping upload... 
[debug] [2025-05-29T15:17:21.099Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:21.100Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:21.100Z] >>> [apiv2][query] GET https://cloudbilling.googleapis.com/v1/projects/plant-detect-edaf6/billingInfo [none]
[debug] [2025-05-29T15:17:22.503Z] <<< [apiv2][status] GET https://cloudbilling.googleapis.com/v1/projects/plant-detect-edaf6/billingInfo 200
[debug] [2025-05-29T15:17:22.503Z] <<< [apiv2][body] GET https://cloudbilling.googleapis.com/v1/projects/plant-detect-edaf6/billingInfo {"name":"projects/plant-detect-edaf6/billingInfo","projectId":"plant-detect-edaf6","billingAccountName":"billingAccounts/016C76-EB0573-5501C9","billingEnabled":true}
[info] i  hosting[plant-detect-edaf6]: beginning deploy... 
[info] i  hosting[plant-detect-edaf6]: found 56 files in www 
[debug] [2025-05-29T15:17:22.519Z] [hosting] uploading with 200 concurrency
[debug] [2025-05-29T15:17:22.617Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:22.617Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:22.619Z] [hosting] hash cache [d3d3] stored for 56 files
[debug] [2025-05-29T15:17:22.619Z] [hosting][hash queue][FINAL] {"max":86,"min":0,"avg":29.5,"active":0,"complete":56,"success":56,"errored":0,"retried":0,"total":56,"elapsed":99}
[debug] [2025-05-29T15:17:22.620Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c:populateFiles [none]
[debug] [2025-05-29T15:17:22.620Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c:populateFiles {"files":{"/prerendered-routes.json":"acea2ed63a273ab4ca21a5071e93c8246d013bca031410b2d34087a30f99602d","/main-ALWVTITI.js":"63946a8117671518a2e44c476f72b303874955ffae17d3410270c828b4ad49ac","/chunk-YXUQHN6G.js":"e14b0f7b6fbc5f214d8a1cdb452ead574b9d57a5e644aadd67e91e825c597591","/chunk-YIYQPFUL.js":"2df6f5d8410d92046f5cfbc412684325a17afea54a98b3d5325fce7dc175b264","/assets/shapes.svg":"19675ac6d01dd73ff8f6e795559e36d66708960e093f65af7fe1d91a118a7e8d","/assets/images/default-avatar.svg":"5d6fd29ffb4754a75b87df4096711680b82386b495d623cd4bb9aeebe9a52618","/assets/icon/favicon.png":"1da1aecb40e0a38e4d9dfe33234e0c8b6bf9f183aaebc9ec6a3a2e444e30b426","/index.html":"7f4a61911e90b96eec7aaffc9771c7aa4905bf3286c53ba4bdd98525bd8aa9e4","/chunk-XFCVXJNA.js":"43616a1779d9f7d79673042bc7f8b92acd8b7f14cfe800af543120d9c8834798","/styles-JBYDEUBV.css":"544b88791dca92606baae065c963c1bdf2e6e7010f94e10f24ef240cd34ddf01","/polyfills-RQ52UMDF.js":"9bbcba6efec863289c34fbcaa9f886853909d7aaa436b50b9176376169c396a0","/chunk-WVDB22SI.js":"89ce227c37c64b1ef0ca92b16748b8ee2f9b3152a27b78232370e40c7b2fa15b","/chunk-TMW6BMAF.js":"dad36f97c15f7b0502a76323c63eaba2efbbd8af84be9739ab63ad3fa6f81677","/chunk-SV7S5NYR.js":"f5d5e14864788db2c64ca8b34023c932d415f3f6f24ba354350efd7ee4ee9ba7","/chunk-UUEAGYFQ.js":"cc022aa55487d880f2237e69a721586773dacd2c7c6bb04f0be08cc27ab73c0a","/chunk-RFH4OGVK.js":"9e1ea2b3bf4881504fa1c31d7a066aeaad2a7b799a42d8e3ebf244a1b956dc40","/chunk-SPQUQXZ2.js":"7c10f55c6a857654ee612c2c685baa10783ddd9bee9fbba683f24120603fafa0","/chunk-R7ZYQ4ZY.js":"162d6af64b4b4bfe46e4f98e7a8609fb5d01f21f68628b51d08fe7a09b233aed","/chunk-OLRFWS6T.js":"0fdb79c861b8ecee0111e70cab37377d0ddc2e16165871774dc9d9e0e60012b5","/chunk-NWDPQBER.js":"30d7647e34a761fa54ee70149406f9bb0a858a579479e6131652ec550e6a8f93","/chunk-SUPRBCLV.js":"90f1e3da1c7761743943688a7f9c499fb045c78047f1eb3a31605e2523f30730","/chunk-NWJQQAMZ.js":"577d0ac9d9cc0a58ce541e0a609c3467497bc9ff36207bd6c732277eeeb0801a","/chunk-MYOBYLQN.js":"ea1bfda2df49f5889e46f8a4b8ba3d10c0991d5794254c93efe580371076d8e5","/chunk-MY5IYA46.js":"822cbe470e3b1a1279e2b13a130b55eaaef0cbcd7071748ddbed5d6ee76e271f","/chunk-MMQ4D4FU.js":"c9166853dce4956b6bbb081ac824032a41d303182eec260441249a4e226352ad","/chunk-QNQAE6SO.js":"378ec35f8ae0b23cd19df6c8ec0439d381f200115c09ea889a87f3280aae5ef4","/chunk-L43MBJFK.js":"c45b7aa40ad6601394227dd113e43a3dd9032fa576f25e2cccc42977e295b9a5","/chunk-KFDP7KL5.js":"a59c1b488d77c382765f2f17f5e0d7f2ae57b0664c5e41da1170b9a8699d8d74","/chunk-JHLW7V75.js":"78a1dae74995c12cc9205b58819ef061e51a7464e35ed012becb2e11654b102e","/chunk-JWIEPCRG.js":"418bc27a7e644b0bbe0474e838e5f81a112f4c16dbe02ceebc799aa3c4cde825","/chunk-JBBAVVC3.js":"02b34e2a524f1fb45c2ad8759cfe827e6d6e5b4f8ea4ff2958bcd656421e2096","/chunk-HW7YFZN3.js":"58e5dc7c76e5f4f9f8c29544b549e5686a72e0e5cc6868e4f7f63bf8c661ab63","/chunk-G2SCEH7O.js":"d343242bf03336279a6368aff4d56d1b5ea35433ed88314d8e6c9e34a30864be","/chunk-FXDVSEPU.js":"ce9f3626adbc18f97152cd84e5b0e59a0bb28ae2f6d5fa1eb9943ff583542465","/chunk-F33KLCOC.js":"748b36746cfe62166c452fc75df3f5847e1448bc86ce2ff4820af14d05d29137","/chunk-FTLYPE6R.js":"04347b91ed0548c6d8bd488fd3b0f4a43b5bbb848c1a53ed15f1b050f5c6f838","/chunk-DQNEVMNF.js":"69d7915f69294470318daeb3d2159792a1f73dce7ec59e6431b722fd23597989","/chunk-D4VGNZLR.js":"c472fd85d56790c00346e4b70d154638040e372e0a71fba64917b831e956f3e1","/chunk-D6ZGN5FU.js":"e46ced408a8a79ece38e1dbac0affc194b2cbc3a98f8123591be44f3bead791c","/chunk-BFLQRJO7.js":"2ff5cc775b13c0cdbcb7a918994569f85e378d593fa5f643e24b47de836ac0f3","/chunk-JZIOZHPP.js":"ce9712fa8ca2134d260ca1943f5e0f358c185b034ebf77cf68b16e08e24a89a6","/chunk-BV5MA6WE.js":"89e0bf2420011d2270efd4af082d349dbec75712bc10f81b6f9fccf2ef12b2ce","/chunk-C5RQ2IC2.js":"f4c1595b55422864c1af07dffb0dcbe532817558e5790460639bed2798af9da2","/chunk-AMBWI5JL.js":"ded4ea763dc253a0b1b8394ab9d02840f777910637b5ef5fa301320b7980a74b","/chunk-6IVW3HKZ.js":"13f028b0f5fd7a0a8c7ac6ea313755394bca6d2aa2c1e251b0166a640383188a","/chunk-6DBQW3S3.js":"4e0e848f8b1ab992e0f356e2ddadc52cc2949dba549c37ed70a1c42231f79dd6","/chunk-6S6VJZAU.js":"e9bcce30ea6318b3710df9afebd983b0f0fe6ec68d11d4cf235b07910dc7f6ca","/chunk-64QR6YHT.js":"91e35a79b0e25adec8232a5bd39b56e4e14ba4782c1af4c5fc19ae8ed1926c34","/chunk-AWIL3PGF.js":"f4f3c060c11e3b45d9145392595c27e84e485ffd0ea88cd28860b2f0699cad63","/chunk-64IB6TUY.js":"10528ba46ffdbbf4522814c02fb24dd4ab83f7efa2e2a9b69a231c77f51f2c0e","/chunk-4U6PRYVA.js":"ae3d48ac5a77c64dd873fbb89226163728bef3b10ccb49b941195564c2495d88","/chunk-2EU4F7L2.js":"febdfa89ed350115ce32a369400b8f6cc158995c6944cf904f5e94d470f878aa","/chunk-4TAC5EVS.js":"855f07153f80640337c348036d7ab6821fb9cdb8b4f1f8bbe46d2ad0031aeebb","/3rdpartylicenses.txt":"2ecf77e68cd847aee6278116f1f2be3c5f1998fce9d6bb135edfb5e3ab8fb45e","/chunk-N2M46O22.js":"9e042bc873ce567afa912975ee62ab000346ada83f96c290205fbc25daa33fa3","/chunk-AZEIYKMX.js":"e007f3f4a5764ef670db5adbf0e90bc2d02e89fae2e3bb2a7da98c5fca02923b"}}
[debug] [2025-05-29T15:17:23.493Z] <<< [apiv2][status] POST https://firebasehosting.googleapis.com/v1beta1/projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c:populateFiles 200
[debug] [2025-05-29T15:17:23.494Z] <<< [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c:populateFiles {"uploadUrl":"https://upload-firebasehosting.googleapis.com/upload/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c/files"}
[debug] [2025-05-29T15:17:23.495Z] [hosting][populate queue][FINAL] {"max":879,"min":879,"avg":879,"active":0,"complete":1,"success":1,"errored":0,"retried":0,"total":1,"elapsed":879}
[debug] [2025-05-29T15:17:23.496Z] [hosting] uploads queued: 0
[debug] [2025-05-29T15:17:23.497Z] [hosting][upload queue][FINAL] {"max":0,"min":9999999999,"avg":0,"active":0,"complete":0,"success":0,"errored":0,"retried":0,"total":0,"elapsed":1748531843497}
[info] i  hosting: upload complete 
[info] +  hosting[plant-detect-edaf6]: file upload complete 
[debug] [2025-05-29T15:17:23.499Z] [hosting] deploy completed after 993ms
[debug] [2025-05-29T15:17:23.500Z] [rules] releasing firebase.storage/plant-detect-edaf6.firebasestorage.app with ruleset projects/plant-detect-edaf6/rulesets/d8d50388-2477-4e59-961e-6d82c3f919b5
[debug] [2025-05-29T15:17:23.501Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:23.501Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:23.502Z] >>> [apiv2][query] PATCH https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app [none]
[debug] [2025-05-29T15:17:23.502Z] >>> [apiv2][body] PATCH https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app {"release":{"name":"projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app","rulesetName":"projects/plant-detect-edaf6/rulesets/d8d50388-2477-4e59-961e-6d82c3f919b5"}}
[debug] [2025-05-29T15:17:24.770Z] <<< [apiv2][status] PATCH https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app 200
[debug] [2025-05-29T15:17:24.770Z] <<< [apiv2][body] PATCH https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app {"name":"projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app","rulesetName":"projects/plant-detect-edaf6/rulesets/d8d50388-2477-4e59-961e-6d82c3f919b5","createTime":"2025-05-29T13:06:48.572864Z","updateTime":"2025-05-29T15:17:25.781936Z"}
[debug] [2025-05-29T15:17:24.770Z] [rules] updated release projects/plant-detect-edaf6/releases/firebase.storage/plant-detect-edaf6.firebasestorage.app
[info] +  storage: released rules storage.rules to firebase.storage 
[debug] [2025-05-29T15:17:24.771Z] [rules] releasing cloud.firestore/(default) with ruleset projects/plant-detect-edaf6/rulesets/01a8b6d5-0442-4c62-9f28-72dc18f5e6e4
[debug] [2025-05-29T15:17:24.771Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:24.771Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:24.772Z] >>> [apiv2][query] PATCH https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases/cloud.firestore/(default) [none]
[debug] [2025-05-29T15:17:24.772Z] >>> [apiv2][body] PATCH https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases/cloud.firestore/(default) {"release":{"name":"projects/plant-detect-edaf6/releases/cloud.firestore/(default)","rulesetName":"projects/plant-detect-edaf6/rulesets/01a8b6d5-0442-4c62-9f28-72dc18f5e6e4"}}
[debug] [2025-05-29T15:17:26.061Z] <<< [apiv2][status] PATCH https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases/cloud.firestore/(default) 200
[debug] [2025-05-29T15:17:26.063Z] <<< [apiv2][body] PATCH https://firebaserules.googleapis.com/v1/projects/plant-detect-edaf6/releases/cloud.firestore/(default) {"name":"projects/plant-detect-edaf6/releases/cloud.firestore","rulesetName":"projects/plant-detect-edaf6/rulesets/01a8b6d5-0442-4c62-9f28-72dc18f5e6e4","createTime":"2025-05-29T13:04:03.134370Z","updateTime":"2025-05-29T15:17:27.067298Z"}
[debug] [2025-05-29T15:17:26.063Z] [rules] updated release projects/plant-detect-edaf6/releases/cloud.firestore
[info] +  firestore: released rules firestore.rules to cloud.firestore 
[info] i  functions: Skipping the deploy of unchanged functions. 
[info] +  functions[healthCheck(us-central1)] Skipped (No changes detected) 
[info] +  functions[identifyPlant(us-central1)] Skipped (No changes detected) 
[info] +  functions[getUserStats(us-central1)] Skipped (No changes detected) 
[info] +  functions[getGlobalStats(us-central1)] Skipped (No changes detected) 
[info] +  functions[cleanupOldLogs(us-central1)] Skipped (No changes detected) 
[debug] [2025-05-29T15:17:26.073Z] Total Function Deployment time: 1
[debug] [2025-05-29T15:17:26.073Z] 0 Functions Deployed
[debug] [2025-05-29T15:17:26.073Z] 0 Functions Errored
[debug] [2025-05-29T15:17:26.073Z] 0 Function Deployments Aborted
[debug] [2025-05-29T15:17:26.073Z] Average Function Deployment time: NaN
[debug] [2025-05-29T15:17:26.229Z] Not printing URL for HTTPS function. Typically this means it didn't match a filter or we failed deployment
[debug] [2025-05-29T15:17:26.230Z] Not printing URL for HTTPS function. Typically this means it didn't match a filter or we failed deployment
[debug] [2025-05-29T15:17:26.231Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:26.232Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:26.232Z] >>> [apiv2][query] GET https://artifactregistry.googleapis.com/v1/projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts [none]
[debug] [2025-05-29T15:17:26.631Z] <<< [apiv2][status] GET https://artifactregistry.googleapis.com/v1/projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts 200
[debug] [2025-05-29T15:17:26.632Z] <<< [apiv2][body] GET https://artifactregistry.googleapis.com/v1/projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts {"name":"projects/plant-detect-edaf6/locations/us-central1/repositories/gcf-artifacts","format":"DOCKER","description":"This repository is created and used by Cloud Functions for storing function docker images.","labels":{"goog-managed-by":"cloudfunctions"},"createTime":"2025-05-29T13:55:37.095046Z","updateTime":"2025-05-29T14:44:14.142457Z","mode":"STANDARD_REPOSITORY","cleanupPolicies":{"firebase-functions-cleanup":{"id":"firebase-functions-cleanup","action":"DELETE","condition":{"tagState":"ANY","olderThan":"1209600s"}}},"sizeBytes":"179884195","vulnerabilityScanningConfig":{"lastEnableTime":"2025-05-29T13:55:28.152843820Z","enablementState":"SCANNING_DISABLED","enablementStateReason":"API containerscanning.googleapis.com is not enabled."},"satisfiesPzi":true,"registryUri":"us-central1-docker.pkg.dev/plant-detect-edaf6/gcf-artifacts"}
[debug] [2025-05-29T15:17:26.633Z] [
  {
    "config": {
      "public": "www",
      "ignore": [
        "firebase.json",
        "**/.*",
        "**/node_modules/**"
      ],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ],
      "headers": [
        {
          "source": "**/*.@(js|css)",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "max-age=31536000"
            }
          ]
        },
        {
          "source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "max-age=31536000"
            }
          ]
        },
        {
          "source": "/manifest.json",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "max-age=0"
            }
          ]
        }
      ],
      "cleanUrls": true,
      "trailingSlash": false,
      "site": "plant-detect-edaf6"
    },
    "version": "projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c"
  }
]
[info] i  hosting[plant-detect-edaf6]: finalizing version... 
[debug] [2025-05-29T15:17:26.636Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:26.636Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:26.637Z] >>> [apiv2][query] PATCH https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c updateMask=status%2Cconfig
[debug] [2025-05-29T15:17:26.637Z] >>> [apiv2][body] PATCH https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c {"status":"FINALIZED","config":{"rewrites":[{"glob":"**","path":"/index.html"}],"headers":[{"glob":"**/*.@(js|css)","headers":{"Cache-Control":"max-age=31536000"}},{"glob":"**/*.@(jpg|jpeg|gif|png|svg|webp|ico)","headers":{"Cache-Control":"max-age=31536000"}},{"glob":"/manifest.json","headers":{"Cache-Control":"max-age=0"}}],"cleanUrls":true,"trailingSlashBehavior":"REMOVE"}}
[debug] [2025-05-29T15:17:27.132Z] <<< [apiv2][status] PATCH https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c 200
[debug] [2025-05-29T15:17:27.133Z] <<< [apiv2][body] PATCH https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c {"name":"projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c","status":"FINALIZED","config":{"headers":[{"headers":{"Cache-Control":"max-age=31536000"},"glob":"**/*.@(js|css)"},{"headers":{"Cache-Control":"max-age=31536000"},"glob":"**/*.@(jpg|jpeg|gif|png|svg|webp|ico)"},{"headers":{"Cache-Control":"max-age=0"},"glob":"/manifest.json"}],"rewrites":[{"glob":"**","path":"/index.html"}],"cleanUrls":true,"trailingSlashBehavior":"REMOVE"},"labels":{"deployment-tool":"cli-firebase"},"createTime":"2025-05-29T15:17:16.783121Z","createUser":{"email":"<EMAIL>"},"finalizeTime":"2025-05-29T15:17:28.114679Z","finalizeUser":{"email":"<EMAIL>"}}
[debug] [2025-05-29T15:17:27.133Z] [hosting] finalized version for plant-detect-edaf6:[object Object]
[info] +  hosting[plant-detect-edaf6]: version finalized 
[info] i  hosting[plant-detect-edaf6]: releasing new version... 
[debug] [2025-05-29T15:17:27.134Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:27.134Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-29T15:17:27.134Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/channels/live/releases versionName=projects%2F*************%2Fsites%2Fplant-detect-edaf6%2Fversions%2F1beeef7f857cfa7c
[debug] [2025-05-29T15:17:27.134Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/channels/live/releases {}
[debug] [2025-05-29T15:17:27.693Z] <<< [apiv2][status] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/channels/live/releases 200
[debug] [2025-05-29T15:17:27.693Z] <<< [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/plant-detect-edaf6/channels/live/releases {"name":"projects/*************/sites/plant-detect-edaf6/channels/live/releases/1748531848042000","version":{"name":"projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c","status":"FINALIZED","config":{"headers":[{"headers":{"Cache-Control":"max-age=31536000"},"glob":"**/*.@(js|css)"},{"headers":{"Cache-Control":"max-age=31536000"},"glob":"**/*.@(jpg|jpeg|gif|png|svg|webp|ico)"},{"headers":{"Cache-Control":"max-age=0"},"glob":"/manifest.json"}],"rewrites":[{"glob":"**","path":"/index.html"}],"cleanUrls":true,"trailingSlashBehavior":"REMOVE"},"labels":{"deployment-tool":"cli-firebase"},"createTime":"2025-05-29T15:17:16.783121Z","createUser":{"email":"<EMAIL>","imageUrl":"https://lh3.googleusercontent.com/a/ACg8ocLZ10xWsNvZ0ii7G0ZWhxbhoce3WVX3HL6JLYGsqb4UPI8iFN9E"},"finalizeTime":"2025-05-29T15:17:28.114679Z","finalizeUser":{"email":"<EMAIL>","imageUrl":"https://lh3.googleusercontent.com/a/ACg8ocLZ10xWsNvZ0ii7G0ZWhxbhoce3WVX3HL6JLYGsqb4UPI8iFN9E"}},"type":"DEPLOY","releaseTime":"2025-05-29T15:17:28.042Z","releaseUser":{"email":"<EMAIL>","imageUrl":"https://lh3.googleusercontent.com/a/ACg8ocLZ10xWsNvZ0ii7G0ZWhxbhoce3WVX3HL6JLYGsqb4UPI8iFN9E"}}
[debug] [2025-05-29T15:17:27.694Z] [hosting] release: {"name":"projects/*************/sites/plant-detect-edaf6/channels/live/releases/1748531848042000","version":{"name":"projects/*************/sites/plant-detect-edaf6/versions/1beeef7f857cfa7c","status":"FINALIZED","config":{"headers":[{"headers":{"Cache-Control":"max-age=31536000"},"glob":"**/*.@(js|css)"},{"headers":{"Cache-Control":"max-age=31536000"},"glob":"**/*.@(jpg|jpeg|gif|png|svg|webp|ico)"},{"headers":{"Cache-Control":"max-age=0"},"glob":"/manifest.json"}],"rewrites":[{"glob":"**","path":"/index.html"}],"cleanUrls":true,"trailingSlashBehavior":"REMOVE"},"labels":{"deployment-tool":"cli-firebase"},"createTime":"2025-05-29T15:17:16.783121Z","createUser":{"email":"<EMAIL>","imageUrl":"https://lh3.googleusercontent.com/a/ACg8ocLZ10xWsNvZ0ii7G0ZWhxbhoce3WVX3HL6JLYGsqb4UPI8iFN9E"},"finalizeTime":"2025-05-29T15:17:28.114679Z","finalizeUser":{"email":"<EMAIL>","imageUrl":"https://lh3.googleusercontent.com/a/ACg8ocLZ10xWsNvZ0ii7G0ZWhxbhoce3WVX3HL6JLYGsqb4UPI8iFN9E"}},"type":"DEPLOY","releaseTime":"2025-05-29T15:17:28.042Z","releaseUser":{"email":"<EMAIL>","imageUrl":"https://lh3.googleusercontent.com/a/ACg8ocLZ10xWsNvZ0ii7G0ZWhxbhoce3WVX3HL6JLYGsqb4UPI8iFN9E"}}
[info] +  hosting[plant-detect-edaf6]: release complete 
[info] 
[info] +  Deploy complete! 
[info] 
[info] Project Console: https://console.firebase.google.com/project/plant-detect-edaf6/overview
[info] Hosting URL: https://plant-detect-edaf6.web.app
